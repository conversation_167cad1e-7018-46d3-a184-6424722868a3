{
  "$schema": "node_modules/wrangler/config-schema.json",
  "name": "picadabra-test",
  "compatibility_date": "2024-09-23",
  "compatibility_flags": ["nodejs_compat_v2"],
  "account_id": "2da2eb61458c5f71b03602c372d3f703",
  "main": "./src/index.ts",
  "assets": {
    "directory": "./public"
  },

  // Default configuration (test/staging environment)
  "routes": [
    {
      "pattern": "api-test.picadabra.ai",
      "custom_domain": true
    }
  ],
  "vars": {
    "PORT": "3000",
    "R2_PUBLIC_URL": "https://assets.picadabra.ai",
    "FIRESTORE_BUCKET_NAME": "a1d-chat2design-dev.firebasestorage.app",
    "GEMINI_API_KEY": "AIzaSyB0000000000000000000000000000000",
    "ENVIRONMENT": "test",
    "SENTRY_DSN": "https://<EMAIL>/****************"
  },
  "kv_namespaces": [
    {
      "binding": "KV",
      "id": "574be2c8b0d54094a99109ac4b3e0479",
      "preview_id": "574be2c8b0d54094a99109ac4b3e0479"
    },
    {
      "binding": "PUBLIC_JWK_CACHE_KV",
      "id": "574be2c8b0d54094a99109ac4b3e0479",
      "preview_id": "574be2c8b0d54094a99109ac4b3e0479"
    }
  ],
  "r2_buckets": [
    {
      "bucket_name": "picadabra-app",
      "binding": "R2Bucket",
      "preview_bucket_name": "picadabra-app"
    }
  ],
  "workflows": [
    {
      "name": "image-generation-workflow",
      "binding": "IMAGE_GENERATION_WORKFLOW",
      "class_name": "ImageGenerationCloudflareWorkflow"
    },
    {
      "name": "video-generation-workflow",
      "binding": "VIDEO_GENERATION_WORKFLOW",
      "class_name": "VideoGenerationCloudflareWorkflow"
    }
  ],
  "images": {
    "binding": "IMAGES"
  },
  "observability": {
    "enabled": true,
    "head_sampling_rate": 1
  },
  "version_metadata": {
    "binding": "CF_VERSION_METADATA"
  },
  "placement": {
    "mode": "smart"
  },
  // "ai": {
  //   "binding": "AI"
  // }

  // Environment configurations
  "env": {
    "prod": {
      "name": "picadabra-prod",
      "triggers": {
        "crons": ["*/10 * * * *"]
      },
      "routes": [
        {
          "pattern": "api.picadabra.ai",
          "custom_domain": true
        }
      ],
      "vars": {
        "ENVIRONMENT": "prod",
        "SENTRY_DSN": "https://<EMAIL>/****************"
      },
      "kv_namespaces": [
        {
          "binding": "KV",
          "id": "03ef445b0d24494fbe7acc3ff0832aa7"
        }
      ]
    }
  }
}
