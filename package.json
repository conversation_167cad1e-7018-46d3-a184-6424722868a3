{"name": "chat-to-design-backend", "type": "module", "scripts": {"dev": "rm -rf .wrangler && wrangler dev", "build": "vite build", "wrangler": "wrangler", "secrets:push": "wrangler secret bulk put secrets.json", "preview": "pnpm run deploy && wrangler tail picadabra-test", "deploy": "rm -rf .wrangler && wrangler deploy", "deploy:prod": "rm -rf .wrangler && wrangler deploy --env prod", "start": "node dist/index.js --env-file=.env"}, "dependencies": {"@fal-ai/client": "^1.5.0", "@google/generative-ai": "^0.24.0", "@hono/firebase-auth": "^1.4.2", "@hono/node-server": "^1.13.8", "@hono/zod-validator": "^0.4.3", "@notionhq/client": "^3.0.1", "@sentry/cloudflare": "^9.29.0", "@upstash/ratelimit": "^1.0.0", "@upstash/redis": "^1.28.3", "chanfana": "^2.8.0", "firebase-rest-firestore": "^1.2.0", "hono": "^4.7.4", "nanoid": "^5.1.5", "reflect-metadata": "^0.2.2", "sharp": "^0.33.5", "tsyringe": "^4.10.0", "uuid": "^9.0.1", "zod": "^3.24.3"}, "devDependencies": {"@cloudflare/vite-plugin": "^0.1.15", "@cloudflare/workers-types": "^4.20250214.0", "@types/nanoid": "^3.0.0", "@types/node": "^22.13.14", "@types/uuid": "^9.0.8", "@vitest/coverage-v8": "^1.3.1", "esbuild": "^0.25.4", "tsx": "^4.7.1", "typescript": "^5.6.2", "vite": "^6.1.1", "vite-plugin-ssr-hot-reload": "^0.2.2", "vitest": "^3.1.2", "wrangler": "4.13.2"}}