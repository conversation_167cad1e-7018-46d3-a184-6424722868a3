# Firebase Auth 中间件集成指南

## 概述

本项目使用 `@hono/firebase-auth` 中间件来处理 Firebase Authentication。该中间件基于 `firebase-auth-cloudflare-workers` 库，专为 Cloudflare Workers 环境设计。

## 工作原理

### 1. 中间件配置

在 `src/index.ts` 中配置了 Firebase Auth 中间件：

```typescript
import {
  VerifyFirebaseAuthConfig,
  verifyFirebaseAuth,
  getFirebaseToken,
} from "@hono/firebase-auth";

const config: VerifyFirebaseAuthConfig = {
  projectId: "a1d-chat2design",
};

app.use("*", verifyFirebaseAuth(config));
```

### 2. 认证流程

1. **客户端发送请求**：客户端在 HTTP 请求头中包含 Firebase ID Token

   ```
   Authorization: Bearer <firebase-id-token>
   ```

2. **中间件验证**：`verifyFirebaseAuth` 中间件会：

   - 从 `Authorization` 头中提取 JWT token
   - 验证 token 的签名和有效性
   - 检查 token 是否过期
   - 验证 token 的 issuer 和 audience
   - 将解码后的 token 信息存储在 Hono context 中

3. **Handler 访问**：在路由处理器中可以通过 `getFirebaseToken(c)` 获取用户信息

### 3. Token 验证过程

中间件使用以下步骤验证 Firebase ID Token：

1. **JWT 解码**：解析 JWT 的 header、payload 和 signature
2. **签名验证**：使用 Google 的公钥验证 JWT 签名
3. **声明验证**：
   - `aud` (audience): 必须匹配 Firebase 项目 ID
   - `iss` (issuer): 必须是 `https://securetoken.google.com/<project-id>`
   - `exp` (expiration): 检查 token 是否过期
   - `iat` (issued at): 检查 token 签发时间
   - `auth_time`: 检查用户认证时间
   - `sub` (subject): 用户 UID，长度不能超过 128 字符

## 在 Handler 中获取用户信息

### 基本用法

```typescript
import { getFirebaseToken } from "@hono/firebase-auth";

export const myHandler = async (c: Context<HonoEnv>) => {
  // 获取 Firebase token
  const firebaseToken = getFirebaseToken(c);

  if (!firebaseToken) {
    return c.json({ error: "Unauthorized" }, 401);
  }

  // 获取用户 UID
  const uid = firebaseToken.uid;

  return c.json({ uid, email: firebaseToken.email });
};
```

### FirebaseIdToken 接口

`getFirebaseToken(c)` 返回的对象包含以下属性：

```typescript
interface FirebaseIdToken {
  // 用户标识
  uid: string; // 用户唯一 ID
  sub: string; // 同 uid

  // 用户信息
  email?: string; // 用户邮箱
  email_verified?: boolean; // 邮箱是否验证
  phone_number?: string; // 电话号码
  picture?: string; // 头像 URL

  // 认证信息
  auth_time: number; // 用户认证时间（Unix 时间戳）
  iat: number; // Token 签发时间
  exp: number; // Token 过期时间

  // Firebase 特定信息
  firebase: {
    sign_in_provider: string; // 登录提供商 (google.com, password, etc.)
    identities: { [key: string]: any }; // 身份提供商信息
    tenant?: string; // 租户 ID（如果使用多租户）
  };

  // 项目信息
  aud: string; // 受众（项目 ID）
  iss: string; // 签发者

  // 自定义声明
  [key: string]: any; // 其他自定义声明
}
```

## 常见使用场景

### 1. 权限检查

```typescript
export const adminOnlyHandler = async (c: Context<HonoEnv>) => {
  const firebaseToken = getFirebaseToken(c);

  if (!firebaseToken) {
    return c.json({ error: "Unauthorized" }, 401);
  }

  // 检查用户角色（假设在自定义声明中）
  if (firebaseToken.role !== "admin") {
    return c.json({ error: "Forbidden" }, 403);
  }

  // 管理员操作...
};
```

### 2. 用户资源访问控制

```typescript
export const getUserData = async (c: Context<HonoEnv>) => {
  const firebaseToken = getFirebaseToken(c);
  const requestedUserId = c.req.param("userId");

  // 用户只能访问自己的数据
  if (firebaseToken.uid !== requestedUserId) {
    return c.json({ error: "Forbidden" }, 403);
  }

  // 返回用户数据...
};
```

### 3. 在服务层中使用

```typescript
export const createPost = async (c: Context<HonoEnv>) => {
  const firebaseToken = getFirebaseToken(c);
  const { body } = await c.req.json();

  const container = c.get("container");
  const postService = container.resolve<PostService>(IPostService);

  // 将用户 UID 传递给服务层
  const post = await postService.createPost({
    ...body,
    authorId: firebaseToken.uid,
    createdAt: new Date(),
  });

  return c.json(post, 201);
};
```

## 错误处理

中间件会在以下情况抛出错误：

1. **缺少 Authorization 头**：返回 400 Bad Request
2. **无效的 JWT 格式**：返回 401 Unauthorized
3. **JWT 签名验证失败**：返回 401 Unauthorized
4. **JWT 过期**：返回 401 Unauthorized
5. **项目 ID 不匹配**：返回 401 Unauthorized

## 环境配置

### 必需的环境变量

在 `wrangler.toml` 中配置：

```toml
[vars]
# 如果不使用自定义 keyStore，需要配置以下变量
PUBLIC_JWK_CACHE_KEY = "firebase-auth-public-keys"

[[kv_namespaces]]
binding = "PUBLIC_JWK_CACHE_KV"
id = "your-kv-namespace-id"
```

### Firebase 项目配置

确保 Firebase 项目配置正确：

1. 在 Firebase Console 中启用 Authentication
2. 配置登录方法（Email/Password, Google, etc.）
3. 获取项目 ID 并在代码中配置

## 性能优化

### 公钥缓存

中间件使用 Cloudflare KV 存储来缓存 Google 的公钥，避免每次请求都获取公钥：

- 公钥会被缓存一定时间
- 当公钥过期时会自动刷新
- 使用 `WorkersKVStoreSingle` 实现单例模式

### 最佳实践

1. **缓存用户信息**：在应用层缓存用户基本信息，减少数据库查询
2. **使用自定义声明**：将用户角色等信息存储在 Firebase 自定义声明中
3. **合理设置 Token 过期时间**：平衡安全性和用户体验
4. **错误处理**：提供清晰的错误信息给客户端

## 调试

### 启用日志

```typescript
const config: VerifyFirebaseAuthConfig = {
  projectId: "a1d-chat2design",
  disableErrorLog: false, // 启用错误日志
};
```

### 常见问题

1. **Token 验证失败**：检查项目 ID 是否正确
2. **KV 存储错误**：确保 KV namespace 已正确绑定
3. **时钟偏差**：服务器时间与 Firebase 时间不同步

## 安全考虑

1. **HTTPS Only**：确保所有请求都通过 HTTPS
2. **Token 传输**：使用 Authorization 头而不是 URL 参数
3. **权限最小化**：只给用户必要的权限
4. **定期轮换密钥**：Firebase 会自动轮换签名密钥
5. **监控异常**：监控认证失败和异常访问模式

## API 端点示例

项目中已经添加了以下认证相关的 API 端点：

- `GET /api/v1/auth/me` - 获取当前用户信息
- `GET /api/v1/auth/check-permission` - 检查用户权限
- `GET /api/v1/auth/resource/:resourceId` - 访问用户资源
- `GET /api/v1/auth/admin` - 管理员专用端点

### 使用示例

```bash
# 获取当前用户信息
curl -H "Authorization: Bearer YOUR_FIREBASE_TOKEN" \
     http://localhost:8787/api/v1/auth/me

# 检查权限
curl -H "Authorization: Bearer YOUR_FIREBASE_TOKEN" \
     "http://localhost:8787/api/v1/auth/check-permission?permission=write"

# 访问用户资源
curl -H "Authorization: Bearer YOUR_FIREBASE_TOKEN" \
     http://localhost:8787/api/v1/auth/resource/123
```

## 总结

现在你已经了解了如何在 Hono Firebase Auth 中间件集成后获取用户 UID 以及它的工作原理：

### 核心要点

1. **获取 UID**：使用 `getFirebaseToken(c).uid`
2. **工作原理**：中间件验证 JWT token 并将解码信息存储在 context 中
3. **安全性**：基于 Firebase 的 JWT 验证，支持签名验证和声明检查
4. **灵活性**：支持自定义声明、角色权限、资源访问控制等

### 快速开始

```typescript
import { getFirebaseToken } from "@hono/firebase-auth";

export const myHandler = async (c: Context<HonoEnv>) => {
  const firebaseToken = getFirebaseToken(c);

  if (!firebaseToken) {
    return c.json({ error: "Unauthorized" }, 401);
  }

  const uid = firebaseToken.uid;
  // 使用 uid 进行业务逻辑处理...
};
```

这个集成为你的应用提供了强大而安全的用户认证和授权机制。
