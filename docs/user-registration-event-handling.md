# 用户注册事件处理技术方案

## 概述

本文档描述了 Chat2Design 后端系统中服务端检测和处理用户注册事件的技术解决方案。

## 问题描述

目前，用户通过客户端直接在 Firestore 中创建用户文档来完成注册。后端需要检测这些注册事件，以执行注册后的业务逻辑，例如：

- 发送欢迎邮件
- 赠送注册积分
- 记录注册事件
- 触发数据分析

## 解决方案架构

### 核心方法

我们实现一个**基于登录的事件检测**系统：

1. 客户端在 Firebase 认证后调用登录接口
2. 后端基于 Firestore 中的 `is_new_user` 标志检查用户是否为新用户
3. 如果检测到新用户，后端异步处理注册逻辑
4. 客户端通过现有的 Firestore 订阅接收数据更新

### 架构图

```
客户端                     CF Workers 后端              Firestore
  |                            |                            |
  |-- Firebase 认证登录 ------>|                            |
  |                            |                            |
  |-- 创建用户文档 -------------|--------------------------->|
  |    (is_new_user: true)     |                            |
  |                            |                            |
  |-- POST /auth/login ------->|                            |
  |                            |-- 查询用户文档 ----------->|
  |                            |<-- 用户数据 ---------------|
  |                            |                            |
  |                            |-- 检查 is_new_user ------->|
  |                            |                            |
  |<-- 204 No Content ---------|                            |
  |                            |                            |
  |                            |-- 异步处理：               |
  |                            |   - 发送欢迎邮件           |
  |                            |   - 赠送积分 ------------->|
  |                            |   - 设置 is_new_user: false |
  |                            |                            |
  |<-- Firestore 订阅 ---------|----------------------------|
  |    (积分已更新)            |                            |
```

## 技术实现

### 1. API 接口设计

```typescript
POST / api / v1 / auth / login;
Headers: Authorization: Bearer<firebase_token>;

// 无需请求体
// 用户信息通过 Firebase Auth 中间件获取

// 响应: 204 No Content (最小化响应)
```

### 2. 用户模式扩展

在现有 User 接口中添加新字段：

```typescript
interface User {
  // ... 现有字段
  is_new_user?: boolean; // 标记新用户的标志
  last_login_at?: Date; // 跟踪登录活动
}
```

### 3. 业务逻辑流程

```typescript
async function processLogin(firebaseToken) {
  const uid = firebaseToken.uid;

  // 1. 从 Firestore 查询用户文档
  const userDoc = await firestore.collection("users").doc(uid).get();

  if (!userDoc.exists) {
    // 边界情况：用户文档不存在
    // 在正常流程中不应该发生
    console.warn("已认证用户的用户文档未找到");
    return;
  }

  const userData = userDoc.data();

  // 2. 检查用户是否为新用户
  if (userData.is_new_user === true) {
    console.log(`处理新用户注册: ${uid}`);

    // 3. 立即标记为已处理（防止重复处理）
    await firestore.collection("users").doc(uid).update({
      is_new_user: false,
      updated_at: new Date(),
    });

    // 4. 异步处理注册逻辑
    c.executionCtx.waitUntil(processNewUserRegistration(uid, userData));
  }

  // 5. 更新最后登录时间
  await firestore.collection("users").doc(uid).update({
    last_login_at: new Date(),
  });
}
```

### 4. 注册处理逻辑

```typescript
async function processNewUserRegistration(uid: string, userData: any) {
  try {
    // 1. 赠送注册积分
    const signupCredits = 100;
    await firestore
      .collection("users")
      .doc(uid)
      .update({
        credits: (userData.credits || 0) + signupCredits,
        updated_at: new Date(),
      });

    // 2. 发送欢迎邮件
    await emailService.sendWelcomeEmail(userData.email, userData.display_name);

    // 3. 记录注册事件
    console.log(`用户注册完成: ${uid}`);

    // 4. 可以在此处添加其他逻辑：
    // - 数据分析跟踪
    // - 第三方集成
    // - 管理员系统通知
  } catch (error) {
    console.error(`用户 ${uid} 注册处理失败:`, error);
    // 考虑实现重试逻辑或死信队列
  }
}
```

## 实现优势

### 1. **架构一致性**

- 利用现有的 Firebase Auth 中间件
- 复用现有的 Firestore 用户服务
- 保持客户端 Firestore 订阅模式

### 2. **简洁性**

- 单一接口，职责明确
- 无需复杂的响应处理
- 客户端修改最少

### 3. **可靠性**

- 幂等操作（可安全重试）
- 立即更新标志防止重复处理
- 异步处理不阻塞响应

### 4. **可扩展性**

- 使用 CF Workers 的 `waitUntil` 进行后台处理
- 无需额外基础设施
- 利用现有的错误处理模式

## 错误处理和边界情况

### 1. **防止重复处理**

- `is_new_user` 标志立即设置为 `false`
- 尽可能使用幂等操作

### 2. **网络故障**

- 客户端可以安全地重试登录接口
- 后台处理失败会被记录但不影响用户体验

### 3. **部分失败**

- 如果邮件发送失败，用户仍然会获得积分
- 各个操作尽可能独立

## 未来增强功能

### 1. **重试机制**

- 为失败的邮件发送实现重试逻辑
- 考虑使用 CF Queues 进行更强大的后台处理

### 2. **数据分析集成**

- 在分析系统中跟踪注册事件
- 监控注册成功率

### 3. **A/B 测试**

- 不同的注册积分数量
- 不同的欢迎邮件模板

## 配置

### 环境变量

```bash
# 邮件服务配置
SENDGRID_API_KEY="your-sendgrid-key"
WELCOME_EMAIL_TEMPLATE_ID="template-id"

# 注册设置
SIGNUP_CREDITS_AMOUNT=100
```

### 功能开关

```typescript
const REGISTRATION_FEATURES = {
  SEND_WELCOME_EMAIL: true,
  GRANT_SIGNUP_CREDITS: true,
  TRACK_ANALYTICS: false,
};
```

## 测试策略

### 1. **单元测试**

- 测试各种用户状态下的注册逻辑
- 测试操作的幂等性

### 2. **集成测试**

- 端到端注册流程
- 错误处理场景

### 3. **负载测试**

- 并发注册处理
- 后台任务性能

## 部署计划

### 第一阶段：核心实现

1. 在 User 模式中添加 `is_new_user` 字段
2. 实现登录接口
3. 添加基本注册处理

### 第二阶段：增强功能

1. 邮件服务集成
2. 数据分析跟踪
3. 管理员通知

### 第三阶段：优化

1. 性能监控
2. 错误率分析
3. 用户体验改进

---

**文档版本**: 1.0
**最后更新**: 2025-01-20
**作者**: 开发团队
**状态**: 草案 - 准备实施
