# Feedback Backend Implementation Plan

## Overview

This document outlines the backend implementation plan for the Feedback/Contact Support feature, designed to work seamlessly with the frontend implementation described in `FeedbackForm-Implementation-Plan.md`.

## Current Architecture Analysis

### Existing Backend Infrastructure

- **Framework**: Hono.js running on Cloudflare Workers
- **Language**: TypeScript with strict type safety
- **Database**: Firebase Firestore with `firebase-rest-firestore` client
- **Storage**: Cloudflare R2 for file uploads
- **Authentication**: Firebase Authentication with `@hono/firebase-auth`
- **API Documentation**: Chanfana (OpenAPI/Swagger) with Zod validation
- **Dependency Injection**: tsyringe container management
- **Error Handling**: Centralized error handling with Sentry integration

### Integration Points

- **File Upload System**: Existing R2StorageService for attachment handling
- **User Management**: Firebase Auth integration for user identification
- **API Patterns**: Consistent endpoint structure following existing conventions
- **Validation**: Zod schemas for request/response validation
- **Documentation**: Auto-generated OpenAPI documentation

## Technical Design

### 1. Data Model Design

#### 1.1 Core Feedback Entity

```typescript
interface Feedback {
  id: string; // UUID
  userId?: string; // Optional for anonymous feedback
  type: FeedbackType; // bug | feature_request | question | other
  subject: string; // Brief description
  content: string; // Detailed description
  status: FeedbackStatus; // pending | in_progress | resolved | closed
  priority: FeedbackPriority; // low | medium | high | critical

  // Device information from frontend
  deviceInfo?: {
    deviceModel?: string; // "iPhone 15 Pro"
    systemVersion?: string; // "iOS 17.0"
    appVersion?: string; // "1.0.0"
    buildNumber?: string; // "100"
    userAgent?: string; // For web clients
  };

  // Attachment handling
  attachments?: FeedbackAttachment[];

  // Administrative fields
  assignedTo?: string; // Admin user ID
  tags?: string[]; // Categorization tags
  metadata?: Record<string, any>; // Extensible metadata

  // Timestamps
  createdAt: Date;
  updatedAt: Date;
  resolvedAt?: Date;
}

interface FeedbackAttachment {
  id: string; // UUID
  filename: string; // Original filename
  contentType: string; // MIME type
  size: number; // File size in bytes
  url: string; // R2 storage URL
  path: string; // R2 storage path
  uploadedAt: Date;
}
```

#### 1.2 Enums and Types

```typescript
enum FeedbackType {
  BUG = "bug",
  FEATURE_REQUEST = "feature_request",
  QUESTION = "question",
  OTHER = "other",
}

enum FeedbackStatus {
  PENDING = "pending",
  IN_PROGRESS = "in_progress",
  RESOLVED = "resolved",
  CLOSED = "closed",
}

enum FeedbackPriority {
  LOW = "low",
  MEDIUM = "medium",
  HIGH = "high",
  CRITICAL = "critical",
}
```

### 2. API Endpoint Design

#### 2.1 Core Endpoints

```typescript
// Submit feedback (supports both anonymous and authenticated users)
POST /api/v1/feedback
- Authentication: Optional (Bearer token)
- Body: SubmitFeedbackRequest
- Response: SubmitFeedbackResponse

// Get user's feedback history (authenticated users only)
GET /api/v1/users/me/feedback
- Authentication: Required
- Query: PaginationQuery
- Response: UserFeedbackListResponse

// Admin endpoints (future implementation)
GET /api/v1/admin/feedback
PATCH /api/v1/admin/feedback/:id
DELETE /api/v1/admin/feedback/:id
```

#### 2.2 Request/Response Schemas

```typescript
// Submit feedback request (matches frontend API design)
interface SubmitFeedbackRequest {
  type: string; // "bug" | "feature_request" | "question" | "other"
  subject: string; // Brief description
  content: string; // Detailed description
  deviceInfo?: {
    deviceModel?: string;
    systemVersion?: string;
    appVersion?: string;
    buildNumber?: string;
  };
  attachments?: {
    id: string; // Frontend-generated ID
    url: string; // Pre-uploaded R2 URL
    filename: string; // Original filename
  }[];
}

// Submit feedback response
interface SubmitFeedbackResponse {
  success: boolean;
  feedbackId: string; // "fb_123456789"
  message: string; // "Thank you for your feedback!"
}
```

### 3. Architecture Implementation

#### 3.1 Directory Structure

```
src/features/feedback/
├── feedback.schema.ts              # Zod validation schemas
├── feedback.routes.ts              # Route registration
├── services/
│   ├── feedback-service.interface.ts
│   └── firestore-feedback.service.ts
├── repositories/
│   ├── feedback-repository.interface.ts
│   └── firestore-feedback.repository.ts
├── endpoints/
│   ├── submit-feedback.handler.ts
│   ├── get-user-feedback.handler.ts
│   └── get-feedback-stats.handler.ts (future)
└── README.md                       # Feature documentation
```

#### 3.2 Service Layer Design

```typescript
// Feedback service interface
interface FeedbackService {
  submitFeedback(
    feedback: CreateFeedbackRequest,
    userId?: string
  ): Promise<Feedback>;
  getUserFeedback(
    userId: string,
    pagination: PaginationOptions
  ): Promise<PaginatedFeedback>;
  getFeedbackById(id: string, userId?: string): Promise<Feedback | null>;
  updateFeedbackStatus(
    id: string,
    status: FeedbackStatus,
    adminId: string
  ): Promise<void>;
}

// Repository interface for data access
interface FeedbackRepository {
  create(feedback: Feedback): Promise<Feedback>;
  findByUserId(
    userId: string,
    pagination: PaginationOptions
  ): Promise<Feedback[]>;
  findById(id: string): Promise<Feedback | null>;
  update(id: string, updates: Partial<Feedback>): Promise<void>;
  delete(id: string): Promise<void>;
}
```

### 4. File Upload Integration

#### 4.1 Attachment Handling Strategy

The feedback system will integrate with the existing file upload infrastructure:

1. **Pre-upload Pattern**: Frontend uploads attachments first, then includes URLs in feedback submission
2. **Dedicated Storage Prefix**: Use `feedback-attachments/` prefix for organization
3. **File Validation**: Leverage existing upload validation (size limits, MIME types)
4. **Cleanup Strategy**: Implement cleanup for orphaned attachments

#### 4.2 Upload Configuration

```typescript
// Extend existing file upload configuration
const FeedbackUploadConfig = {
  maxFileSize: 10 * 1024 * 1024, // 10MB per file
  maxTotalSize: 50 * 1024 * 1024, // 50MB total
  allowedMimeTypes: [
    "image/jpeg",
    "image/png",
    "image/gif",
    "application/pdf",
    "text/plain",
    "application/json", // For log files
  ],
  prefix: "feedback-attachments",
  compressionQuality: 0.8,
};
```

### 5. Authentication & Authorization

#### 5.1 Flexible Authentication

- **Anonymous Submissions**: Allow feedback without authentication
- **Authenticated Users**: Link feedback to user accounts for history tracking
- **Admin Access**: Role-based access for feedback management (future)

#### 5.2 Permission Model

```typescript
// Permission levels
enum FeedbackPermission {
  SUBMIT_ANONYMOUS = "feedback:submit:anonymous",
  SUBMIT_AUTHENTICATED = "feedback:submit:authenticated",
  VIEW_OWN = "feedback:view:own",
  VIEW_ALL = "feedback:view:all", // Admin
  MANAGE = "feedback:manage", // Admin
}
```

### 6. Error Handling & Validation

#### 6.1 Validation Rules

```typescript
const SubmitFeedbackSchema = z.object({
  type: z.enum(["bug", "feature_request", "question", "other"]),
  subject: z.string().min(5).max(200),
  content: z.string().min(10).max(5000),
  deviceInfo: z
    .object({
      deviceModel: z.string().optional(),
      systemVersion: z.string().optional(),
      appVersion: z.string().optional(),
      buildNumber: z.string().optional(),
    })
    .optional(),
  attachments: z
    .array(
      z.object({
        id: z.string().uuid(),
        url: z.string().url(),
        filename: z.string().min(1).max(255),
      })
    )
    .max(5)
    .optional(),
});
```

#### 6.2 Error Response Format

Following existing error handling patterns:

```typescript
// Standard error responses
interface FeedbackErrorResponse {
  error: string;
  code?: string;
  details?: string;
}

// Common error codes
enum FeedbackErrorCode {
  VALIDATION_ERROR = "FEEDBACK_VALIDATION_ERROR",
  ATTACHMENT_TOO_LARGE = "FEEDBACK_ATTACHMENT_TOO_LARGE",
  TOO_MANY_ATTACHMENTS = "FEEDBACK_TOO_MANY_ATTACHMENTS",
  RATE_LIMIT_EXCEEDED = "FEEDBACK_RATE_LIMIT_EXCEEDED",
  INTERNAL_ERROR = "FEEDBACK_INTERNAL_ERROR",
}
```

### 7. Performance & Security Considerations

#### 7.1 Rate Limiting

```typescript
// Rate limiting configuration
const FeedbackRateLimit = {
  anonymous: {
    requests: 3,
    window: "1h",
  },
  authenticated: {
    requests: 10,
    window: "1h",
  },
};
```

#### 7.2 Security Measures

- **Input Sanitization**: Sanitize all text inputs to prevent XSS
- **File Validation**: Strict MIME type and size validation
- **Rate Limiting**: Prevent spam submissions
- **Content Filtering**: Basic profanity and spam detection
- **Audit Logging**: Log all feedback submissions for monitoring

### 8. Database Design

#### 8.1 Firestore Collections

```
feedback/
├── {feedbackId}/
│   ├── id: string
│   ├── userId?: string
│   ├── type: string
│   ├── subject: string
│   ├── content: string
│   ├── status: string
│   ├── priority: string
│   ├── deviceInfo?: object
│   ├── attachments?: array
│   ├── tags?: array
│   ├── metadata?: object
│   ├── createdAt: timestamp
│   ├── updatedAt: timestamp
│   └── resolvedAt?: timestamp

feedback_attachments/
├── {attachmentId}/
│   ├── feedbackId: string
│   ├── filename: string
│   ├── contentType: string
│   ├── size: number
│   ├── url: string
│   ├── path: string
│   └── uploadedAt: timestamp
```

#### 8.2 Indexing Strategy

```javascript
// Firestore composite indexes
feedback: [
  ["userId", "createdAt"],
  ["status", "createdAt"],
  ["type", "createdAt"],
  ["priority", "createdAt"],
  ["assignedTo", "status", "createdAt"],
];

feedback_attachments: [["feedbackId", "uploadedAt"]];
```

### 9. Integration with Existing Systems

#### 9.1 Container Registration

```typescript
// Add to container.setup.ts
container.register<FeedbackService>(IFeedbackService, {
  useClass: FirestoreFeedbackService,
});

container.register<FeedbackRepository>(IFeedbackRepository, {
  useClass: FirestoreFeedbackRepository,
});
```

#### 9.2 Route Registration

```typescript
// Add to src/index.ts
import { registerFeedbackRoutes } from "./features/feedback/feedback.routes";

// Register feedback routes (mixed authentication)
registerFeedbackRoutes(openapi, "/api/v1/feedback");
```

### 10. Monitoring & Analytics

#### 10.1 Metrics Collection

- **Submission Rate**: Track feedback submission frequency
- **Type Distribution**: Monitor feedback type distribution
- **Response Time**: API endpoint performance metrics
- **Error Rate**: Track validation and processing errors
- **User Engagement**: Authenticated vs anonymous submissions

#### 10.2 Alerting

- **High Error Rate**: Alert on validation or processing failures
- **Spam Detection**: Alert on suspicious submission patterns
- **Storage Usage**: Monitor attachment storage consumption

## Implementation Phases

### Phase 1: Core Infrastructure (Week 1)

- [ ] Data models and schemas
- [ ] Repository and service interfaces
- [ ] Basic Firestore implementation
- [ ] Container registration

### Phase 2: API Endpoints (Week 1-2)

- [ ] Submit feedback endpoint
- [ ] User feedback history endpoint
- [ ] Request/response validation
- [ ] Error handling implementation

### Phase 3: File Upload Integration (Week 2)

- [ ] Attachment handling logic
- [ ] R2 storage integration
- [ ] File validation and cleanup
- [ ] Upload configuration

### Phase 4: Security & Performance (Week 2-3)

- [ ] Rate limiting implementation
- [ ] Input sanitization
- [ ] Content filtering
- [ ] Performance optimization

### Phase 5: Testing & Documentation (Week 3)

- [ ] Unit tests
- [ ] Integration tests
- [ ] API documentation
- [ ] Deployment preparation

## Success Criteria

1. **Functional Requirements**

   - ✅ Support both anonymous and authenticated feedback submission
   - ✅ Handle multiple file attachments up to size limits
   - ✅ Provide user feedback history for authenticated users
   - ✅ Maintain data consistency and integrity

2. **Performance Requirements**

   - ✅ API response time < 2 seconds for submission
   - ✅ Support concurrent submissions without data corruption
   - ✅ Efficient pagination for feedback history

3. **Security Requirements**

   - ✅ Prevent spam and abuse through rate limiting
   - ✅ Sanitize all user inputs
   - ✅ Secure file upload handling
   - ✅ Proper authentication and authorization

4. **Integration Requirements**
   - ✅ Seamless integration with existing architecture
   - ✅ Consistent API patterns and error handling
   - ✅ Proper dependency injection and container management

## Risk Assessment

### Technical Risks

- **Medium**: File upload complexity and cleanup logic
- **Low**: Firestore integration (well-established patterns)
- **Low**: Authentication integration (existing infrastructure)

### Business Risks

- **Medium**: Spam and abuse potential
- **Low**: Storage cost implications
- **Low**: User adoption and engagement

## Future Enhancements

1. **Admin Dashboard**: Web interface for feedback management
2. **Email Notifications**: Automated responses and status updates
3. **AI Classification**: Automatic feedback categorization and priority assignment
4. **Analytics Dashboard**: Feedback trends and insights
5. **Integration APIs**: Webhook support for external systems (Slack, Jira, etc.)
6. **Multi-language Support**: Internationalization for global users

## Technical Implementation Details

### 10.1 Key Service Implementation

```typescript
// FirestoreFeedbackService implementation highlights
@singleton()
export class FirestoreFeedbackService implements FeedbackService {
  constructor(
    @inject(IFeedbackRepository) private repository: FeedbackRepository,
    @inject(IStorageService) private storageService: StorageService,
    @inject(IUserRepository) private userRepository: UserRepository
  ) {}

  async submitFeedback(
    request: CreateFeedbackRequest,
    userId?: string
  ): Promise<Feedback> {
    // Validate attachments exist in R2
    await this.validateAttachments(request.attachments);

    // Create feedback entity
    const feedback: Feedback = {
      id: uuidv4(),
      userId,
      type: request.type as FeedbackType,
      subject: request.subject.trim(),
      content: request.content.trim(),
      status: FeedbackStatus.PENDING,
      priority: this.determinePriority(request.type, request.content),
      deviceInfo: request.deviceInfo,
      attachments: await this.processAttachments(request.attachments),
      tags: this.generateTags(request.type, request.content),
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    return await this.repository.create(feedback);
  }

  private async validateAttachments(
    attachments?: AttachmentRequest[]
  ): Promise<void> {
    if (!attachments?.length) return;

    // Validate total size and count
    const totalSize = attachments.reduce(
      (sum, att) => sum + (att.size || 0),
      0
    );
    if (totalSize > FeedbackUploadConfig.maxTotalSize) {
      throw new Error("Total attachment size exceeds limit");
    }

    if (attachments.length > 5) {
      throw new Error("Too many attachments");
    }
  }
}
```

### 10.2 Endpoint Implementation Pattern

```typescript
// SubmitFeedbackEndpoint following existing patterns
export class SubmitFeedbackEndpoint extends OpenAPIRoute {
  schema = {
    tags: ["Feedback"],
    summary: "Submit user feedback",
    description:
      "Submit feedback with optional attachments. Supports both anonymous and authenticated users.",
    request: {
      body: {
        content: {
          "application/json": {
            schema: SubmitFeedbackRequestSchema,
          },
        },
      },
    },
    responses: {
      201: {
        description: "Feedback submitted successfully",
        content: {
          "application/json": {
            schema: SubmitFeedbackResponseSchema,
          },
        },
      },
      400: {
        description: "Validation error",
        content: {
          "application/json": {
            schema: ValidationErrorSchema,
          },
        },
      },
      429: {
        description: "Rate limit exceeded",
        content: {
          "application/json": {
            schema: ErrorResponseSchema,
          },
        },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    try {
      const { body } = await this.getValidatedData<typeof this.schema>();
      const container = c.get("container");
      const feedbackService =
        container.resolve<FeedbackService>(IFeedbackService);

      // Get user ID if authenticated (optional)
      const firebaseToken = getFirebaseToken(c);
      const userId = firebaseToken?.uid;

      // Apply rate limiting
      await this.checkRateLimit(c, userId);

      // Submit feedback
      const feedback = await feedbackService.submitFeedback(body, userId);

      return c.json(
        {
          success: true,
          feedbackId: feedback.id,
          message: "Thank you for your feedback! We'll review it shortly.",
        },
        201
      );
    } catch (error: any) {
      console.error("Error submitting feedback:", error);

      if (error.message.includes("rate limit")) {
        return c.json(
          { error: "Too many submissions. Please try again later." },
          429
        );
      }

      if (error.message.includes("validation")) {
        return c.json({ error: error.message }, 400);
      }

      return c.json(
        { error: "Failed to submit feedback. Please try again." },
        500
      );
    }
  }

  private async checkRateLimit(
    c: Context<HonoEnv>,
    userId?: string
  ): Promise<void> {
    // Implementation would use existing rate limiting infrastructure
    // Similar to other endpoints in the project
  }
}
```

### 10.3 Database Migration Considerations

Since this is a new feature, no migration is needed, but we should consider:

```typescript
// Initial data seeding for feedback types and priorities
const FEEDBACK_SEED_DATA = {
  types: [
    { id: "bug", name: "Bug Report", description: "Report a problem or error" },
    {
      id: "feature_request",
      name: "Feature Request",
      description: "Suggest a new feature",
    },
    {
      id: "question",
      name: "Question",
      description: "Ask for help or clarification",
    },
    { id: "other", name: "Other", description: "General feedback or comments" },
  ],
  priorities: {
    bug: "high",
    feature_request: "medium",
    question: "medium",
    other: "low",
  },
};
```

## Alignment with Frontend Requirements

### 11.1 API Contract Compliance

The backend design exactly matches the frontend API expectations:

✅ **Endpoint**: `POST /api/v1/feedback` (matches frontend expectation)
✅ **Request Format**: Identical to frontend `SubmitFeedbackRequest`
✅ **Response Format**: Matches expected `SubmitFeedbackResponse`
✅ **Authentication**: Optional (supports both anonymous and authenticated)
✅ **File Upload**: Pre-upload pattern with URL references

### 11.2 Error Handling Alignment

```typescript
// Error responses match frontend error handling expectations
interface FeedbackErrorResponse {
  error: string; // Human-readable message
  code?: string; // Programmatic error code
  details?: string; // Additional context
}

// Frontend can handle these error scenarios:
// - Network errors (handled by frontend retry logic)
// - Validation errors (400 with detailed messages)
// - Rate limiting (429 with retry-after guidance)
// - Server errors (500 with generic message)
```

### 11.3 Device Info Integration

The backend accepts and stores device information exactly as provided by the frontend:

```typescript
// Device info structure matches iOS UIDevice data
interface DeviceInfo {
  deviceModel?: string; // UIDevice.current.model
  systemVersion?: string; // UIDevice.current.systemVersion
  appVersion?: string; // Bundle.main.infoDictionary CFBundleShortVersionString
  buildNumber?: string; // Bundle.main.infoDictionary CFBundleVersion
}
```

## Conclusion

This comprehensive backend implementation plan provides:

1. **Complete Architecture**: Full-stack solution aligned with existing patterns
2. **Frontend Compatibility**: 100% API contract compliance with frontend requirements
3. **Scalable Design**: Extensible architecture for future enhancements
4. **Security First**: Built-in rate limiting, validation, and abuse prevention
5. **Performance Optimized**: Efficient data access patterns and caching strategies
6. **Maintainable Code**: Clean architecture with proper separation of concerns

The implementation follows all established project conventions while introducing a robust feedback system that enhances user engagement and provides valuable insights for product improvement.
