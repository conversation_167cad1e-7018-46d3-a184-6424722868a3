# TSyringe 依赖注入最佳实践

## 问题：构造函数依赖过多

随着应用程序的增长，服务类的依赖会越来越多，导致构造函数参数过多的问题：

```typescript
// ❌ 问题：构造函数参数过多
@singleton()
export class ComplexService {
  constructor(
    @inject(IDbService) private dbService: DbService,
    @inject(IEnvService) private envService: EnvService,
    @inject(ICacheService) private cacheService: CacheService,
    @inject(IStorageService) private storageService: StorageService,
    @inject(NotionService) private notionService: NotionService,
    @inject(IGptService) private gptService: GptService,
    @inject(IUserService) private userService: UserService,
    // ... 更多依赖
  ) {}
}
```

## TSyringe 提供的解决方案

### 1. Factory Provider 模式 (推荐)

TSyringe 的 Factory Provider 是处理复杂依赖的最佳方式：

```typescript
// 创建工厂类
@singleton()
export class ImageGenerationServiceFactory {
  constructor(
    @inject(IDbService) private dbService: DbService,
    @inject(IEnvService) private envService: EnvService,
    @inject(NotionService) private notionService: NotionService,
    @inject(ICacheService) private cacheService: CacheService
  ) {}

  create(): ImageGenerationService {
    return new ImageGenerationService({
      dbService: this.dbService,
      envService: this.envService,
      notionService: this.notionService,
      cacheService: this.cacheService,
    });
  }
}

// 在容器中注册
container.register(IImageGenerationService, {
  useFactory: (container) => {
    const factory = container.resolve(ImageGenerationServiceFactory);
    return factory.create();
  },
});
```

### 2. 服务聚合模式

将相关服务组合成更高层次的服务：

```typescript
// 基础设施服务聚合器
@singleton()
export class InfrastructureServices {
  constructor(
    @inject(IDbService) public readonly db: DbService,
    @inject(IEnvService) public readonly env: EnvService,
    @inject(ICacheService) public readonly cache: CacheService,
    @inject(IStorageService) public readonly storage: StorageService
  ) {}
}

// 业务服务聚合器
@singleton()
export class BusinessServices {
  constructor(
    @inject(NotionService) public readonly notion: NotionService,
    @inject(IGptService) public readonly gpt: GptService
  ) {}
}

// 简化的服务类
@singleton()
export class ImageGenerationService {
  constructor(
    @inject(InfrastructureServices) private infrastructure: InfrastructureServices,
    @inject(BusinessServices) private business: BusinessServices
  ) {}

  async doSomething() {
    const data = await this.business.notion.getPromptList();
    await this.infrastructure.cache.set('key', data);
    return this.infrastructure.db.getFirestoreInstance().collection('test').add(data);
  }
}
```

### 3. 配置对象模式

使用配置对象封装依赖：

```typescript
export interface ServiceDependencies {
  dbService: DbService;
  envService: EnvService;
  notionService: NotionService;
  cacheService?: CacheService;
}

@singleton()
export class MyService {
  constructor(private dependencies: ServiceDependencies) {}
  
  // 或者使用构造函数重载支持两种方式
  constructor(dependencies: ServiceDependencies);
  constructor(
    dbService: DbService,
    envService: EnvService,
    notionService: NotionService
  );
  constructor(
    dependenciesOrDbService: ServiceDependencies | DbService,
    envService?: EnvService,
    notionService?: NotionService
  ) {
    // 实现逻辑...
  }
}
```

### 4. TSyringe 内置工厂函数

使用 TSyringe 提供的内置工厂函数：

```typescript
import { instanceCachingFactory, instancePerContainerCachingFactory } from "tsyringe";

// 单例工厂
container.register("SingletonService", {
  useFactory: instanceCachingFactory<MyService>(c => {
    const deps = c.resolve(ServiceFactory);
    return deps.create();
  })
});

// 容器作用域工厂
container.register("ContainerScopedService", {
  useFactory: instancePerContainerCachingFactory<MyService>(c => {
    return c.resolve(MyService);
  })
});
```

## 最佳实践总结

### 1. 选择合适的模式

- **Factory Provider**: 适用于复杂的依赖关系和条件性创建
- **服务聚合**: 适用于逻辑相关的服务组合
- **配置对象**: 适用于参数较多但结构稳定的场景

### 2. 保持向后兼容

使用构造函数重载来支持新旧两种方式：

```typescript
constructor(dependencies: Dependencies);
constructor(service1: Service1, service2: Service2);
constructor(/* 实现 */) {}
```

### 3. 利用 TypeScript 类型系统

```typescript
// 使用接口定义依赖结构
interface ServiceDependencies {
  required: RequiredService;
  optional?: OptionalService;
}

// 使用泛型提高复用性
interface ServiceFactory<T> {
  create(): T;
}
```

### 4. 测试友好

工厂模式和聚合模式都便于单元测试：

```typescript
// 测试时可以轻松模拟依赖
const mockDependencies: ServiceDependencies = {
  dbService: mockDbService,
  envService: mockEnvService,
  // ...
};

const service = new MyService(mockDependencies);
```

## 推荐的项目结构

```
src/
├── infrastructure/
│   ├── service-aggregator.ts     # 服务聚合器
│   └── factories/                # 工厂类
├── features/
│   └── feature-name/
│       ├── feature.service.ts    # 业务服务
│       ├── feature.factory.ts    # 服务工厂
│       └── feature.interface.ts  # 接口定义
└── container.setup.ts            # 容器配置
```

这种方式既解决了依赖过多的问题，又保持了代码的可维护性和可测试性。
