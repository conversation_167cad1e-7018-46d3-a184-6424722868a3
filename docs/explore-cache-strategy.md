# Explore 功能缓存策略设计

## 概述

本文档详细描述了 Explore 功能的多层缓存架构设计，旨在通过合理的缓存策略提升性能、降低成本并改善用户体验。

## 缓存架构概览

### 多层缓存架构

```
用户请求 → Cloudflare CDN → Worker Memory Cache → KV Cache → Firestore
```

### 缓存层级说明

1. **Cloudflare CDN 边缘缓存** - 全球分布式缓存
2. **Cloudflare KV Cache** - 跨实例持久化缓存
4. **Firestore** - 数据源

## 详细缓存策略

### 第一层：Cloudflare CDN 边缘缓存

#### 适用场景

- 热门内容首页 (`/api/v1/explore?sortBy=popular&page=1`)
- 默认参数查询 (`/api/v1/explore`)
- 公共内容列表

#### 缓存配置

```typescript
// 热门内容 - 缓存 10 分钟
if (query.sortBy === "popular" && query.page === 1) {
  c.header("Cache-Control", "public, max-age=300, s-maxage=600");
}

// 最新内容 - 缓存 2 分钟
else if (query.sortBy === "latest") {
  c.header("Cache-Control", "public, max-age=60, s-maxage=120");
}

// 其他内容 - 缓存 1 分钟
else {
  c.header("Cache-Control", "public, max-age=30, s-maxage=60");
}

// 确保不同参数分别缓存
c.header("Vary", "Accept-Encoding");
```

#### 缓存键构成

```
URL + Query Parameters + Vary Headers
例：/api/v1/explore?page=1&limit=20&sortBy=popular
```

#### TTL 策略

- **热门内容**: 10 分钟（变化较慢）
- **最新内容**: 2 分钟（变化较快）
- **分页内容**: 5 分钟（中等变化频率）
- **错误响应**: 不缓存


### 第三层：Cloudflare KV Cache

#### 适用场景

- 跨 Worker 实例数据共享
- 持久化缓存存储
- 复杂查询结果

#### 缓存内容

```typescript
// 主要内容缓存
"explore:content:{page}:{limit}:{type}:{sourceType}:{sortBy}" → ExploreResponse

// 用户信息缓存
"explore:users:batch:{userIds_hash}" → Map<string, UserInfo>

// 统计信息缓存
"explore:stats:{date}:{type}:{sourceType}" → FilterMeta

// 热门内容缓存
"explore:trending:{date}:{limit}" → ExploreItem[]
```

#### TTL 策略

- **内容结果**: 5-30 分钟
- **用户信息**: 10 分钟
- **统计数据**: 15 分钟
- **热门内容**: 30 分钟

## 缓存键设计规范

### 命名规范

```typescript
// 基础格式
{feature}:{type}:{parameters}

// 具体示例
explore:content:1:20:image/*:ai_generated:popular
explore:users:batch:abc123def456
explore:stats:2024-01-02:video:all
```

### 键生成算法

```typescript
function generateCacheKey(query: ExploreQuery): string {
  const params = [
    query.page || 1,
    query.limit || 20,
    query.type || "all",
    query.sourceType || "all",
    query.sortBy || "latest",
  ].join(":");

  return `explore:content:${params}`;
}

function generateUserCacheKey(userIds: string[]): string {
  const hash = createHash(userIds.sort().join(","));
  return `explore:users:batch:${hash}`;
}
```

## 缓存失效策略

### 主动失效

#### 内容更新触发

```typescript
// 新 Asset 创建时
async onAssetCreated(asset: Asset) {
  await this.invalidateContentCache(asset.sourceType, asset.type);
}

// 社交统计更新时
async onSocialStatsUpdated(assetId: string) {
  await this.invalidatePopularCache();
}
```

#### 失效范围

- **新内容发布**: 清除 `latest` 排序相关缓存
- **热门度变化**: 清除 `popular` 排序相关缓存
- **用户信息更新**: 清除用户信息相关缓存

### 被动失效

- **TTL 自然过期**: 各层缓存根据 TTL 自动过期
- **LRU 策略**: 内存缓存采用 LRU 淘汰策略
- **容量限制**: KV 缓存根据使用量自动清理

## 实现架构

### 缓存服务接口

```typescript
interface ExploreCacheService {
  // 获取缓存内容
  getExploreContent(query: ExploreQuery): Promise<ExploreResponse | null>;

  // 设置缓存内容
  setExploreContent(
    query: ExploreQuery,
    data: ExploreResponse,
    ttl?: number
  ): Promise<void>;

  // 获取用户信息
  getUserInfoBatch(userIds: string[]): Promise<Map<string, UserInfo> | null>;

  // 失效缓存
  invalidateContentCache(sourceType?: string, type?: string): Promise<void>;
  invalidateUserCache(userIds?: string[]): Promise<void>;
}
```

### 分层缓存逻辑

```typescript
async getExploreContent(query: ExploreQuery): Promise<ExploreResponse> {
  const cacheKey = this.generateCacheKey(query);

  // 2. 尝试 KV 缓存
  result = await this.kvCache.get<ExploreResponse>(cacheKey);
  if (result) {
    console.log(`KV cache hit: ${cacheKey}`);
    // 回填内存缓存
    await this.memoryCache.set(cacheKey, result, 60000); // 1分钟
    return result;
  }

  // 3. 从数据库查询
  console.log(`Cache miss, fetching from database: ${cacheKey}`);
  result = await this.fetchFromDatabase(query);

  // 4. 写入缓存
  const ttl = this.calculateTTL(query);
  await this.kvCache.set(cacheKey, result, ttl);
  await this.memoryCache.set(cacheKey, result, Math.min(ttl, 60000));

  return result;
}
```

## 性能优化策略

### 预热策略

```typescript
// 定时预热热门内容
async warmupPopularContent() {
  const popularQueries = [
    { sortBy: 'popular', page: 1, limit: 20 },
    { sortBy: 'popular', type: 'image/*', page: 1, limit: 20 },
    { sortBy: 'popular', type: 'video/*', page: 1, limit: 20 }
  ];

  for (const query of popularQueries) {
    await this.getExploreContent(query);
  }
}

// 新内容发布时预热
async onNewContentPublished(asset: Asset) {
  // 预热相关页面
  await this.warmupRelatedContent(asset.sourceType, asset.type);
}
```

### 智能缓存

```typescript
// 根据访问频率动态调整 TTL
function calculateTTL(query: ExploreQuery, accessCount: number): number {
  const baseTTL = query.sortBy === "popular" ? 600000 : 120000; // 10分钟 vs 2分钟

  // 高频访问内容延长缓存时间
  if (accessCount > 100) return baseTTL * 2;
  if (accessCount > 50) return baseTTL * 1.5;

  return baseTTL;
}
```

### 缓存穿透保护

```typescript
// 空结果也缓存，防止缓存穿透
async getExploreContent(query: ExploreQuery): Promise<ExploreResponse> {
  // ... 缓存查询逻辑

  const result = await this.fetchFromDatabase(query);

  // 即使是空结果也缓存（较短TTL）
  if (result.items.length === 0) {
    await this.kvCache.set(cacheKey, result, 30000); // 30秒
  } else {
    await this.kvCache.set(cacheKey, result, this.calculateTTL(query));
  }

  return result;
}
```

## 监控和指标

### 缓存性能指标

#### 关键指标

- **缓存命中率**: 各层缓存的命中率统计
- **响应时间**: 缓存命中 vs 数据库查询的响应时间对比
- **缓存大小**: 各层缓存的存储使用情况
- **失效频率**: 缓存失效的频率和原因分析

#### 监控实现

```typescript
interface CacheMetrics {
  // 命中率统计
  hits: number;
  misses: number;
  hitRate: number;

  // 响应时间统计
  avgResponseTime: number;
  cacheResponseTime: number;
  dbResponseTime: number;

  // 存储统计
  totalKeys: number;
  totalSize: number;

  // 失效统计
  evictions: number;
  expirations: number;
}

class CacheMonitor {
  async recordCacheHit(layer: "memory" | "kv" | "cdn", key: string) {
    await this.analyticsService.recordEvent("cache_hit", {
      layer,
      key: this.hashKey(key),
      timestamp: Date.now(),
    });
  }

  async recordCacheMiss(layer: "memory" | "kv" | "cdn", key: string) {
    await this.analyticsService.recordEvent("cache_miss", {
      layer,
      key: this.hashKey(key),
      timestamp: Date.now(),
    });
  }
}
```

### 告警策略

- **命中率低于 70%**: 需要优化缓存策略
- **响应时间超过 500ms**: 检查缓存配置
- **缓存大小超过限制**: 调整 TTL 或清理策略

## 成本分析

### 缓存收益

- **Firestore 读取减少**: 预计减少 60-80% 的数据库查询
- **响应时间提升**: 缓存命中时响应时间 < 50ms
- **并发能力提升**: 支持更高的并发访问量

### 成本考虑

- **KV 存储成本**: 基于存储量和读写次数
- **CDN 缓存**: Cloudflare 免费提供
- **内存使用**: Worker 内存限制内免费

### ROI 估算

```
假设场景：
- 日访问量: 10万次
- 缓存命中率: 75%
- Firestore 读取成本: $0.36/100万次

成本节省：
- 无缓存: 100,000 * $0.36/1,000,000 = $0.036/天
- 有缓存: 25,000 * $0.36/1,000,000 = $0.009/天
- 节省: $0.027/天 = $9.86/年

额外收益：
- 用户体验提升
- 系统稳定性增强
- 支持更高并发
```

## 实施计划

### Phase 1: 基础缓存（1-2 周）

- [x] 集成现有 KV 缓存服务
- [ ] 实现基础的内容缓存逻辑
- [ ] 添加 CDN 缓存头设置
- [ ] 基础监控和日志

### Phase 2: 优化缓存（2-3 周）

- [ ] 实现用户信息批量缓存
- [ ] 添加智能 TTL 计算
- [ ] 实现缓存预热机制
- [ ] 完善监控指标

### Phase 3: 高级功能（3-4 周）

- [ ] 实现缓存失效策略
- [ ] 添加缓存穿透保护
- [ ] 性能调优和压力测试
- [ ] 完整的监控面板

## 风险控制

### 技术风险

- **缓存一致性**: 通过合理的失效策略确保数据一致性
- **缓存雪崩**: 通过错开 TTL 和预热机制避免
- **缓存穿透**: 通过空值缓存和布隆过滤器防护

### 业务风险

- **数据延迟**: 通过合理的 TTL 平衡性能和实时性
- **存储成本**: 通过监控和自动清理控制成本
- **复杂度增加**: 通过完善的文档和监控降低维护成本

## 总结

本缓存策略通过多层缓存架构，充分利用 Cloudflare Workers 的边缘计算优势，实现了：

1. **性能提升**: 响应时间从数百毫秒降低到数十毫秒
2. **成本控制**: 大幅减少数据库查询次数
3. **扩展性**: 支持高并发和全球分布式访问
4. **可维护性**: 清晰的缓存策略和完善的监控

该方案为 Explore 功能提供了坚实的性能基础，为后续的社交功能和个性化推荐奠定了良好的架构基础。

---

_文档版本: 1.0_
_创建日期: 2025-01-02_
_下次更新: Phase 1 实施完成后_
