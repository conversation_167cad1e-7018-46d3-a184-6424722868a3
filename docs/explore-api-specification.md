# Explore API 规范文档

## 概述

本文档定义了 Explore 功能的详细 API 规范，包括请求/响应格式、错误处理、认证要求等。

## 基础信息

- **Base URL**: `/api/v1`
- **认证方式**: <PERSON><PERSON> (Firebase JWT)
- **内容类型**: `application/json`

## API 端点

### 1. 获取 Explore 内容

#### `GET /explore`

获取公开的用户生成内容列表，支持分页和过滤。

**请求参数**

| 参数       | 类型   | 必需 | 默认值   | 描述                                                     |
| ---------- | ------ | ---- | -------- | -------------------------------------------------------- |
| page       | number | 否   | 1        | 页码，从 1 开始                                          |
| limit      | number | 否   | 20       | 每页数量，最大 50                                        |
| type       | string | 否   | "all"    | MIME 类型过滤: "image/_", "video/_", "image/jpeg", "all" |
| sourceType | string | 否   | "all"    | 来源类型: "ai_generated", "user_upload", "all"           |
| sortBy     | string | 否   | "latest" | 排序方式: "latest", "popular"                            |

**响应格式**

```typescript
{
  items: ExploreItem[],
  pagination: PaginationMeta,
  filters: {
    totalCounts: {
      image: number,
      video: number,
      total: number
    }
  }
}
```

**ExploreItem 结构**

```typescript
{
  id: string,                    // Asset ID
  type: string,                  // MIME 类型 (如 "image/jpeg", "video/mp4")
  sourceType: "ai_generated" | "user_upload", // 来源类型
  userId: string,                // 创建者ID
  userDisplayName?: string,      // 创建者显示名称
  userPhotoURL?: string,         // 创建者头像

  // 内容信息 (Web 标准)
  url: string,                   // 内容URL (原 storageUrl)
  thumbnailUrl?: string,         // 缩略图URL
  name?: string,                 // 文件名 (原 originalFilename)
  size?: number,                 // 文件大小 (原 fileSize)
  generationPrompt?: string,     // 生成提示词（仅AI生成内容）
  description?: string,          // 内容描述
  tags?: string[],               // 标签

  // 统计信息
  likeCount: number,             // 点赞数
  favoriteCount: number,         // 收藏数

  // 时间信息
  createdAt: string,             // 创建时间 (ISO string)
}
```

**响应示例**

```json
{
  "items": [
    {
      "id": "asset-123",
      "type": "image/jpeg",
      "sourceType": "ai_generated",
      "userId": "user-456",
      "userDisplayName": "Alice",
      "userPhotoURL": "https://example.com/avatar.jpg",
      "url": "https://r2.example.com/image.jpg",
      "thumbnailUrl": "https://r2.example.com/thumb.jpg",
      "name": "sunset-mountains.jpg",
      "size": 2048576,
      "generationPrompt": "A beautiful sunset over mountains",
      "likeCount": 15,
      "favoriteCount": 3,
      "createdAt": "2024-01-15T10:30:00Z"
    }
  ],
  "pagination": {
    "currentPage": 1,
    "totalPages": 5,
    "totalItems": 100,
    "itemsPerPage": 20,
    "hasNextPage": true,
    "hasPreviousPage": false
  },
  "filters": {
    "availableCategories": ["nature", "portrait", "abstract"],
    "totalCounts": {
      "image": 80,
      "video": 20,
      "ai_generated": 60,
      "user_upload": 40,
      "total": 100
    }
  }
}
```

### 2. 用户交互状态查询

#### `GET /explore/:assetId/interaction-status`

获取当前用户对指定资产的交互状态。

**认证**: 必需

**路径参数**

- `assetId`: string - 资产 ID

**响应**

```json
{
  "assetId": "asset-123",
  "isLikedByCurrentUser": false,
  "isFavoritedByCurrentUser": true
}
```

### 3. 社交交互 API

#### `POST /assets/:assetId/like`

为指定资产添加点赞。

**认证**: 必需

**路径参数**

- `assetId`: string - 资产 ID

**响应**

```json
{
  "success": true,
  "likeCount": 16
}
```

#### `DELETE /assets/:assetId/like`

取消对指定资产的点赞。

**认证**: 必需

**响应**

```json
{
  "success": true,
  "likeCount": 15
}
```

#### `POST /assets/:assetId/favorite`

收藏指定资产。

**认证**: 必需

**响应**

```json
{
  "success": true,
  "favoriteCount": 4
}
```

#### `DELETE /assets/:assetId/favorite`

取消收藏指定资产。

**认证**: 必需

**响应**

```json
{
  "success": true,
  "favoriteCount": 3
}
```

### 4. 用户交互历史

#### `GET /users/me/likes`

获取当前用户的点赞列表。

**认证**: 必需

**请求参数**
| 参数 | 类型 | 必需 | 默认值 | 描述 |
|------|------|------|--------|------|
| page | number | 否 | 1 | 页码 |
| limit | number | 否 | 20 | 每页数量 |

**响应**

```json
{
  "items": [ExploreItem],
  "pagination": PaginationMeta
}
```

#### `GET /users/me/favorites`

获取当前用户的收藏列表。

**认证**: 必需

**请求参数**: 同上

**响应**: 同上

### 4. 资产统计信息

#### `GET /assets/:assetId/stats`

获取指定资产的详细统计信息。

**路径参数**

- `assetId`: string - 资产 ID

**响应**

```json
{
  "assetId": "asset-123",
  "stats": {
    "likeCount": 15,
    "favoriteCount": 3,
    "shareCount": 2,
    "viewCount": 120
  }
}
```

## 错误处理

### 标准错误格式

```json
{
  "error": "Error message",
  "code": "ERROR_CODE",
  "details": "Additional error details"
}
```

### 常见错误码

| HTTP 状态码 | 错误码          | 描述                     |
| ----------- | --------------- | ------------------------ |
| 400         | INVALID_REQUEST | 请求参数无效             |
| 401         | UNAUTHORIZED    | 未认证或认证失效         |
| 403         | FORBIDDEN       | 无权限访问               |
| 404         | NOT_FOUND       | 资源不存在               |
| 409         | ALREADY_EXISTS  | 资源已存在（如重复点赞） |
| 429         | RATE_LIMITED    | 请求频率超限             |
| 500         | INTERNAL_ERROR  | 服务器内部错误           |

### 错误示例

```json
{
  "error": "Asset not found or not public",
  "code": "NOT_FOUND",
  "details": "The requested asset does not exist or is not publicly accessible"
}
```

