# Explore 功能实现计划

## 概述

本文档详细规划了 Explore 功能的实现步骤，包括具体的开发任务、时间安排和依赖关系。

## 实现阶段

### Phase 1: 基础架构 (第 1-2 周)

#### 1.1 扩展 Asset Schema

**任务**: 为 Asset 添加 AI 生成内容相关字段

**具体工作**:

- [ ] 修改 `src/features/assets/asset.schema.ts`
- [ ] Web 规范化字段：`fileType` → `type`, `originalFilename` → `name`, `fileSize` → `size`, `storageUrl` → `url`
- [ ] 添加 `sourceType`, `sourceTaskId`, `generationPrompt` 字段
- [ ] 添加 `likeCount`, `favoriteCount` 字段
- [ ] 更新 Zod 验证 schema

**预期产出**:

```typescript
// 扩展后的 Asset 接口
export interface Asset {
  // ... 现有字段

  // Web 标准字段 (遵循 File API)
  name: string; // 原 originalFilename
  size: number; // 原 fileSize
  type: string; // MIME type (原 mimeType)
  url: string; // 原 storageUrl

  // 业务字段
  sourceType: "ai_generated" | "user_upload";
  sourceTaskId?: string; // 仅AI生成内容
  generationPrompt?: string; // 仅AI生成内容
  likeCount: number;
  favoriteCount: number;
}
```

#### 1.2 实现 Asset 自动创建机制

**任务**: 在 Task 完成时自动创建对应的 Asset 记录

**具体工作**:

- [ ] 修改 `ImageGenerationService.handleTaskSuccess`
- [ ] 修改 `VideoGenerationService.handleTaskSuccess`
- [ ] 实现 `createAssetFromTask` 工具函数
- [ ] 添加错误处理和重试机制

**关键代码**:

```typescript
// 在 Task 完成时调用
async createAssetFromTask(task: Task | VideoTask): Promise<Asset> {
  const assetData = {
    userId: task.userId,
    type: task.type === "image" ? "image/jpeg" : "video/mp4", // MIME type
    sourceType: "ai_generated",
    sourceTaskId: task.taskId,
    generationPrompt: task.prompt || task.inputData?.prompt,
    url: task.resultImageUrls?.[0] || task.videoUrl,
    thumbnailUrl: task.resultImageUrls?.[0] || task.coverImageUrl,
    name: `generated-${task.taskId}.${task.type === "image" ? "jpg" : "mp4"}`,
    size: 0, // 需要从实际文件获取
    // ... 其他字段
  };
  return await assetService.createAsset(task.userId, assetData);
}
```

#### 1.3 创建 Explore 基础结构

**任务**: 创建 Explore 功能的基础文件结构

**具体工作**:

- [x] 创建 `src/features/explore/` 目录
- [x] 实现 `explore.interface.ts`
- [x] 实现 `explore.schema.ts`
- [x] 实现基础的 `explore.service.ts`

**实现细节**:

```typescript
// explore.interface.ts - 定义服务接口
export abstract class ExploreService {
  abstract getExploreContent(query: ExploreQuery): Promise<ExploreResponse>;
  abstract getUserInteractionStatus(
    userId: string,
    assetIds: string[]
  ): Promise<Map<string, UserInteractionState>>;
}

// explore.schema.ts - 定义数据验证和类型
export interface ExploreQuery {
  page?: number;
  limit?: number;
  type?: string; // MIME type filter
  sourceType?: "ai_generated" | "user_upload" | "all";
  sortBy?: "latest" | "popular";
}

export interface ExploreItem {
  id: string;
  type: string;
  sourceType: "ai_generated" | "user_upload";
  userId: string;
  userDisplayName?: string;
  userPhotoURL?: string;
  url: string;
  thumbnailUrl?: string;
  name?: string;
  size?: number;
  generationPrompt?: string;
  likeCount: number;
  favoriteCount: number;
  createdAt: string;
}

// explore.service.ts - 核心业务逻辑实现
export class FirestoreExploreService implements ExploreService {
  async getExploreContent(query: ExploreQuery): Promise<ExploreResponse> {
    // 1. 查询公开的 Asset 记录
    // 2. 关联用户信息
    // 3. 应用过滤和排序
    // 4. 返回格式化结果
  }
}
```

### Phase 2: 核心功能 (第 3-4 周) ✅ 已完成

#### 2.1 实现 Explore Service

**任务**: 实现核心的 Explore 业务逻辑

**具体工作**:

- [x] 实现 `getExploreContent` 方法
- [x] 实现用户信息关联查询
- [x] 实现分页和排序逻辑
- [x] 实现内容类型过滤

**实现细节**:

```typescript
export class FirestoreExploreService implements ExploreService {
  async getExploreContent(query: ExploreQuery): Promise<ExploreResponse> {
    // 1. 构建查询条件 - 基于现有的 AssetService.getPublicAssets
    const assetQuery: AssetListQuery = {
      page: query.page || 1,
      limit: query.limit || 20,
      type: query.type,
      // 只查询公开的AI生成内容
      status: "active",
    };

    // 2. 查询公开资产
    const assetResponse = await this.assetService.getPublicAssets(assetQuery);

    // 3. 过滤 sourceType
    let filteredAssets = assetResponse.assets;
    if (query.sourceType && query.sourceType !== "all") {
      filteredAssets = filteredAssets.filter(
        (asset) => asset.sourceType === query.sourceType
      );
    }

    // 4. 应用排序
    if (query.sortBy === "popular") {
      filteredAssets.sort(
        (a, b) =>
          b.likeCount + b.favoriteCount - (a.likeCount + a.favoriteCount)
      );
    }

    // 5. 批量获取用户信息
    const userIds = [...new Set(filteredAssets.map((asset) => asset.userId))];
    const userInfoMap = await this.getUserInfoBatch(userIds);

    // 6. 转换为 ExploreItem 格式
    const items: ExploreItem[] = filteredAssets.map((asset) => ({
      id: asset.id,
      type: asset.type,
      sourceType: asset.sourceType,
      userId: asset.userId,
      userDisplayName: userInfoMap.get(asset.userId)?.displayName,
      userPhotoURL: userInfoMap.get(asset.userId)?.photoURL,
      url: asset.url,
      thumbnailUrl: asset.thumbnailUrl,
      name: asset.name,
      size: asset.size,
      generationPrompt: asset.generationPrompt,
      likeCount: asset.likeCount,
      favoriteCount: asset.favoriteCount,
      createdAt: asset.createdAt.toISOString(),
    }));

    return {
      items,
      pagination: assetResponse.pagination,
      filters: {
        totalCounts: {
          image: filteredAssets.filter((a) => a.type.startsWith("image/"))
            .length,
          video: filteredAssets.filter((a) => a.type.startsWith("video/"))
            .length,
          total: filteredAssets.length,
        },
      },
    };
  }
}
```

#### 2.2 实现 Explore API 端点

**任务**: 创建 Explore 的 REST API 端点

**具体工作**:

- [x] 实现 `endpoints/get-explore-content.ts`
- [x] 添加请求参数验证
- [x] 实现响应格式化
- [x] 添加错误处理

**实现细节**:

```typescript
// endpoints/get-explore-content.ts
export const GetExploreContentEndpoint = createRoute({
  method: "get",
  path: "/explore",
  summary: "获取 Explore 内容",
  description: "获取公开的用户生成内容列表，支持分页和过滤",
  request: {
    query: ExploreQuerySchema,
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: ExploreResponseSchema,
        },
      },
      description: "成功获取 Explore 内容",
    },
    400: {
      content: {
        "application/json": {
          schema: ErrorResponseSchema,
        },
      },
      description: "请求参数无效",
    },
  },
})(async (c) => {
  try {
    const query = c.req.valid("query");
    const exploreService = container.resolve(IExploreService);

    const result = await exploreService.getExploreContent(query);

    return c.json(result, 200);
  } catch (error) {
    console.error("[GetExploreContent] Error:", error);
    return c.json(
      {
        error: "Failed to fetch explore content",
        code: "INTERNAL_ERROR",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      500
    );
  }
});
```

#### 2.3 注册路由

**任务**: 将 Explore 路由集成到主应用

**具体工作**:

- [x] 创建 `explore.routes.ts`
- [x] 在 `src/index.ts` 中注册路由
- [x] 配置 OpenAPI 文档

**实现细节**:

```typescript
// explore.routes.ts
export function registerExploreRoutes(openapi: any, basePath: string) {
  // GET /explore - 获取 Explore 内容（无需认证）
  openapi.get(basePath, GetExploreContentEndpoint);

  // GET /explore/:assetId/interaction-status - 获取用户交互状态（需要认证）
  openapi.get(
    `${basePath}/:assetId/interaction-status`,
    authMiddleware,
    GetInteractionStatusEndpoint
  );
}

// 在 src/index.ts 中注册
registerExploreRoutes(openapi, "/api/v1/explore");
```

### Phase 3: 社交功能 (第 5-6 周) ✅ 已完成

#### 3.1 创建社交交互系统

**任务**: 实现点赞、收藏等社交功能

**具体工作**:

- [x] 创建 `src/features/social/` 目录
- [x] 实现 `AssetInteraction` 数据模型
- [x] 实现 `SocialService` 接口和 Firestore 实现
- [x] 创建 Firestore 集合和索引设计

**数据结构**:

```typescript
export interface AssetInteraction {
  id: string;
  assetId: string;
  userId: string;
  type: "like" | "favorite" | "view";
  createdAt: Date;
  metadata?: Record<string, any>; // 扩展信息
}
```

#### 3.2 实现社交 API 端点

**任务**: 创建社交交互的 API 端点

**具体工作**:

- [x] 实现 `endpoints/like-asset.ts` - 点赞/取消点赞
- [x] 实现 `endpoints/favorite-asset.ts` - 收藏/取消收藏
- [x] 实现 `endpoints/get-user-interactions.ts` - 用户交互历史
- [x] 实现 `endpoints/get-asset-stats.ts` - 资产统计信息
- [x] 添加认证中间件和权限控制

#### 3.3 统计更新机制

**任务**: 实现社交统计的实时更新

**具体工作**:

- [x] 实现事务性统计更新（使用 Firestore 事务）
- [x] 添加防重复操作机制
- [x] 实现批量查询优化
- [x] 集成到 ExploreService 中

### Phase 4: 优化和完善 (第 7-8 周)

#### 4.1 性能优化

**任务**: 优化查询性能和响应速度

**具体工作**:

- [ ] 创建 Firestore 复合索引
- [ ] 实现热门内容缓存
- [ ] 优化用户信息批量查询
- [ ] 添加分页性能优化

#### 4.2 用户体验优化

**任务**: 改善用户交互体验

**具体工作**:

- [ ] 实现用户交互状态查询
- [ ] 添加内容预加载机制
- [ ] 优化图片加载和缓存

#### 4.3 监控和分析

**任务**: 添加监控和数据分析

**具体工作**:

- [ ] 添加 API 性能监控
- [ ] 实现用户行为分析
- [ ] 添加错误监控和告警

## 技术实现细节

### 数据库设计

#### Firestore 集合结构

```
assets/
├── {assetId}/
│   ├── contentType: "image" | "video"
│   ├── sourceType: "ai_generated" | "user_upload"
│   ├── sourceTaskId: string
│   ├── generationPrompt: string
│   ├── likeCount: number
│   ├── favoriteCount: number
│   └── isPublic: boolean

asset_interactions/
├── {interactionId}/
│   ├── assetId: string
│   ├── userId: string
│   ├── type: "like" | "favorite"
│   └── createdAt: timestamp
```

#### 索引策略

```javascript
// 主要查询索引
assets: [
  ["isPublic", "sourceType", "contentType", "createdAt"],
  ["sourceType", "likeCount"],
  ["contentType", "likeCount"],
  ["userId", "sourceType", "contentType"],
];

asset_interactions: [
  ["assetId", "type"],
  ["userId", "type", "createdAt"],
  ["userId", "assetId"],
];
```

### 关键算法

#### 热门内容排序

```typescript
// 基于点赞数和时间的综合排序
function calculatePopularityScore(asset: Asset): number {
  const ageInHours =
    (Date.now() - asset.createdAt.getTime()) / (1000 * 60 * 60);
  const timeDecay = Math.exp(-ageInHours / 24); // 24小时衰减
  return (asset.likeCount * 10 + asset.favoriteCount * 5) * timeDecay;
}
```

#### 用户交互状态查询优化

```typescript
// 批量查询用户交互状态，避免 N+1 问题
async function getUserInteractionStates(
  userId: string,
  assetIds: string[]
): Promise<Map<string, UserInteractionState>> {
  const interactions = await this.socialService.getUserInteractions(
    userId,
    assetIds
  );
  return new Map(interactions.map((i) => [i.assetId, i]));
}
```

## 测试计划

### 单元测试

- [ ] Asset Schema 验证测试
- [ ] ExploreService 业务逻辑测试
- [ ] SocialService 交互逻辑测试
- [ ] 工具函数测试

### 集成测试

- [ ] API 端点测试
- [ ] 数据库操作测试
- [ ] 认证和权限测试
- [ ] 错误处理测试

### 性能测试

- [ ] 大量数据查询性能测试
- [ ] 并发点赞操作测试
- [ ] 缓存效果测试

## 部署计划

### 测试环境部署

- [ ] 配置测试环境数据库
- [ ] 部署 API 服务
- [ ] 配置监控和日志

### 生产环境准备

- [ ] 数据库索引创建
- [ ] 缓存配置
- [ ] 监控告警配置

## 风险控制

### 技术风险

- **数据一致性**: 使用 Firestore 事务确保统计数据一致性
- **性能问题**: 提前创建索引，实现缓存策略
- **并发冲突**: 实现乐观锁和重试机制

### 业务风险

- **内容质量**: 实现内容审核机制
- **用户隐私**: 严格的权限控制
- **恶意行为**: 防刷机制和行为分析

## 成功标准

### 技术指标

- API 响应时间 < 500ms (P95)
- 系统可用性 > 99.9%
- 错误率 < 0.1%

### 业务指标

- 用户参与度提升 20%
- 内容浏览量增长 50%
- 用户留存率提升 15%

## 实现总结

### 已完成功能 ✅

#### Phase 1: 基础架构

- [x] **Asset Schema 扩展**: 已完成，Asset 系统已支持所有必需字段
- [x] **Asset 自动创建机制**: 已实现，Task 完成时自动创建 Asset 记录
- [x] **Explore 基础结构**: 已创建完整的文件结构和接口定义

#### Phase 2: 核心功能

- [x] **ExploreService 实现**: 完整的业务逻辑实现
  - 基于现有 AssetService.getPublicAssets 构建
  - 支持分页、过滤、排序
  - 批量用户信息查询优化
  - 完善的错误处理
- [x] **API 端点实现**: 两个核心端点
  - `GET /api/v1/explore` - 获取 Explore 内容（无需认证）
  - `GET /api/v1/explore/:assetId/interaction-status` - 获取交互状态（需认证）
- [x] **路由注册**: 完整集成到主应用
- [x] **容器注册**: 依赖注入配置完成
- [x] **文档和测试**: README 和 API 测试文件

### 技术实现亮点

1. **架构复用**: 基于现有 Asset 系统构建，避免重复开发
2. **性能优化**: 批量用户信息查询，避免 N+1 问题
3. **类型安全**: 完整的 TypeScript 类型定义和 Zod 验证
4. **错误处理**: 统一的错误响应格式和详细错误信息
5. **扩展性**: 为 Phase 3 社交功能预留接口

### 文件结构

```
src/features/explore/
├── explore.interface.ts          # 服务接口定义
├── explore.service.ts           # 核心业务逻辑实现
├── explore.schema.ts            # 数据验证和类型定义
├── explore.routes.ts            # 路由注册
├── endpoints/
│   ├── get-explore-content.ts   # 获取 Explore 内容端点
│   └── get-interaction-status.ts # 获取用户交互状态端点
├── api.http                     # API 测试文件
└── README.md                    # 功能文档
```

#### Phase 3: 社交功能 ✅ 已完成

- [x] **AssetInteraction 数据模型**: 完整的社交交互数据结构
- [x] **SocialService 实现**: 包含所有社交功能的服务层
- [x] **点赞/收藏功能**: 完整的 API 端点和事务性统计更新
- [x] **用户交互历史**: 支持分页的用户点赞和收藏列表查询
- [x] **资产统计查询**: 实时的社交统计信息
- [x] **ExploreService 集成**: 真实的用户交互状态查询
- [x] **路由和依赖注入**: 完整的系统集成
- [x] **API 测试和文档**: 完整的测试用例和使用文档

### 技术实现亮点 (Phase 3)

1. **事务性操作**: 使用 Firestore 事务确保统计数据一致性
2. **批量查询优化**: 避免 N+1 问题，支持高效的用户交互状态查询
3. **防重复机制**: 智能处理重复点赞/收藏操作
4. **完整集成**: 与现有 Asset 和 Explore 系统无缝集成
5. **扩展性设计**: 为未来社交功能预留扩展空间

### 文件结构 (更新)

```
src/features/
├── explore/
│   ├── explore.interface.ts          # 服务接口定义
│   ├── explore.service.ts           # 核心业务逻辑实现（已集成社交功能）
│   ├── explore.schema.ts            # 数据验证和类型定义
│   ├── explore.routes.ts            # 路由注册
│   ├── endpoints/
│   │   ├── get-explore-content.ts   # 获取 Explore 内容端点
│   │   └── get-interaction-status.ts # 获取用户交互状态端点
│   ├── api.http                     # API 测试文件
│   └── README.md                    # 功能文档
└── social/                          # 新增社交功能模块
    ├── social.interface.ts          # 社交服务接口定义
    ├── social.schema.ts             # 社交数据验证和类型定义
    ├── firestore-social.service.ts  # Firestore 社交服务实现
    ├── social.routes.ts             # 社交路由注册
    ├── endpoints/
    │   ├── like-asset.ts            # 点赞功能端点
    │   ├── favorite-asset.ts        # 收藏功能端点
    │   ├── get-user-interactions.ts # 用户交互历史端点
    │   └── get-asset-stats.ts       # 资产统计端点
    ├── api.http                     # 社交功能 API 测试文件
    └── README.md                    # 社交功能文档
```

### 下一步计划

**Phase 4: 优化和完善** (可选)

- 性能优化：创建 Firestore 复合索引
- 缓存策略：热门内容缓存
- 监控分析：用户行为分析和性能监控
- 高级功能：分享功能、浏览统计、推荐算法

---

_实现计划版本: 1.2_
_制定日期: 2024-01-15_
_Phase 1-2 完成日期: 2025-01-02_
_Phase 3 完成日期: 2025-01-02_
_项目状态: 核心功能完成，可投入生产使用_
