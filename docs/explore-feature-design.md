# Explore 功能设计方案

## 概述

基于现有的 Asset 系统扩展实现 Explore 功能，展示用户创建的公开图片和视频内容。通过扩展 Asset 数据结构和添加社交交互功能，为用户提供内容发现和社交体验。

## 设计原则

1. **复用现有架构**：基于现有的 Asset 系统扩展，避免重复开发
2. **数据一致性**：所有用户内容统一在 Asset 系统中管理
3. **扩展性优先**：为未来的社交功能预留扩展空间
4. **性能考虑**：合理的数据结构设计，支持高效查询

## 核心架构

### 1. 数据流程

```
AI 生成任务完成 → 自动创建 Asset 记录 → Explore 接口展示公开内容
```

### 2. 数据结构扩展

#### A. Asset 表扩展

**外层字段**（核心业务字段）：

```typescript
export interface Asset {
  // ... 现有字段

  // Web 标准字段 (遵循 File API)
  name?: string; // 原 originalFilename
  size?: number; // 原 fileSize
  type?: string; // MIME type (原 mimeType)
  url?: string; // 原 storageUrl

  // 业务分类字段
  sourceType?: "ai_generated" | "user_upload";
  sourceTaskId?: string; // 关联的任务ID（仅AI生成内容）
  generationPrompt?: string; // AI生成的prompt（仅AI生成内容）

  // 社交统计（基础计数）
  likeCount?: number;
  favoriteCount?: number;
}
```

**metadata 字段**（扩展信息）：

```typescript
metadata: {
  // AI生成相关详细信息
  generationProvider?: string;           // AI提供商 (openai, vidu, runway等)
  originalTaskData?: Record<string, any>; // 原始任务数据备份

  // 其他扩展信息
  [key: string]: any;
}
```

#### B. 社交交互表（独立表）

```typescript
// AssetInteraction 表
export interface AssetInteraction {
  id: string;
  assetId: string; // 关联 Asset.id
  userId: string; // 操作用户ID
  type: "like" | "favorite";
  createdAt: Date;
  metadata?: Record<string, any>; // 扩展信息（如分享平台等）
}

// 复合索引：(assetId, type), (userId, type), (userId, assetId)
```

### 3. API 设计

#### A. Explore 接口

```typescript
GET /api/v1/explore
Query Parameters:
- page: number (默认 1)
- limit: number (默认 20, 最大 50)
- type: string (MIME type 过滤，如 "image/*", "video/*")
- sourceType: "ai_generated" | "user_upload" | "all" (默认 "all")
- sortBy: "latest" | "popular" | "trending" (默认 "latest")
- category?: string (可选分类过滤)

Response:
{
  items: ExploreItem[],
  pagination: PaginationMeta,
  filters: {
    availableCategories: string[],
    totalCounts: {
      image: number,
      video: number,
      total: number
    }
  }
}
```

#### B. 社交交互接口

```typescript
// 点赞/取消点赞
POST /api/v1/assets/:assetId/like
DELETE /api/v1/assets/:assetId/like

// 收藏/取消收藏
POST /api/v1/assets/:assetId/favorite
DELETE /api/v1/assets/:assetId/favorite

// 获取用户的点赞/收藏列表
GET /api/v1/users/me/likes
GET /api/v1/users/me/favorites

// 获取资产的交互统计
GET /api/v1/assets/:assetId/stats
```

### 4. 数据类型定义

```typescript
// Explore 响应项
export interface ExploreItem {
  id: string;
  type: string; // MIME type (如 "image/jpeg", "video/mp4")
  sourceType: "ai_generated" | "user_upload";
  userId: string;
  userDisplayName?: string;
  userPhotoURL?: string;

  // 内容信息 (Web 标准)
  url: string; // 原 storageUrl
  thumbnailUrl?: string;
  name?: string; // 原 originalFilename
  size?: number; // 原 fileSize
  generationPrompt?: string; // 仅AI生成内容

  // 统计信息
  likeCount: number;
  favoriteCount: number;

  // 时间信息
  createdAt: string;
}

// 社交统计
export interface AssetSocialStats {
  likeCount: number;
  favoriteCount: number;
  shareCount: number;
  viewCount: number;
  recentLikes: UserInfo[]; // 最近点赞的用户
}
```

## 实现计划

### Phase 1: 基础 Explore 功能

1. **扩展 Asset Schema**

   - 添加 sourceType, sourceTaskId, generationPrompt 字段
   - 添加基础的社交统计字段
   - 更新 Zod 验证 schema

2. **修改任务完成流程**

   - 在 ImageGenerationService 中添加 Asset 创建逻辑
   - 在 VideoGenerationService 中添加 Asset 创建逻辑
   - 确保生成的内容自动创建对应的 Asset 记录

3. **实现 Explore 接口**
   - 创建 ExploreService
   - 实现基于 getPublicAssets 的内容查询
   - 添加用户信息关联查询

### Phase 2: 社交交互功能

1. **创建社交交互系统**

   - 创建 AssetInteraction 表和 Service
   - 实现点赞/收藏功能
   - 实现统计更新机制

2. **用户交互状态**
   - 在 Explore 接口中返回用户交互状态
   - 实现用户的点赞/收藏列表查询

## 技术考虑

### 1. 性能优化

- **索引策略**：为 sourceType, isPublic, createdAt 等字段创建复合索引
- **缓存策略**：热门内容使用 Cloudflare KV 缓存
- **分页优化**：使用 cursor-based 分页替代 offset-based
- **图片优化**：自动生成多种尺寸的缩略图
- **CDN 加速**：利用 Cloudflare R2 的全球 CDN 加速内容分发

### 2. 数据一致性

- **统计更新**：使用事务确保点赞计数的一致性
- **异步处理**：统计更新可以异步处理，提高响应速度
- **数据同步**：Task 完成后自动创建 Asset 记录的同步机制
- **失败重试**：Asset 创建失败时的重试机制

### 3. 安全考虑

- **内容审核**：AI 生成内容的自动审核
- **用户隐私**：用户可以控制内容的公开状态
- **防刷机制**：防止恶意点赞/收藏
- **访问控制**：确保只有公开内容才能在 Explore 中展示
- **内容过滤**：敏感内容检测和过滤机制

### 4. 监控和分析

- **用户行为分析**：跟踪用户在 Explore 中的行为
- **内容质量监控**：监控生成内容的质量和用户反馈
- **性能监控**：API 响应时间和错误率监控

## 数据库设计

### Firestore 集合结构

```
assets/
├── {assetId}/
│   ├── id: string
│   ├── userId: string
│   ├── sourceType: string
│   ├── sourceTaskId?: string
│   ├── generationPrompt?: string
│   ├── likeCount: number
│   ├── favoriteCount: number
│   └── ... (其他现有字段)

asset_interactions/
├── {interactionId}/
│   ├── assetId: string
│   ├── userId: string
│   ├── type: string
│   └── createdAt: timestamp
```

### 索引策略

```javascript
// Assets 集合索引
- (isPublic, sourceType, createdAt DESC)
- (userId, sourceType, createdAt DESC)
- (sourceType, likeCount DESC)

// AssetInteractions 集合索引
- (assetId, type)
- (userId, type, createdAt DESC)
- (userId, assetId)
```

## 实施细节

### 1. 文件结构

```
src/features/explore/
├── explore.interface.ts          # 服务接口定义
├── explore.service.ts           # 核心业务逻辑
├── explore.schema.ts            # 数据验证 schema
├── explore.routes.ts            # 路由注册
└── endpoints/
    ├── get-explore-content.ts   # 获取 Explore 内容
    ├── like-asset.ts           # 点赞功能
    ├── favorite-asset.ts       # 收藏功能
    └── get-user-interactions.ts # 用户交互历史

src/features/social/
├── social.interface.ts          # 社交功能接口
├── social.service.ts           # 社交交互逻辑
└── social.schema.ts            # 社交数据 schema
```

### 2. 关键实现点

#### A. Asset 自动创建机制

```typescript
// 在 Task 完成时自动创建 Asset
async function onTaskCompleted(task: Task | VideoTask) {
  if (task.status === "succeeded") {
    await createAssetFromTask(task);
  }
}
```

