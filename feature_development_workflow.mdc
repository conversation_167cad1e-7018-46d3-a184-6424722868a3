---
description: 
globs: 
alwaysApply: false
---
## Core Technologies

- **Framework:** <PERSON>o ([src/index.ts](mdc:src/index.ts))
- **Language:** TypeScript ([tsconfig.json](mdc:tsconfig.json))
- **OpenAPI & Routing Augmentation:** `chanfana` (used for `OpenAPIRoute`, e.g., [src/features/user/endpoints/create-user.ts](mdc:src/features/user/endpoints/create-user.ts))
- **Dependency Injection:** tsyringe ([src/index.ts](mdc:src/index.ts))

5.  **Define Endpoints & Routes (`src/features/product/endpoints/` and `src/features/product/product.routes.ts`):**
    -   **Create Endpoint Handler Classes (e.g., `src/features/product/endpoints/create-product.ts`):**
        -   For each API operation (create, get, update, delete), create a class (e.g., `CreateProductEndpoint`) that extends `OpenAPIRoute` from `chanfana`.
        -   Inside each class, define a `schema` property. This property will hold:
            -   OpenAPI metadata like `summary`, `description`, `tags`.
            -   Request validation details (e.g., `request.body`, `request.params`) using Zod schemas imported from your `product.schema.ts`.
            -   Response definitions (e.g., `responses['200']`, `responses['400']`) also using Zod schemas.
        -   Implement an asynchronous `handle(c: Context)` method within the class:
            -   Use `const { body, params } = await this.getValidatedData<typeof this.schema>();` to get validated request data based on the class's `schema` property.
            -   Resolve your service instance: `const productService = container.resolve(ProductService);`.
            -   Call the appropriate service method with the validated data.
            -   Return the response using `c.json()` (e.g., `return c.json(result, 200);`).
            -   Implement error handling using `try...catch` blocks, returning appropriate error responses (e.g., `c.json({ error: '...' }, 500);`).

    -   **Create a Route Registration File (e.g., `src/features/product/product.routes.ts`):**
        -   Create a function, for example, `registerProductRoutes(openapi: any, routePrefix: string = "/products")`.
            -   The `openapi` parameter will typically be your Hono application instance or a router instance capable of registering OpenAPI routes.
        -   Inside this function, register your endpoint handler classes to specific HTTP methods and paths using the `openapi` object.
            -   Example: `openapi.post(baseRoutePrefix, CreateProductEndpoint);`
            -   Example: `openapi.get(\`${baseRoutePrefix}/:id\`, GetProductByIdEndpoint);`
        -   Export this registration function.

6.  **Mount Routes (`src/index.ts`):**
    - Import your `registerProductRoutes` function from `src/features/product/product.routes.ts`.
    - Call the registration function, passing your Hono app instance: `registerProductRoutes(app, '/products');` (adjust base path `/products` as needed).
