---
description: develop a new feature
globs: 
alwaysApply: false
---
# Feature Development Workflow

This guide outlines the standard process for developing new features in the chat-to-design-backend project, adhering to its architecture and tooling.

## Core Technologies

- **Framework:** Hono ([src/index.ts](mdc:src/index.ts))
- **Language:** TypeScript ([tsconfig.json](mdc:tsconfig.json))
- **Dependency Injection:** tsyringe ([src/index.ts](mdc:src/index.ts))
- **Testing:** Vitest ([src/test/](mdc:src/test))
- **Package Manager:** pnpm ([package.json](mdc:package.json))
- **Data Validation:** Zod (used within features, e.g., [src/features/user/user.schema.ts](mdc:src/features/user/user.schema.ts))

## Architecture Overview

- **`src/features/<feature-name>`:** Contains all domain-specific logic for a feature (e.g., user, chat). This includes:
    - `*.routes.ts`: Hono route definitions.
    - `*.service.ts`: Business logic implementation.
    - `*.schema.ts`: Zod schemas for request/response validation and type definitions.
    - (Optional) `*.controller.ts`: If logic separation from routes is complex.
- **`src/infrastructure`:** Houses shared components like database access ([src/infrastructure/firebase/firebase.ts](mdc:src/infrastructure/firebase/firebase.ts)), AI services ([src/infrastructure/ai/GeminiService.ts](mdc:src/infrastructure/ai/GeminiService.ts)), storage ([src/infrastructure/storage/StorageService.ts](mdc:src/infrastructure/storage/StorageService.ts)), etc.
- **`src/test`:** Contains test files, mirroring the `src` structure.

## New Feature Development Steps

Let's say you are adding a "product" feature:

1.  **Create Directories:**
    - `src/features/product/`
    - `src/test/features/product/`

2.  **Define Schemas & Types (`src/features/product/product.schema.ts`):**
    - Use Zod to define schemas for API request bodies, parameters, and responses.
    - Export inferred TypeScript types from these schemas (`z.infer<typeof schema>`).

3.  **Implement Service (`src/features/product/product.service.ts`):**
    - Create the `ProductService` class containing the core business logic.
    - Decorate the class with `@singleton()` for DI registration with tsyringe.
    - Inject dependencies (like `FirebaseService`, other feature services) through the constructor. Ensure injected services are also registered with tsyringe (usually in `src/index.ts` or a dedicated DI setup file).
    - Use schemas defined in step 2 for function parameters and return types where applicable.

4.  **Register Service for DI (`src/index.ts`):**
    - Import the new `ProductService`.
    - Register it using `container.registerSingleton(ProductService);`.

5.  **Define Routes (`src/features/product/product.routes.ts`):**
    - Create a new Hono instance (`const productRoutes = new Hono();`).
    - Define API endpoints (e.g., `productRoutes.get('/', ...)`).
    - Use `zValidator` from `@hono/zod-validator` to validate requests against your Zod schemas.
    - Inside the route handler:
        - Resolve the service instance: `const productService = container.resolve(ProductService);`
        - Call the appropriate service methods with validated data (`c.req.valid('json')` or `c.req.valid('param')`).
        - Return responses using `c.json()`.        
    - Export the `productRoutes` instance.

6.  **Mount Routes (`src/index.ts`):**
    - Import your `productRoutes` from `src/features/product/product.routes.ts`.
    - Mount it under the main API router: `api.route('/products', productRoutes);` (adjust base path `/products` as needed).

7.  **Write Tests (`src/test/features/product/`):**
    - **Integration/API Tests (`*.api.test.ts`):**
        - Use `testClient` from `hono/testing` to make requests to your new API endpoints ([src/test/userApi.test.ts](mdc:src/test/userApi.test.ts) is a good example).
        - Test the full request/response cycle, including validation and interaction with the actual (or mocked) service layer.
        - Set up and tear down test data if necessary.

8.  **Code Standards:**
    - Follow existing code style.
    - Write comments **only in English** and only when necessary for clarification.
    - Use `pnpm` for package management.

## Testing Guidelines

- **Focus:** Prioritize testing business logic in services and the API contract through integration tests.
- **Coverage:** Aim for coverage of critical paths and edge cases. Don't just chase percentage numbers.
- **File Naming:** Use `*.test.ts` or `*.spec.ts` suffixes. Keep test files alongside the code they test within the `src/test` mirrored structure.
