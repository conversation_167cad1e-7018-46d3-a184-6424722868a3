# AppSolve Backend Service

AppSolve is a comprehensive backend service built with Hono and running on Cloudflare Workers. It provides AI-powered content generation, asset management, social features, and more.

## 🚀 Quick Startup

### Prerequisites

- Node.js 18+
- pnpm (`npm install -g pnpm`)
- Cloudflare account

### 1. Clone & Install

```bash
git clone <repository-url>
cd appsolve-backend
pnpm install
```

### 2. Environment Setup

Create `.dev.vars` file in root:

```bash
# Firebase
FIREBASE_PROJECT_ID="your-project-id"

# AI Services
GOOGLE_API_KEY="your-google-api-key"
OPENAI_API_KEY="your-openai-key"

# Storage
R2_BUCKET_NAME="your-r2-bucket"
R2_ACCESS_KEY_ID="your-r2-access-key"
R2_SECRET_ACCESS_KEY="your-r2-secret"

# Rate Limiting
UPSTASH_REDIS_REST_URL="your-upstash-url"
UPSTASH_REDIS_REST_TOKEN="your-upstash-token"

# Notion (optional)
NOTION_API_KEY="your-notion-key"
```

### 3. Start Development

```bash
pnpm run dev
```

Server runs at `http://localhost:8787`

### 4. API Documentation

Visit `http://localhost:8787/docs` for interactive API documentation.

## Table of Contents

- [AppSolve Backend Service](#appsolve-backend-service)
  - [🚀 Quick Startup](#-quick-startup)
    - [Prerequisites](#prerequisites)
    - [1. Clone \& Install](#1-clone--install)
    - [2. Environment Setup](#2-environment-setup)
    - [3. Start Development](#3-start-development)
    - [4. API Documentation](#4-api-documentation)
  - [Table of Contents](#table-of-contents)
  - [Tech Stack](#tech-stack)
  - [项目结构](#项目结构)
  - [详细安装设置](#详细安装设置)
    - [前置要求](#前置要求)
    - [安装步骤](#安装步骤)
  - [本地开发](#本地开发)
  - [生产环境构建](#生产环境构建)
  - [部署](#部署)
  - [运行测试](#运行测试)
  - [环境变量和密钥](#环境变量和密钥)
  - [主要功能](#主要功能)
    - [🎨 AI 内容生成](#-ai-内容生成)
    - [📁 资产管理](#-资产管理)
    - [🌟 Explore 功能](#-explore-功能)
    - [👥 社交功能](#-社交功能)
    - [🔐 认证与授权](#-认证与授权)
    - [🔗 集成服务](#-集成服务)
  - [API 端点](#api-端点)
    - [📖 API 文档](#-api-文档)
    - [🔑 主要端点](#-主要端点)
      - [认证](#认证)
      - [资产管理](#资产管理)
      - [Explore 功能](#explore-功能)
      - [AI 生成](#ai-生成)
      - [社交功能](#社交功能)
      - [文件上传](#文件上传)
    - [🔐 认证](#-认证)
    - [📊 响应格式](#-响应格式)

## Tech Stack

- **Runtime/Framework**: Node.js, Hono, Cloudflare Workers
- **Language**: TypeScript
- **Package Manager**: pnpm
- **Authentication**: Firebase Authentication (`@hono/firebase-auth`)
- **Database**: Firebase Firestore (`firebase-rest-firestore`)
- **Storage**: Cloudflare R2
- **AI Services**:
  - `@google/generative-ai`: Google Gemini
  - `openai`: OpenAI GPT/DALL-E
  - Multiple AI providers for image/video generation
- **API and Backend Services**:
  - `@notionhq/client`: Notion CMS integration
  - `@upstash/ratelimit` & `@upstash/redis`: Rate limiting and caching
- **Image Processing**: `sharp`
- **Validation**: `zod`, `@hono/zod-validator`
- **Dependency Injection**: `tsyringe`, `reflect-metadata`
- **API Documentation**: `chanfana` (OpenAPI/Swagger)
- **Build Tools**: Vite, esbuild
- **Testing**: Vitest, tsx
- **Deployment**: Cloudflare Wrangler

## 项目结构

```
appsolve-backend/
├── .github/            # GitHub Actions workflows
├── .wrangler/          # Wrangler local development state
├── docs/               # Documentation
├── public/             # Static assets
│   └── static/
├── src/                # Source code
│   ├── features/       # Feature modules
│   │   ├── assets/     # Asset management (files, metadata)
│   │   ├── auth/       # Authentication & authorization
│   │   ├── explore/    # Public content discovery
│   │   ├── file-upload/ # File upload & storage
│   │   ├── image-generation/ # AI image generation
│   │   ├── video-generation/ # AI video generation
│   │   ├── notion/     # Notion CMS integration
│   │   ├── social/     # Social features (likes, favorites)
│   │   ├── user/       # User management
│   │   └── webhook/    # External webhooks
│   ├── infrastructure/ # Core infrastructure
│   │   ├── ai/         # AI service providers
│   │   ├── cache/      # Caching layer
│   │   ├── db/         # Database services
│   │   ├── env/        # Environment configuration
│   │   └── storage/    # File storage services
│   ├── lib/            # Shared utilities
│   ├── middleware/     # HTTP middleware
│   └── types/          # TypeScript type definitions
├── .dev.vars           # Local development environment variables
├── .env.example        # Environment variables template
├── package.json        # Dependencies and scripts
├── pnpm-lock.yaml
├── README.md
├── secrets.json        # Cloudflare Workers secrets template
├── tsconfig.json       # TypeScript configuration
├── vite.config.ts      # Vite configuration
├── vitest.config.ts    # Vitest configuration
└── wrangler.jsonc      # Wrangler configuration
```

## 详细安装设置

### 前置要求

1.  **Node.js**: 版本 18.x 或更高。(建议使用版本管理器如 `nvm`)
2.  **pnpm**: 通过 `npm install -g pnpm` 安装或查看 [pnpm 安装指南](https://pnpm.io/installation)。
3.  **Cloudflare 账户**: 部署到 Cloudflare Workers 时需要。
4.  **Wrangler CLI**: 安装并配置 Cloudflare Wrangler CLI。
    ```bash
    npm install -g wrangler
    wrangler login
    ```
5.  **外部服务**:
    - 为所使用的任何外部服务（例如 Google AI, Notion, Upstash Redis, Firebase）设置账户并获取 API 密钥/凭证。

### 安装步骤

1.  **克隆仓库**:

    ```bash
    git clone <repository-url>
    cd appsolve-backend
    ```

2.  **安装依赖**:

    ```bash
    pnpm install
    ```

3.  **配置环境变量**:
    - **针对 Cloudflare Workers 密钥 (生产/预览环境)**:
      - 复制 `secrets.json.example` (如果存在) 或根据所需服务的密钥 (如 Google AI, Notion, Upstash 等) 创建 `secrets.json`。
      - 用您的实际密钥值填充 `secrets.json`。
      - **重要**: 如果 `secrets.json` 包含真实密钥，则不应将其提交到版本控制系统。它用作 `wrangler secret bulk put` 的源文件。
      - 将密钥上传到 Cloudflare:
        ```bash
        pnpm run secrets:push
        ```
    - **针对使用 Wrangler (`wrangler dev`) 的本地开发**:
      - 在根目录中创建一个 `.dev.vars` 文件。`wrangler dev` 会自动加载此文件。
      - 在此处以 `KEY=VALUE` 格式添加您的开发环境变量。例如:
        ```
        GOOGLE_API_KEY="your_google_api_key"
        NOTION_API_KEY="your_notion_api_key"
        UPSTASH_REDIS_REST_URL="your_upstash_url"
        UPSTASH_REDIS_REST_TOKEN="your_upstash_token"
        # ... 其他变量
        ```
    - **针对其他本地 Node.js 脚本或非 Wrangler 环境**:
      - 将 `.env.example` 复制为 `.env`。
      - 使用您的本地配置值更新 `.env`。如果不是通过 Wrangler 运行，某些测试脚本或自定义 Node.js 脚本会加载此文件。

## 本地开发

使用 Wrangler 启动开发服务器。这通常会在本地运行服务，通常是 `http://localhost:8787` (请检查 Wrangler 输出以获取确切端口)。HMR (热模块替换)应该处于活动状态。

```bash
pnpm run dev
```

或者，对于更接近 Cloudflare 环境的本地预览（但重新加载速度可能较慢）：

```bash
pnpm run preview
```

## 生产环境构建

编译 TypeScript 代码并使用 Vite 打包应用程序 (在 `wrangler.jsonc` 中配置，或者如果 Wrangler 构建过程直接使用 `vite.config.ts` )。

```bash
pnpm run build
```

该命令通常在部署过程中由 Wrangler 调用。

## 部署

将服务部署到 Cloudflare Workers:

```bash
pnpm run deploy
```

此命令将根据 `wrangler.jsonc` 配置构建和部署您的应用程序。请确保您在 Cloudflare 仪表板中的密钥是最新的 (通过 `pnpm run secrets:push` 推送)。

## 运行测试

项目在 `package.json` 中定义了几个测试脚本:

```bash
# 运行特定的测试套件 (示例)
pnpm run test:service
pnpm run test:api
pnpm run test:batch
pnpm run test:image
pnpm run test:chat-service
pnpm run test:chat-api
pnpm run test:message-process
pnpm run test:storage

# 使用 Vitest 运行所有测试 (如果为此配置)
# pnpm vitest
```

有关可用测试命令及其特定用途的完整列表，请参阅 `package.json`。如果测试依赖于某些服务（如本地数据库或模拟服务器），请确保它们正在运行，或者测试已正确模拟。

## 环境变量和密钥

- **`.dev.vars`**: 由 `wrangler dev` 用于本地开发。存储非敏感配置，如果它们不敏感或是占位符，则可以提交。对于实际密钥，请使用您的 shell 或 CI/CD 系统提供的环境变量。
- **`secrets.json`**: 此文件 **不是** `.dev.vars`。它是一个本地 JSON 文件，`wrangler secret bulk put secrets.json` 使用它将密钥上传到您在 Cloudflare 仪表板中的 Worker。**如果 `secrets.json` 包含真实的生产密钥，请勿提交它。** 请使用 `secrets.json.example` 作为模板。
- **Cloudflare 仪表板密钥**:直接在 Cloudflare Workers 仪表板中管理的密钥。这些密钥在运行时注入到您的 Worker 中。使用 `wrangler secret put KEY` 或 `wrangler secret bulk put secrets.json` 来管理这些密钥。
- **`.env`**: 用于可能不通过 `wrangler dev` 运行的通用 Node.js 脚本或本地测试的标准 dotenv 文件。此处的值通常用于本地设置，可能与 `.dev.vars` 重叠，但服务于不同的执行上下文。

## 主要功能

AppSolve 提供以下核心功能模块：

### 🎨 AI 内容生成

- **图像生成**: 支持多个 AI 提供商 (OpenAI DALL-E, Google Imagen 等)
- **视频生成**: AI 驱动的视频内容创建
- **任务管理**: 异步生成任务的状态跟踪

### 📁 资产管理

- **文件上传**: 支持多种文件类型的上传和存储
- **资产元数据**: 完整的文件信息、标签、描述管理
- **公开/私有访问**: 灵活的访问控制机制
- **缩略图生成**: 自动图像缩略图处理

### 🌟 Explore 功能

- **内容发现**: 公开资产的浏览和发现
- **分页和过滤**: 高效的内容浏览体验
- **用户信息集成**: 显示创作者信息

### 👥 社交功能

- **点赞系统**: 用户可以点赞喜欢的内容
- **收藏功能**: 保存感兴趣的资产
- **交互统计**: 实时的社交数据统计

### 🔐 认证与授权

- **Firebase 认证**: 完整的用户认证系统
- **权限控制**: 基于角色的访问控制
- **用户管理**: 用户配置文件和设置

### 🔗 集成服务

- **Notion CMS**: 内容管理系统集成
- **Webhook 支持**: 外部服务集成 (RevenueCat 等)
- **速率限制**: 基于 Upstash Redis 的 API 限流

## API 端点

AppSolve 提供完整的 RESTful API，支持 OpenAPI/Swagger 文档。

### 📖 API 文档

访问 `http://localhost:8787/docs` 查看完整的交互式 API 文档。

### 🔑 主要端点

#### 认证

```http
GET  /api/v1/auth/me                    # 获取当前用户信息
GET  /api/v1/auth/check-permission      # 检查用户权限
```

#### 资产管理

```http
POST /api/v1/assets                     # 创建资产 (需认证)
GET  /api/v1/assets                     # 获取用户资产列表 (需认证)
GET  /api/v1/assets/{id}                # 获取资产详情 (需认证)
GET  /api/v1/assets/{id}/public         # 获取公开资产详情 (无需认证)
PUT  /api/v1/assets/{id}                # 更新资产 (需认证)
DELETE /api/v1/assets/{id}              # 删除资产 (需认证)
```

#### Explore 功能

```http
GET  /api/v1/explore                    # 获取公开内容 (无需认证)
GET  /api/v1/explore/{id}/interaction-status # 获取交互状态 (需认证)
```

#### AI 生成

```http
POST /api/v1/image-generation/generate  # 图像生成 (需认证)
POST /api/v1/video-generation/generate  # 视频生成 (需认证)
GET  /api/v1/tasks/{id}                 # 获取任务状态 (需认证)
```

#### 社交功能

```http
POST   /api/v1/assets/{id}/like         # 点赞资产 (需认证)
DELETE /api/v1/assets/{id}/like         # 取消点赞 (需认证)
POST   /api/v1/assets/{id}/favorite     # 收藏资产 (需认证)
DELETE /api/v1/assets/{id}/favorite     # 取消收藏 (需认证)
GET    /api/v1/users/me/likes           # 获取用户点赞列表 (需认证)
GET    /api/v1/users/me/favorites       # 获取用户收藏列表 (需认证)
```

#### 文件上传

```http
POST /api/v1/file-upload                # 基础文件上传 (需认证)
POST /api/v1/file-upload/with-asset     # 上传并创建资产 (需认证)
```

### 🔐 认证

大部分 API 需要 Firebase JWT 认证：

```http
Authorization: Bearer <firebase-jwt-token>
```

### 📊 响应格式

所有 API 响应都遵循统一的 JSON 格式，错误响应包含详细的错误信息和状态码。
