import type { InjectionToken } from "tsyringe";
import type { VideoTask } from "../features/video-generation/video-generation.schema";

export const IVideoGenerationWorkflowService: InjectionToken<VideoGenerationWorkflowService> =
  Symbol("IVideoGenerationWorkflowService");

/**
 * Abstract class representing the contract for a video generation workflow service.
 * Defines methods for managing video generation tasks through the workflow system.
 */
export abstract class VideoGenerationWorkflowService {
  /**
   * Fetch and validate a video generation task
   * @param taskId - The ID of the task to fetch
   * @returns Promise<Task | null> - The task data or null if not found/invalid
   */
  abstract fetchTask(taskId: string): Promise<VideoTask | null>;

  /**
   * Set a task status to processing
   * @param taskId - The ID of the task to update
   * @returns Promise<void>
   */
  abstract setTaskProcessing(taskId: string): Promise<void>;

  /**
   * Update the progress of a video generation task
   * @param taskId - The ID of the task to update
   * @param progress - Progress percentage (0-100)
   * @returns Promise<void>
   */
  abstract updateTaskProgress(taskId: string, progress: number): Promise<void>;

  /**
   * Update the provider task ID for a task
   * @param taskId - The ID of the task to update
   * @param providerTaskId - The provider's task ID
   * @returns Promise<void>
   */
  abstract updateProviderTaskId(
    taskId: string,
    providerTaskId: string
  ): Promise<void>;

  /**
   * Handle successful completion of a video generation task
   * @param taskId - The ID of the task that succeeded
   * @param result - The result data from the provider
   * @returns Promise<void>
   */
  abstract handleSuccess(taskId: string, result: any): Promise<void>;

  /**
   * Handle failure of a video generation task
   * @param taskId - The ID of the task that failed
   * @param error - The error that occurred
   * @returns Promise<void>
   */
  abstract handleFailure(taskId: string, error: any): Promise<void>;

  /**
   * Execute the complete video generation workflow for a task
   * @param taskId - The ID of the task to process
   * @returns Promise<void>
   */
  abstract executeWorkflow(taskId: string): Promise<void>;
}
