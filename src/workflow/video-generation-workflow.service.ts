import { singleton, inject } from "tsyringe";
import type { DocumentReference } from "firebase-rest-firestore";

import {
  IDbService,
  type DbService,
} from "../infrastructure/db/db-service.interface";
import {
  IProviderRouter,
  ProviderRouter,
} from "../features/video-generation/providers/provider-router";
import type { VideoTask } from "../features/video-generation/video-generation.schema";
import { VideoGenerationWorkflowService as VideoGenerationWorkflowServiceInterface } from "./video-generation-workflow.interface";
import type {
  TaskVideoOutput,
  TaskProvider,
} from "../features/video-generation/providers/provider.interface";

const VIDEO_TASK_COLLECTION = "video-generation-tasks";

/**
 * Video Generation Workflow Service
 * Handles the asynchronous processing of video generation tasks
 */
@singleton()
export class VideoGenerationWorkflowService
  implements VideoGenerationWorkflowServiceInterface
{
  private firestore;

  constructor(
    @inject(IDbService) private dbService: DbService,
    @inject(IProviderRouter) private providerRouter: ProviderRouter
  ) {
    this.firestore = this.dbService.getFirestoreInstance();
  }

  private async _getTaskRef(taskId: string): Promise<DocumentReference> {
    return this.firestore.collection(VIDEO_TASK_COLLECTION).doc(taskId);
  }

  private async _fetchTaskData(
    taskRef: DocumentReference,
    taskId: string
  ): Promise<VideoTask | null> {
    const taskDoc = await taskRef.get();
    if (!taskDoc.exists) {
      console.error(`[VideoWorkflow Task ${taskId}] Task document not found.`);
      return null;
    }
    return taskDoc.data() as VideoTask;
  }

  private async _updateTaskStatus(
    taskRef: DocumentReference,
    status: VideoTask["status"],
    additionalData: Partial<VideoTask> = {}
  ): Promise<void> {
    await taskRef.update({
      status,
      ...additionalData,
      updatedAt: new Date(),
    });
    console.log(
      `[VideoWorkflow Task ${taskRef.id}] Status updated to ${status}.`
    );
  }

  private async _validateTaskData(
    taskData: VideoTask,
    taskRef: DocumentReference,
    taskId: string
  ): Promise<boolean> {
    if (!taskData.provider) {
      console.error(`[VideoWorkflow Task ${taskId}] No provider specified`);
      await this._updateTaskStatus(taskRef, "failed", {
        errorMessage: "No provider specified for video generation",
      });
      return false;
    }

    // Check if prompt exists in inputData
    const prompt = taskData.inputData?.prompt;
    if (!prompt) {
      console.error(
        `[VideoWorkflow Task ${taskId}] No prompt provided in inputData`
      );
      await this._updateTaskStatus(taskRef, "failed", {
        errorMessage: "No prompt provided for video generation",
      });
      return false;
    }

    return true;
  }

  public async fetchTask(taskId: string): Promise<VideoTask | null> {
    const taskRef = await this._getTaskRef(taskId);
    const taskData = await this._fetchTaskData(taskRef, taskId);
    if (!taskData) {
      return null;
    }
    if (!(await this._validateTaskData(taskData, taskRef, taskId))) {
      return null;
    }
    return taskData;
  }

  public async setTaskProcessing(taskId: string): Promise<void> {
    const taskRef = await this._getTaskRef(taskId);
    await this._updateTaskStatus(taskRef, "processing");
  }

  public async updateTaskProgress(
    taskId: string,
    progress: number
  ): Promise<void> {
    const taskRef = await this._getTaskRef(taskId);
    await taskRef.update({
      progress: Math.min(100, Math.max(0, progress)),
      updatedAt: new Date(),
    });
  }

  public async updateProviderTaskId(
    taskId: string,
    providerTaskId: string
  ): Promise<void> {
    const taskRef = await this._getTaskRef(taskId);
    await taskRef.update({
      providerTaskId,
      updatedAt: new Date(),
    });
  }

  public async handleSuccess(
    taskId: string,
    result: TaskVideoOutput
  ): Promise<void> {
    const taskRef = await this._getTaskRef(taskId);

    await this._updateTaskStatus(taskRef, "succeeded", {
      progress: 100,
      resultData: result.data,
      coverImageUrl: result.coverImageUrl,
      videoUrl: result.videoUrl,
    });
    console.log(
      `[VideoWorkflow Task ${taskId}] Video generation completed successfully.`
    );

    // Create Asset record from the completed video task
    try {
      const videoTask = await this.fetchTask(taskId);
      if (videoTask && videoTask.status === "succeeded") {
        const { createAssetFromVideoTask } = await import(
          "../features/assets/utils/create-asset-from-task"
        );
        const asset = await createAssetFromVideoTask(videoTask);
        if (asset) {
          console.log(
            `[VideoWorkflow Task ${taskId}] Successfully created asset ${asset.id}`
          );
        } else {
          console.warn(`[VideoWorkflow Task ${taskId}] Failed to create asset`);
        }
      }
    } catch (error) {
      console.error(
        `[VideoWorkflow Task ${taskId}] Error creating asset:`,
        error
      );
      // Don't throw error here to avoid breaking the workflow
    }
  }

  public async handleFailure(taskId: string, error: any): Promise<void> {
    const taskRef = await this._getTaskRef(taskId);
    const errorMessage = error instanceof Error ? error.message : String(error);
    await this._updateTaskStatus(taskRef, "failed", {
      errorMessage,
    });
    console.error(
      `[VideoWorkflow Task ${taskId}] Video generation failed: ${errorMessage}`
    );
  }

  /**
   * Main workflow execution method
   */
  async executeWorkflow(taskId: string): Promise<void> {
    try {
      console.log(`[VideoWorkflow] Starting workflow for task: ${taskId}`);

      // 1. Fetch and validate task
      const taskData = await this.fetchTask(taskId);
      if (!taskData) {
        console.error(`[VideoWorkflow] Task ${taskId} not found or invalid`);
        return;
      }

      // 2. Set task to processing
      await this.setTaskProcessing(taskId);

      // 3. Get provider and submit task
      const provider = this.providerRouter.getProvider(taskData.provider!);
      console.log(`[VideoWorkflow] Using provider: ${provider.providerId}`);

      // 4. Prepare input for provider
      const providerInput = provider.normalizeInput({
        ...taskData.inputData,
      });

      // 5. Submit task to provider
      const submitResult = await provider.submitTask(providerInput);
      console.log(
        `[VideoWorkflow] Task submitted to provider: ${submitResult.providerTaskId}`
      );

      // 6. Update task with provider task ID
      await this.updateProviderTaskId(taskId, submitResult.providerTaskId);

      // 7. Poll for results
      await this.pollForResult(taskId, provider, submitResult.providerTaskId);
    } catch (error) {
      console.error(
        `[VideoWorkflow] Error in workflow for task ${taskId}:`,
        error
      );
      await this.handleFailure(taskId, error);
    }
  }

  private async pollForResult(
    taskId: string,
    provider: TaskProvider,
    providerTaskId: string
  ): Promise<void> {
    let attempts = 0;
    const maxAttempts = 60; // 10 minutes maximum (60 attempts * 10 seconds average)

    while (attempts < maxAttempts) {
      try {
        console.log(
          `[VideoWorkflow] Polling attempt ${
            attempts + 1
          }/${maxAttempts} for task ${taskId}`
        );

        // Wait before checking (progressive backoff)
        const waitTime = this.getWaitTime(attempts);
        await this.sleep(waitTime);

        // Check status with provider
        const result = await provider.checkTaskStatus(providerTaskId);
        console.log(
          `[VideoWorkflow] Provider status for task ${taskId}:`,
          result.status
        );

        // Update progress if available
        if (result.progress !== undefined) {
          await this.updateTaskProgress(taskId, result.progress);
        }

        // Handle completion
        if (result.status === "completed") {
          await this.handleSuccess(taskId, result.result!);
          return;
        }

        // Handle failure
        if (result.status === "failed") {
          await this.handleFailure(
            taskId,
            result.error || "Provider reported failure"
          );
          return;
        }

        attempts++;
      } catch (error) {
        console.error(
          `[VideoWorkflow] Error during polling for task ${taskId}:`,
          error
        );
        attempts++;

        // If we've tried many times, fail the task
        if (attempts >= maxAttempts) {
          await this.handleFailure(
            taskId,
            `Polling failed after ${maxAttempts} attempts: ${error}`
          );
          return;
        }
      }
    }

    // Timeout
    await this.handleFailure(
      taskId,
      `Task timeout after ${maxAttempts} polling attempts`
    );
  }

  private getWaitTime(attempts: number): number {
    // Progressive backoff: 5s -> 10s -> 30s
    if (attempts < 6) return 3000; // First 30 seconds: every 3 seconds
    if (attempts < 18) return 5000; // Next 2 minutes: every 5 seconds
    return 10000; // After 2.5 minutes: every 10 seconds
  }

  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}
