import {
  WorkflowEntrypoint,
  WorkflowStep,
  WorkflowEvent,
} from "cloudflare:workers";
import { container } from "tsyringe";

import {
  IProviderRouter,
  ProviderRouter,
} from "../features/video-generation/providers/provider-router";
import {
  IVideoGenerationWorkflowService,
  VideoGenerationWorkflowService,
} from "./video-generation-workflow.interface";
import type { CloudflareBindings } from "../types";
import { setupRequestContainer } from "../container.setup";
import { CLOUDFLARE_ENV } from "../infrastructure/env/env-service.interface";

/**
 * Cloudflare Workflow for Video Generation
 * This is the entry point for the video generation workflow
 */
export class VideoGenerationWorkflow extends WorkflowEntrypoint<
  CloudflareBindings,
  { taskId: string }
> {
  private workflowService: VideoGenerationWorkflowService;
  private providerRouter: ProviderRouter;

  constructor(ctx: any, env: CloudflareBindings) {
    super(ctx, env);

    console.log("VideoGenerationWorkflow constructor called");
    console.log("env: ", env);
    container.register(CLOUDFLARE_ENV, {
      useValue: env,
    });
    setupRequestContainer(container);
    this.workflowService = container.resolve(IVideoGenerationWorkflowService);
    this.providerRouter = container.resolve(IProviderRouter);
  }
  async run(event: WorkflowEvent<{ taskId: string }>, step: WorkflowStep) {
    const { taskId } = event.payload;

    console.log(
      `[VideoGenerationWorkflow] Starting workflow for task: ${taskId}`
    );

    try {
      // Fetch task and validate
      const taskData = await step.do("fetchTask", async () => {
        console.log(`[VideoGenerationWorkflow] Fetching task: ${taskId}`);
        const fetchedTask = await this.workflowService.fetchTask(taskId);
        if (!fetchedTask) {
          throw new Error(`Task ${taskId} not found or invalid.`);
        }
        return fetchedTask;
      });

      // Set task to processing
      await step.do("setTaskProcessing", async () => {
        console.log(
          `[VideoGenerationWorkflow] Setting task to processing: ${taskId}`
        );
        await this.workflowService.setTaskProcessing(taskId);
        return { status: "processing" };
      });

      // Get provider and submit task
      const submitResult = await step.do("submitToProvider", async () => {
        console.log(
          `[VideoGenerationWorkflow] Submitting task to provider: ${taskId}`
        );
        const provider = this.providerRouter.getProvider(taskData.provider!);

        const providerInput = provider.normalizeInput({
          ...taskData.inputData,
        });

        const result = await provider.submitTask(providerInput);
        console.log(
          `[VideoGenerationWorkflow] Task submitted: ${result.providerTaskId}`
        );

        // Update task with provider task ID
        await this.workflowService.updateProviderTaskId(
          taskId,
          result.providerTaskId
        );

        return {
          providerTaskId: result.providerTaskId,
          estimatedDuration: result.estimatedDuration,
        };
      });

      // Poll for results with progressive backoff
      const finalResult = await this.pollForResult(
        step,
        taskId,
        taskData.provider!,
        submitResult.providerTaskId
      );

      // Handle success
      await step.do("handleSuccess", async () => {
        console.log(
          `[VideoGenerationWorkflow] Handling success for task: ${taskId}`
        );
        await this.workflowService.handleSuccess(taskId, finalResult);
        return {
          status: "succeeded",
          result: finalResult,
        };
      });
    } catch (error: any) {
      console.error(
        `[VideoGenerationWorkflow] Error in workflow for task ${taskId}:`,
        error.message
      );

      // Handle failure
      await step.do("handleFailure", async () => {
        await this.workflowService.handleFailure(taskId, error);
        throw error; // Re-throw to mark the workflow instance as failed
      });
    }
  }

  private async pollForResult(
    step: WorkflowStep,
    taskId: string,
    providerId: string,
    providerTaskId: string
  ): Promise<any> {
    let attempts = 0;
    const maxAttempts = 60; // 10 minutes maximum

    while (attempts < maxAttempts) {
      // Progressive delay
      const delaySeconds = this.getDelaySeconds(attempts);
      await step.sleep(`poll-delay-${attempts}`, `${delaySeconds} seconds`);

      const result = await step.do(`check-status-${attempts}`, async () => {
        console.log(
          `[VideoGenerationWorkflow] Checking status (attempt ${
            attempts + 1
          }/${maxAttempts}) for task: ${taskId}`
        );

        const provider = this.providerRouter.getProvider(providerId);

        const statusResult = await provider.checkTaskStatus(providerTaskId);
        console.log(`[VideoGenerationWorkflow] Status result:`, statusResult);

        // Update progress if available
        if (statusResult.progress !== undefined) {
          await this.workflowService.updateTaskProgress(
            taskId,
            statusResult.progress
          );
        }

        return statusResult;
      });

      if (result.status === "completed") {
        console.log(
          `[VideoGenerationWorkflow] Task ${taskId} completed successfully`
        );
        return result.result;
      }

      if (result.status === "failed") {
        throw new Error(
          `Provider task failed: ${result.error || "Unknown error"}`
        );
      }

      attempts++;
    }

    throw new Error(
      `Task ${taskId} timeout after ${maxAttempts} polling attempts`
    );
  }

  private getDelaySeconds(attempts: number): number {
    // Progressive backoff strategy
    if (attempts < 6) return 5; // First 30 seconds: every 5 seconds
    if (attempts < 18) return 10; // Next 2 minutes: every 10 seconds
    return 30; // After 2.5 minutes: every 30 seconds
  }
}
