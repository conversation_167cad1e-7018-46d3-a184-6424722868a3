import { inject, injectable } from "tsyringe";
import type {
  FirestoreClient,
  DocumentReference,
} from "firebase-rest-firestore";
import {
  IDbService,
  type DbService,
} from "../infrastructure/db/db-service.interface";

import type { Task } from "../features/image-generation/image-generation.schema";
import { GptService, IGptService } from "../infrastructure/ai/gpt.interface";
import {
  IEnvService,
  type EnvService,
} from "../infrastructure/env/env-service.interface";
import { downloadImageToR2 } from "../infrastructure/storage/r2-service";
const TASK_COLLECTION = "image-generation-tasks";

@injectable()
export class ImageGenerationWorkflowService
  implements ImageGenerationWorkflowService
{
  private firestore: FirestoreClient;

  constructor(
    @inject(IDbService) private dbService: DbService,
    @inject(IGptService) private gptService: GptService,
    @inject(IEnvService) private envService: EnvService
  ) {
    this.firestore = this.dbService.getFirestoreInstance();
  }

  private async _getTaskRef(taskId: string): Promise<DocumentReference> {
    return this.firestore
      .collection(TASK_COLLECTION)
      .doc(taskId) as DocumentReference;
  }

  private async _fetchTaskData(
    taskRef: DocumentReference,
    taskId: string
  ): Promise<Task | null> {
    const taskDoc = await taskRef.get();
    if (!taskDoc.exists) {
      console.error(`[Workflow Task ${taskId}] Task document not found.`);
      return null;
    }
    return taskDoc.data() as Task;
  }

  private async _updateTaskStatus(
    taskRef: DocumentReference,
    status: Task["status"],
    additionalData: Partial<Task> = {}
  ): Promise<void> {
    await taskRef.update({
      status,
      ...additionalData,
      updatedAt: new Date(),
    });
    console.log(`[Workflow Task ${taskRef.id}] Status updated to ${status}.`);
  }

  private async _validateTaskData(
    taskData: Task,
    taskRef: DocumentReference,
    taskId: string
  ): Promise<boolean> {
    if (!taskData.prompt) {
      console.error(
        `[Workflow Task ${taskId}] Prompt is missing from task data.`
      );
      await this._updateTaskStatus(taskRef, "failed", {
        errorMessage: "Task data is missing the prompt.",
      });
      return false;
    }
    return true;
  }

  public async fetchTask(taskId: string): Promise<Task | null> {
    const taskRef = await this._getTaskRef(taskId);
    const taskData = await this._fetchTaskData(taskRef, taskId);
    if (!taskData) {
      // Optionally handle error or let the caller decide
      return null;
    }
    if (!(await this._validateTaskData(taskData, taskRef, taskId))) {
      // Optionally handle error or let the caller decide
      return null; // Or throw an error
    }
    return taskData;
  }

  public async setTaskProcessing(taskId: string): Promise<void> {
    const taskRef = await this._getTaskRef(taskId);
    await this._updateTaskStatus(taskRef, "processing");
  }

  public async updateTaskProgress(
    taskId: string,
    progress: number
  ): Promise<void> {
    const taskRef = await this._getTaskRef(taskId);
    await taskRef.update({
      progress,
      updatedAt: new Date(),
    });
    console.log(`[Workflow Task ${taskId}] Progress updated to ${progress}%.`);
  }

  public async generateImage(
    taskId: string,
    prompt: string,
    inputImageUrls?: string[]
  ): Promise<string | null> {
    console.log(
      `[Workflow Task ${taskId}] Calling GptService.generateImage with prompt: "${prompt}" and ${
        inputImageUrls?.length || 0
      } image(s).`
    );

    // Create progress callback that updates the task
    const onProgress = async (progress: number) => {
      await this.updateTaskProgress(taskId, progress);
    };

    return this.gptService.generateImage(prompt, inputImageUrls, onProgress);
  }

  public async uploadToR2(taskId: string, imageUrl: string): Promise<string> {
    console.log(`[Workflow Task ${taskId}] Uploading image to R2: ${imageUrl}`);

    const env = this.envService.getBindings();
    const r2ImageUrl = await downloadImageToR2(
      imageUrl,
      env.R2Bucket,
      env.R2_PUBLIC_URL
    );

    console.log(
      `[Workflow Task ${taskId}] Successfully uploaded to R2: ${r2ImageUrl}`
    );

    return r2ImageUrl;
  }

  public async handleSuccess(
    taskId: string,
    generatedImageUrl: string,
    r2ImageUrl: string
  ): Promise<void> {
    const taskRef = await this._getTaskRef(taskId);
    await this._updateTaskStatus(taskRef, "succeeded", {
      resultImageUrls: [r2ImageUrl],
      errorMessage: undefined,
    });
    console.log(
      `[Workflow Task ${taskId}] Task succeeded. Generated image URL: ${generatedImageUrl}, R2 URL: ${r2ImageUrl}`
    );

    // Create Asset record from the completed task
    try {
      const task = await this.fetchTask(taskId);
      if (task && task.status === "succeeded") {
        const { createAssetFromImageTask } = await import(
          "../features/assets/utils/create-asset-from-task"
        );
        const asset = await createAssetFromImageTask(task);
        if (asset) {
          console.log(
            `[Workflow Task ${taskId}] Successfully created asset ${asset.id}`
          );
        } else {
          console.warn(`[Workflow Task ${taskId}] Failed to create asset`);
        }
      }
    } catch (error) {
      console.error(`[Workflow Task ${taskId}] Error creating asset:`, error);
      // Don't throw error here to avoid breaking the workflow
    }
  }

  public async handleNoResult(taskId: string): Promise<void> {
    const taskRef = await this._getTaskRef(taskId);
    await this._updateTaskStatus(taskRef, "no_result", {
      errorMessage: "GptService returned no image URL.",
    });
    console.log(
      `[Workflow Task ${taskId}] Task completed with no image result from GptService.`
    );
  }

  public async handleFailure(taskId: string, error: any): Promise<void> {
    const taskRef = await this._getTaskRef(taskId);
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error(
      `[Workflow Task ${taskId}] Error processing workflow:`,
      error
    );
    await this._updateTaskStatus(taskRef, "failed", {
      errorMessage: `Workflow failed: ${errorMessage}`,
    }).catch((updateError: Error) => {
      console.error(
        `[Workflow Task ${taskId}] Failed to update task status to 'failed' after error:`,
        updateError
      );
    });
  }

  /**
   * Executes the image generation workflow for a given task.
   * Fetches task details, calls GptService to generate an image,
   * and updates the task status in Firestore.
   * @param taskId - The ID of the task to process.
   */
  async executeWorkflow(taskId: string): Promise<void> {
    try {
      const taskData = await this.fetchTask(taskId);
      if (!taskData) {
        // Error already logged and status updated by fetchTask or _validateTaskData
        return;
      }

      await this.setTaskProcessing(taskId);

      const generatedImageUrl = await this.generateImage(
        taskId,
        taskData.prompt,
        taskData.inputImageUrls
      );

      if (generatedImageUrl) {
        const r2ImageUrl = await this.uploadToR2(taskId, generatedImageUrl);
        await this.handleSuccess(taskId, generatedImageUrl, r2ImageUrl);
      } else {
        await this.handleNoResult(taskId);
      }
    } catch (error: any) {
      await this.handleFailure(taskId, error);
    }
  }
}
