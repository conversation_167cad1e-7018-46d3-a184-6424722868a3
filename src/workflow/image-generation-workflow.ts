import {
  WorkflowEntrypoint,
  WorkflowStep,
  type WorkflowEvent,
} from "cloudflare:workers";
// import { NonRetryableError } from "cloudflare:workflows";
import type { CloudflareBindings } from "../types";

import { setupRequestContainer } from "../container.setup";
import { container } from "tsyringe";
import {
  IImageGenerationWorkflowService,
  ImageGenerationWorkflowService,
} from "./image-generation-workflow.interface";
import { CLOUDFLARE_ENV } from "../infrastructure/env/env-service.interface";

// User-defined parameters passed to your workflow
type WorkflowParams = {
  taskId: string;
};

export class ImageGenerationCloudflareWorkflow extends WorkflowEntrypoint<
  CloudflareBindings,
  WorkflowParams
> {
  private imageGenerationService: ImageGenerationWorkflowService;

  constructor(ctx: any, env: CloudflareBindings) {
    super(ctx, env);

    console.log("ImageGenerationCloudflareWorkflow constructor called");
    console.log("env: ", env);
    container.register(CLOUDFLARE_ENV, {
      useValue: env,
    });
    setupRequestContainer(container);
    this.imageGenerationService = container.resolve(
      IImageGenerationWorkflowService
    );
  }

  async run(event: WorkflowEvent<WorkflowParams>, step: WorkflowStep) {
    const { taskId } = event.payload;

    try {
      const taskData = await step.do("fetchTask", async () => {
        console.log(`[Workflow] Fetching task: ${taskId}`);
        const fetchedTask = await this.imageGenerationService.fetchTask(taskId);
        if (!fetchedTask)
          throw new Error(`Task ${taskId} not found or invalid.`);
        return fetchedTask;
      });

      await step.do("setTaskProcessing", async () => {
        console.log(`[Workflow] Setting task to processing: ${taskId}`);
        await this.imageGenerationService.setTaskProcessing(taskId);
        return { status: "processing" };
      });

      const generatedImageUrl = await step.do(
        "generateImage",
        {
          retries: {
            limit: 2,
            delay: "3 seconds",
            backoff: "linear",
          },
          timeout: "15 minutes",
        },
        async () => {
          console.log(`[Workflow] Generating image for task: ${taskId}`);
          const url = await this.imageGenerationService.generateImage(
            taskId,
            taskData.prompt,
            taskData.inputImageUrls
          );
          if (!url) throw new Error("Image generation failed to return a URL.");
          return url;
        }
      );

      const r2ImageUrl = await step.do(
        "uploadToR2",
        {
          retries: {
            limit: 2,
            delay: "2 seconds",
            backoff: "linear",
          },
          timeout: "5 minutes",
        },
        async () => {
          console.log(`[Workflow] Uploading image to R2 for task: ${taskId}`);
          const r2Url = await this.imageGenerationService.uploadToR2(
            taskId,
            generatedImageUrl
          );
          if (!r2Url) throw new Error("R2 upload failed to return a URL.");
          return r2Url;
        }
      );

      await step.do("handleSuccess", async () => {
        console.log(`[Workflow] Handling success for task: ${taskId}`);
        await this.imageGenerationService.handleSuccess(
          taskId,
          generatedImageUrl,
          r2ImageUrl
        );
        return {
          status: "succeeded",
          imageUrl: r2ImageUrl,
          originalImageUrl: generatedImageUrl,
        };
      });
    } catch (error: any) {
      console.error(
        `[Workflow] Error in workflow for task ${taskId}:`,
        error.message
      );
      // Use a step to handle failure to leverage retry and logging capabilities of Workflows
      await step.do("handleFailure", async () => {
        await this.imageGenerationService.handleFailure(taskId, error);
        throw error; // Re-throw to mark the workflow instance as failed if not automatically handled
      });
    }
  }
}
