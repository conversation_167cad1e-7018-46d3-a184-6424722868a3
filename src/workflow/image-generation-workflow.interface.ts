import type { InjectionToken } from "tsyringe";
import type { Task } from "../features/image-generation/image-generation.schema";

export const IImageGenerationWorkflowService: InjectionToken<ImageGenerationWorkflowService> =
  Symbol("IImageGenerationWorkflowService");

export abstract class ImageGenerationWorkflowService {
  abstract fetchTask(taskId: string): Promise<Task | null>;
  abstract setTaskProcessing(taskId: string): Promise<void>;
  abstract generateImage(
    taskId: string,
    prompt: string,
    inputImageUrls?: string[]
  ): Promise<string | null>;
  abstract updateTaskProgress(taskId: string, progress: number): Promise<void>;
  abstract uploadToR2(taskId: string, imageUrl: string): Promise<string>;
  abstract handleSuccess(
    taskId: string,
    generatedImageUrl: string,
    r2ImageUrl: string
  ): Promise<void>;
  abstract handleFailure(taskId: string, error: any): Promise<void>;
}
