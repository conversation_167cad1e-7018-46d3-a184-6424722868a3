import { type DependencyContainer } from "tsyringe";
// import { ChatService } from "./features/chat/chat.service";
// import { MessageProcessService } from "./features/message-process/message-process.service";

// import { GeminiService } from "./infrastructure/ai/GeminiService";
import { RateLimitService } from "./infrastructure/ratelimit/RateLimitService";
// import { UsecaseService } from "./features/usecases/usecase.service";
// Notion
import { INotionService } from "./features/notion/notion.interface";
import { NotionService } from "./features/notion/notion.service";
// Env
import { IEnvService } from "./infrastructure/env/env-service.interface";
import { CloudflareEnvService } from "./infrastructure/env/cf-env.service";
// User
import { IUserService } from "./features/user/user.interface";
import { FireStoreUserService } from "./features/user/firestore-user.service";

// Storage
import { IStorageService } from "./infrastructure/storage/storage-service.interface";

// Image Generation
import { IImageGenerationService } from "./features/image-generation/image-generation.interface";
import { ImageGenerationService } from "./features/image-generation/image-generation.service";
import { IQuotaService } from "./infrastructure/quota/quota.interface";
import { UniversalQuotaService } from "./infrastructure/quota/quota.service";
import { IAnalyticsService } from "./infrastructure/analytics/analytics.interface";
import { KVAnalyticsService } from "./infrastructure/analytics/kv-analytics.service";
import { R2StorageService } from "./infrastructure/storage/r2-storage.service";

// DB
import { IDbService } from "./infrastructure/db/db-service.interface";
import { FirestoreDbService } from "./infrastructure/db/firestore.service";

// Workflow
import { IImageGenerationWorkflowService } from "./workflow/image-generation-workflow.interface";
import { ImageGenerationWorkflowService } from "./workflow/image-generation-workflow.service";

// Gpt
import { IGptService } from "./infrastructure/ai/gpt.interface";
import { GptService } from "./infrastructure/ai/gpt-service";

// FalAI
import { IFalAiService } from "./infrastructure/ai/fal-ai.interface";
import { FalAiService } from "./infrastructure/ai/fal-ai.service";

// Cache
import { ICacheService } from "./infrastructure/cache/cache-service.interface";
import { KVCacheService } from "./infrastructure/cache/kv-cache.service";

// Asset
import { IAssetService } from "./features/assets/asset.interface";
import { FirestoreAssetService } from "./features/assets/asset.service";

// Explore
import { IExploreService } from "./features/explore/explore.interface";
import { FirestoreExploreService } from "./features/explore/explore.service";
// Social
import { ISocialService } from "./features/social/social.interface";
import { FirestoreSocialService } from "./features/social/firestore-social.service";

// Video Generation
import { IVideoGenerationService } from "./features/video-generation/video-generation.interface";
import { VideoGenerationService } from "./features/video-generation/video-generation.service";

// Video Generation Workflow
import { VideoGenerationWorkflowService } from "./workflow/video-generation-workflow.service";
import { IVideoGenerationWorkflowService } from "./workflow/video-generation-workflow.interface";

// Video Generation Provider Router
import {
  IProviderRouter,
  ProviderRouter,
} from "./features/video-generation/providers/provider-router";

// Webhook Event
import { IWebhookEventService } from "./features/webhook/webhook-event.interface";
import { FirestoreWebhookEventService } from "./features/webhook/firestore-webhook-event.service";

// Webhook Handlers
import {
  IRevenueCatWebhookHandler,
  IWebhookHandlerRegistry,
} from "./features/webhook/handlers/webhook-handler.interface";
import { RevenueCatWebhookHandler } from "./features/webhook/handlers/revenue-cat-webhook.handler";
import { DefaultWebhookHandlerRegistry } from "./features/webhook/handlers/webhook-handler.registry";

// Webhook Processors
import { SubscriptionProcessor } from "./features/webhook/processors/subscription.processor";
import { CreditsProcessor } from "./features/webhook/processors/credits.processor";

// Webhook Transformers
import { RevenueCatTransformer } from "./features/webhook/transformers/revenue-cat.transformer";

export function setupRequestContainer(container: DependencyContainer): void {
  container.registerSingleton(IEnvService, CloudflareEnvService);

  container.registerSingleton(IDbService, FirestoreDbService);

  container.registerSingleton(IUserService, FireStoreUserService);
  container.registerSingleton(IAssetService, FirestoreAssetService);
  container.registerSingleton(ISocialService, FirestoreSocialService);
  container.registerSingleton(IExploreService, FirestoreExploreService);
  // container.register("ChatService", { useClass: ChatService });
  container.registerSingleton(IStorageService, R2StorageService);
  // container.register("GeminiService", { useClass: GeminiService });
  container.register("RateLimitService", { useClass: RateLimitService });
  // container.register("MessageProcessService", { useClass: MessageProcessService });
  container.register(IImageGenerationService, {
    useClass: ImageGenerationService,
  });

  container.registerSingleton(IVideoGenerationService, VideoGenerationService);

  container.registerSingleton(IQuotaService, UniversalQuotaService);
  container.registerSingleton(IAnalyticsService, KVAnalyticsService);
  // container.register("UsecaseService", { useClass: UsecaseService });
  container.registerSingleton(INotionService, NotionService);

  container.register(IImageGenerationWorkflowService, {
    useClass: ImageGenerationWorkflowService,
  });

  container.registerSingleton(
    IVideoGenerationWorkflowService,
    VideoGenerationWorkflowService
  );

  container.register(IGptService, {
    useClass: GptService,
  });

  container.register(IFalAiService, {
    useClass: FalAiService,
  });

  container.registerSingleton(IProviderRouter, ProviderRouter);

  // Register webhook event service
  container.registerSingleton(
    IWebhookEventService,
    FirestoreWebhookEventService
  );

  // Register webhook handlers and processors
  container.registerSingleton(RevenueCatTransformer, RevenueCatTransformer);
  container.registerSingleton(SubscriptionProcessor, SubscriptionProcessor);
  container.registerSingleton(CreditsProcessor, CreditsProcessor);
  container.registerSingleton(
    IRevenueCatWebhookHandler,
    RevenueCatWebhookHandler
  );
  container.registerSingleton(
    IWebhookHandlerRegistry,
    DefaultWebhookHandlerRegistry
  );

  // Register handlers with the registry
  // This needs to be done after both the registry and handlers are registered
  const webhookHandlerRegistry = container.resolve(
    IWebhookHandlerRegistry
  ) as DefaultWebhookHandlerRegistry;
  const revenueCatHandler = container.resolve(
    IRevenueCatWebhookHandler
  ) as RevenueCatWebhookHandler;
  webhookHandlerRegistry.registerHandler(revenueCatHandler);

  // Register cache services
  // Use MemoryCacheService as the default implementation
  container.registerSingleton(ICacheService, KVCacheService);
  // Register KVCacheService with a specific token if needed
  // container.registerSingleton("KVCacheService", KVCacheService);

  console.log(
    "setupContainer called to register service classes with global container:",
    container
  );
}
