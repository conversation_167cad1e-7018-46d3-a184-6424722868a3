import type { ApiR<PERSON>ponse, CloudflareBindings } from "../types";

const BASE64_CHUNK_SIZE = 8192; // For converting large ArrayBuffers to binary strings
export const MAX_REPLY_LENGTH = 280;
export const TRUNCATION_SUFFIX = "...";

/**
 * Convert ArrayBuffer to Base64 string
 */
export function arrayBufferToBase64(buffer: ArrayBuffer): string {
  const bytes = new Uint8Array(buffer);
  let binaryString = "";
  for (let i = 0; i < bytes.length; i += BASE64_CHUNK_SIZE) {
    const chunk = bytes.subarray(i, i + BASE64_CHUNK_SIZE);
    binaryString += String.fromCharCode.apply(null, Array.from(chunk));
  }
  return btoa(binaryString);
}

/**
 * Converts a Base64 string to a Uint8Array.
 */
export function base64ToUint8Array(base64: string): Uint8Array {
  try {
    const binaryString = atob(base64);
    const len = binaryString.length;
    const bytes = new Uint8Array(len);
    for (let i = 0; i < len; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    return bytes;
  } catch (e) {
    console.error("Failed to decode base64 string:", e);
    throw new Error("Invalid base64 string provided to base64ToUint8Array");
  }
}

// Validate image data
export function isValidBase64Image(base64String: string): boolean {
  try {
    // Check if it's a valid base64 string - now supports webp
    if (!/^data:image\/(jpeg|png|jpg|webp);base64,/.test(base64String)) {
      return false;
    }

    // Check if the base64 part is valid
    const base64Data = base64String.split(",")[1];
    if (!base64Data || !/^[A-Za-z0-9+/=]+$/.test(base64Data)) {
      return false;
    }

    // Check if the decoded size is reasonable (max 10MB)
    const decodedSize = Math.ceil((base64Data.length * 3) / 4);
    if (decodedSize > 10 * 1024 * 1024) {
      return false;
    }

    return true;
  } catch {
    return false;
  }
}

/**
 * Extract content type from base64 image string
 * @param base64String - Base64 encoded image string with data URL prefix
 * @returns Content type (e.g., "image/png", "image/jpeg", "image/webp")
 */
export function extractContentTypeFromBase64(base64String: string): string {
  const match = base64String.match(/^data:(image\/[^;]+);base64,/);
  if (!match) {
    throw new Error("Invalid base64 image format");
  }
  return match[1];
}

/**
 * Fetch image from URL, convert to base64, and determine MIME type.
 */
export async function fetchImageAsBase64(
  imageUrl: string
): Promise<{ mimeType: string; base64Data: string } | null> {
  try {
    const response = await fetch(imageUrl);

    if (!response.ok) {
      console.warn(
        `Failed to fetch image: ${imageUrl}, Status: ${response.status}`
      );
      return null;
    }

    const imageBuffer = await response.arrayBuffer();
    const base64Data = arrayBufferToBase64(imageBuffer);

    // Prioritize Content-Type header
    let mimeType = response.headers.get("content-type")?.split(";")[0].trim();

    // Fallback to extension guessing if header is missing or generic
    if (!mimeType || mimeType === "application/octet-stream") {
      console.warn(
        `Missing or generic Content-Type for ${imageUrl}. Guessing from extension.`
      );
      const lowerUrl = imageUrl.toLowerCase();
      if (lowerUrl.endsWith(".png")) {
        mimeType = "image/png";
      } else if (lowerUrl.endsWith(".gif")) {
        mimeType = "image/gif";
      } else if (lowerUrl.endsWith(".jpg") || lowerUrl.endsWith(".jpeg")) {
        mimeType = "image/jpeg";
      } else if (lowerUrl.endsWith(".webp")) {
        mimeType = "image/webp";
      } else {
        mimeType = "image/jpeg"; // Default guess
        console.warn(
          `Could not determine MIME type for ${imageUrl}, defaulting to ${mimeType}`
        );
      }
    }

    return { mimeType, base64Data };
  } catch (error) {
    console.error(`Error fetching or processing image ${imageUrl}:`, error);
    return null;
  }
}

/**
 * Create a JSON response
 */
export function createJsonResponse(data: any, status: number = 200): Response {
  return new Response(JSON.stringify(data), {
    status,
    headers: { "Content-Type": "application/json" },
  });
}

/**
 * Convert a base64 image to a stream
 */
export function base64ToStream(
  base64Image: string
): ReadableStream<Uint8Array> {
  const base64Data = base64Image.replace(/^data:image\/\w+;base64,/, "");
  const imageBuffer = base64ToUint8Array(base64Data);

  return new ReadableStream({
    start(controller) {
      controller.enqueue(imageBuffer);
      controller.close();
    },
  });
}

/**
 * Convert a stream to a base64 URL string
 */
export async function streamToBase64URL(
  stream: ReadableStream,
  contentType: string
): Promise<string> {
  const response = new Response(stream);
  const arrayBuffer = await response.arrayBuffer();
  return `data:${contentType};base64,${arrayBufferToBase64(arrayBuffer)}`;
}

/**
 * Extract image URLs from response
 */
export function extractImageUrls(content: string): string[] {
  const regex = /!\[.*?\]\((https:\/\/[^)]+)\)/g;
  const urls: string[] = [];
  let match;

  while ((match = regex.exec(content)) !== null) {
    urls.push(match[1]);
  }

  return urls;
}

/**
 * extract image URL from GPT response
 */
export function extractImageUrlFromResponse(content: string): string | null {
  // find markdown image syntax: ![alt](url)
  const imageRegex = /!\[.*?\]\((.*?)\)/;
  const match = content.match(imageRegex);

  if (match && match[1]) {
    return match[1];
  }

  return null;
}

/**
 * Build success response
 */
export function buildSuccessResponse<T>(data: T): ApiResponse<T> {
  return {
    success: true,
    data,
  };
}

/**
 * Build error response
 */
export function buildErrorResponse(
  message: string,
  status: number = 500
): ApiResponse<null> {
  return {
    success: false,
    error: {
      message,
      status,
    },
  };
}
