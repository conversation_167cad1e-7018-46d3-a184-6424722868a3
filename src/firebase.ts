import { createFirestoreClient } from "firebase-rest-firestore";

interface FirestoreConfig {
  projectId: string;
  privateKey: string;
  clientEmail: string;
}

// Create a client with your configuration
export const initializeFirestoreClient = (config: FirestoreConfig) => {
  return createFirestoreClient({
    projectId: config.projectId,
    privateKey: config.privateKey,
    clientEmail: config.clientEmail,
  });
};

// The direct export of 'firestore' is removed.
// It should now be accessed via DbService.

// import { initializeApp, cert } from "firebase-admin/app";
// import { getFirestore } from "firebase-admin/firestore";
// import { getStorage } from "firebase-admin/storage";
// import { readFileSync } from "fs";
// // import serviceAccount from "./service-account-key.json";

// const serviceAccount = JSON.parse(
//   readFileSync(new URL("../service-account-key.json", import.meta.url), "utf-8")
// );

// const app = initializeApp({
//   credential: cert(serviceAccount),
//   storageBucket:
//     process.env.FIREBASE_STORAGE_BUCKET ||
//     `${serviceAccount.project_id}.appspot.com`,
// });

// export const db = getFirestore();
// export const storage = getStorage(app);
