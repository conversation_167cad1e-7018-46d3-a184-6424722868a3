import { type Context, type MiddlewareHandler } from "hono";
import {
  getFirebaseToken,
  verifyFirebaseAuth,
  type VerifyFirebaseAuthConfig,
} from "@hono/firebase-auth";
import type { HonoEnv } from "../types";

// Firebase Auth 配置
export const firebaseAuthConfig: VerifyFirebaseAuthConfig = {
  projectId: "a1d-chat2design",
};

// 基础认证中间件
export const authMiddleware = verifyFirebaseAuth(firebaseAuthConfig);

/**
 * 可选认证中间件
 * 如果有 token 就验证，没有 token 也允许通过
 */
export const optionalAuthMiddleware = (): MiddlewareHandler => {
  return async (c: Context<HonoEnv>, next) => {
    const authorization = c.req.header("Authorization");

    // 如果没有 Authorization 头，直接通过
    if (!authorization || !authorization.startsWith("Bearer ")) {
      await next();
      return;
    }

    // 如果有 Authorization 头，则验证 token
    try {
      await authMiddleware(c, next);
    } catch (error) {
      // 如果验证失败，返回 401
      return c.json({ error: "Invalid token" }, 401);
    }
  };
};
