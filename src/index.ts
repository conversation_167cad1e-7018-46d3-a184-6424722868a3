import "reflect-metadata";

import { Hono } from "hono";
import { logger } from "hono/logger";
import { cors } from "hono/cors";
import { extendZod<PERSON>ith<PERSON>pen<PERSON><PERSON>, from<PERSON><PERSON> } from "chanfana";
import { z, ZodError } from "zod";
import * as Sentry from "@sentry/cloudflare";

import { setupRequestContainer } from "./container.setup";
import type {
  ExecutionContext,
  ScheduledEvent,
} from "@cloudflare/workers-types";

import type { HonoEnv, CloudflareBindings } from "./types";
import { refreshNotionCmsCache } from "./scheduled-tasks/refresh-ghibli-cms-cache";

// import chatRoutes from "./features/chat/chat.routes";
// import messageProcessRoutes from "./features/message-process/message-process.routes";

import "./firebase";

import { registerUserRoutes } from "./features/user/user.routes";
// import { registerUsecaseRoutes } from "./features/usecases/usecase.routes";
import { registerImageGenerationRoutes } from "./features/image-generation/image-generation.routes";
import { registerFileUploadRoutes } from "./features/file-upload/upload.routes";
import { registerNotionRoutes } from "./features/notion/notion.routes";
import { registerExploreRoutes } from "./features/explore/explore.routes";
import { registerSocialRoutes } from "./features/social/social.routes";

import { registerAuthRoutes } from "./features/auth/auth.routes";
import { registerAssetRoutes } from "./features/assets/asset.routes";
import { registerWebhookRoutes } from "./features/webhook/webhook.routes";

import { container as globalContainer } from "tsyringe";
import { CLOUDFLARE_ENV } from "./infrastructure/env/env-service.interface";
import { registerVideoGenerationRoutes } from "./features/video-generation/video-generation.routes";
// import { registerEnvRoutes } from "./features/env/env.routes";

/**
 * OpenApi Config
 */
extendZodWithOpenApi(z);

const app = new Hono<HonoEnv>();
const options = {
  docs_url: "/docs",
  security: [
    {
      BearerAuth: {},
    },
  ],
};
const openapi = fromHono(app, options);

openapi.registry.registerComponent("securitySchemes", "BearerAuth", {
  type: "http",
  scheme: "bearer",
  bearerFormat: "JWT",
  description: "JWT token with Bearer prefix",
});

/**
 * Dependencies Injection
 */
app.use("/api/*", async (c, next) => {
  const requestContainer = globalContainer.createChildContainer();
  requestContainer.register(CLOUDFLARE_ENV, {
    useValue: c.env,
  });
  setupRequestContainer(requestContainer);
  c.set("container", requestContainer);

  await next();
  // requestContainer.dispose();
});

app.use("/api/*", logger());

// CORS middleware for handling preflight OPTIONS requests
app.use(
  "/api/*",
  cors({
    origin: "*", // Allow all origins - you can restrict this to specific domains if needed
    allowMethods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowHeaders: ["Content-Type", "Authorization"],
    exposeHeaders: ["Content-Length"],
    maxAge: 600,
    credentials: false,
  })
);

// test
app.get("/debug-sentry", async (c) => {
  throw new Error("My first Sentry error!");
});

app.onError((err, c) => {
  console.error("Unhandled Server Error:", err);
  Sentry.captureException(err);
  if (err instanceof ZodError) {
    return c.json(
      {
        error: "Validation Failed",
        issues: err.flatten().fieldErrors,
      },
      400
    );
  }

  const statusCode = (err as any).statusCode || 500;
  const message = (err as any).message || "Internal Server Error";

  return c.json({ error: message }, statusCode);
});

app.get("/", (c) => {
  return c.json({ status: "ok", message: "API service is running" });
});

// registerUsecaseRoutes(openapi, "/api/v1/usecases");
registerVideoGenerationRoutes(openapi, "/api/v1/video-generation");
registerImageGenerationRoutes(openapi, "/api/v1/image-generation");
registerFileUploadRoutes(openapi, "/api/v1/file-upload");
registerAssetRoutes(openapi, "/api/v1/assets");
registerExploreRoutes(openapi, "/api/v1/explore");

// Register social routes (integrated with assets and users)
registerSocialRoutes(openapi, "/api/v1/assets", "/api/v1/users");

// Webhook routes (no authentication required)
registerWebhookRoutes(openapi, "/api/v1/webhook");

// 需要认证的路由
registerUserRoutes(openapi, "/api/v1/users");
registerAuthRoutes(openapi, "/api/v1/auth");
registerNotionRoutes(openapi);
// registerEnvRoutes(openapi, "/api/v1/env");

const api = new Hono<HonoEnv>();

// api.route("/chat", chatRoutes);
// api.route("/messages", messageProcessRoutes);

app.route("/api/v1", api);

/**
 * Cloudflare worker scheduled function
 */
export const scheduled = async (
  controller: ScheduledEvent,
  env: CloudflareBindings,
  ctx: ExecutionContext
) => {
  console.log("Running scheduled task:", controller.cron);

  // Refresh all Notion CMS caches every 10 minutes
  if (controller.cron === "*/10 * * * *") {
    await refreshNotionCmsCache(controller, env, ctx);
  }
};

export { ImageGenerationCloudflareWorkflow } from "./workflow/image-generation-workflow";
export { VideoGenerationWorkflow as VideoGenerationCloudflareWorkflow } from "./workflow/video-generation-workflow";

export default Sentry.withSentry((env: CloudflareBindings) => {
  // Handle case where CF_VERSION_METADATA might not exist
  const versionId = env.CF_VERSION_METADATA?.id || "unknown";

  return {
    dsn: env.SENTRY_DSN,
    release: versionId,
    // Adds request headers and IP for users, for more info visit:
    // https://docs.sentry.io/platforms/javascript/guides/cloudflare/configuration/options/#sendDefaultPii
    sendDefaultPii: true,
    // Enable logs to be sent to Sentry
    _experiments: { enableLogs: true },
    // Set tracesSampleRate to 1.0 to capture 100% of spans for tracing.
    // Learn more at
    // https://docs.sentry.io/platforms/javascript/configuration/options/#traces-sample-rate
    tracesSampleRate: 1.0,
  };
}, app);
