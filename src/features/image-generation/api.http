@baseUrl = http://localhost:3000/api/v1
# @baseUrl = https://chat-to-design-backend.fly.dev/api/v1

###
POST {{baseUrl}}/image-generation
Content-Type: application/json
Authorization: Bearer YOUR_FIREBASE_ID_TOKEN_HERE

{
    "prompt": "replace the cute dragon with holy tiger",
    "imageUrls": [
        "https://pbs.twimg.com/media/Gpf1QX_a0AAyeia.jpg"
    ]
}

###
# Create image generation task with batch ID and previous task reference
POST {{baseUrl}}/image-generation
Content-Type: application/json
Authorization: Bearer YOUR_FIREBASE_ID_TOKEN_HERE

{
    "prompt": "continue from the previous image and add more details",
    "imageUrls": [
        "https://pbs.twimg.com/media/Gpf1QX_a0AAyeia.jpg"
    ],
    "batchId": "batch-2024-01-15-001",
    "previousTaskId": "52df2e8e-e72d-455a-82fb-464fc7504040"
}

###
GET {{baseUrl}}/image-generation/history/123

###
# Get task by ID (basic)
GET {{baseUrl}}/image-generation/task/550e8400-e29b-41d4-a716-446655440000

###
# Get task by ID with progress info (for real-time polling)
GET {{baseUrl}}/image-generation/task/550e8400-e29b-41d4-a716-446655440000?includeProgress=true


###
POST {{baseUrl}}/image-generation/usecase
Content-Type: application/json

{
    "name": "Studio Ghibli",
    "imageUrls": ["https://pbs.twimg.com/media/Gpf1QX_a0AAyeia.jpg"]
}