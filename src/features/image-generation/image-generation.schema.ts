import { z } from "zod";
/**
 * @Warning deniffer: only for type inference
 */

export const GenericErrorSchema = z.object({ error: z.string() });

export const ImageGenerationRequestSchema = z.object({
  imageUrls: z
    .array(z.string().url({ message: "Each image URL must be a valid URL." }))
    .openapi({
      example: ["https://pbs.twimg.com/media/Gpf1QX_a0AAyeia.jpg"],
    }),
  prompt: z.string().min(1, { message: "Prompt cannot be empty." }).openapi({
    example: "replace the cute dragon with holy tiger",
  }),
  batchId: z.string().optional().openapi({
    example: "batch-2024-01-15-001",
    description:
      "Optional batch identifier for grouping multiple tasks together",
  }),
  previousTaskId: z.string().optional().openapi({
    example: "52df2e8e-e72d-455a-82fb-464fc7504040",
    description:
      "Optional reference to a previous task for chaining or continuation",
  }),
});

export const UsecaseImageGenerationRequestSchema = z.object({
  id: z
    .string()
    .min(1, { message: "Usecase id cannot be empty." })
    .openapi({ example: "1f662e26-c437-804f-ae55-e3740f199a0d" }),
  imageUrls: z
    .array(z.string().url({ message: "Each image URL must be a valid URL." }))
    .openapi({
      example: ["https://pbs.twimg.com/media/Gpf1QX_a0AAyeia.jpg"],
    }),
  userId: z.string().optional().openapi({ example: "test-user-123" }),
  deviceId: z
    .string()
    .min(1, { message: "Device ID cannot be empty." })
    .openapi({ example: "test-device-123" }),
  userType: z
    .enum(["free", "weekly_pro", "monthly_pro", "yearly_pro"])
    .openapi({ example: "free" }),
});

export const ImageEditRequestSchema = z.object({
  prompt: z.string().min(1, { message: "Prompt cannot be empty." }).openapi({
    example: "Put a donut next to the flour.",
  }),
  image_url: z
    .string()
    .url({ message: "Image URL must be a valid URL." })
    .openapi({
      example:
        "https://v3.fal.media/files/rabbit/rmgBxhwGYb2d3pl3x9sKf_output.png",
    }),
});

export const UsecaseImageGenerationResponseSchema = z.object({
  success: z.boolean(),
  message: z.string().optional(),
  imageUrl: z.string().url().optional(),
  watermarkedImageUrl: z.string().url().optional(),
});

export const ImageEditResponseSchema = z.object({
  success: z.boolean(),
  message: z.string().optional(),
  imageUrl: z.string().url().optional(),
});

export const TaskStatusSchema = z.enum([
  "pending",
  "processing",
  "succeeded", // using succeeded instead of completed for clarity
  "failed", // explicit failed state
  "no_result", // specific state when API succeeded but returned no images
  "deleted", // soft delete state for tasks
]);

// Schema for the task document stored in Firestore
// We need a way to represent Timestamp for Zod validation.
// Firestore Timestamps are tricky with Zod directly. We might validate as `z.instanceof(Timestamp)`
// during runtime or use `z.date()` or `z.number()` if converting.
// For simplicity in definition, let's use z.any() for timestamps,
// but actual validation/transformation might be needed in the service layer.
export const TaskSchema = z.object({
  taskId: z.string().uuid(), // Assuming UUID for task ID
  userId: z.string(), // ID of the user who initiated the task
  status: TaskStatusSchema,
  prompt: z.string(),
  inputImageUrls: z.array(z.string().url()).optional(),
  resultImageUrls: z.array(z.string().url()).optional(),
  errorMessage: z.string().optional(),
  batchId: z.string().optional(), // Optional batch identifier for grouping tasks
  previousTaskId: z.string().optional(), // Optional reference to a previous task
  progress: z.number().min(0).max(100).optional(), // Progress percentage (0-100)
  // Representing Firestore Timestamp. Use z.any() for schema definition ease,
  // but handle actual Timestamp objects in code.
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const ImageGenerationResponseSchema = z.object({
  taskId: z.string().uuid(),
});

export const ThirdPartyApiResponseSchema = z.object({
  success: z.boolean(),
  data: z.object({
    imageUrls: z.array(z.string().url()),
  }),
  message: z.string().optional(),
});

export const GetUserTasksRequestSchema = z.object({
  userId: z
    .string()
    .min(1, { message: "User ID cannot be empty." })
    .openapi({ example: "bGl3YXcxe3YsZeSSDvlG86kA4wj1" }),
});

export const GetUserTasksQuerySchema = z.object({
  batchId: z.string().optional().openapi({
    example: "batch-2024-01-15-001",
    description: "Batch identifier to retrieve all tasks in the batch",
  }),
  limit: z
    .string()
    .optional()
    .transform((val) => (val ? parseInt(val, 10) : 20))
    .refine((val) => val > 0 && val <= 100, {
      message: "Limit must be between 1 and 100",
    })
    .openapi({
      example: "20",
      description: "Number of tasks to return (1-100, default: 20)",
    }),
  page: z
    .string()
    .optional()
    .transform((val) => (val ? parseInt(val, 10) : 1))
    .refine((val) => val > 0, {
      message: "Page must be greater than 0",
    })
    .openapi({
      example: "1",
      description: "Page number (starting from 1, default: 1)",
    }),
});

export const PaginationMetaSchema = z.object({
  currentPage: z.number(),
  totalPages: z.number(),
  totalItems: z.number(),
  itemsPerPage: z.number(),
  hasNextPage: z.boolean(),
  hasPreviousPage: z.boolean(),
});

export const GetUserTasksResponseSchema = z.object({
  tasks: z.array(TaskSchema),
  pagination: PaginationMetaSchema,
});

export const GetTaskByIdRequestSchema = z.object({
  taskId: z
    .string()
    .uuid({ message: "Task ID must be a valid UUID." })
    .openapi({
      example: "52df2e8e-e72d-455a-82fb-464fc7504040",
    }),
});

export const GetTaskByIdResponseSchema = TaskSchema;

export const GetTasksByBatchIdRequestSchema = z.object({
  batchId: z.string().min(1, { message: "Batch ID cannot be empty." }).openapi({
    example: "batch-2024-01-15-001",
    description: "Batch identifier to retrieve all tasks in the batch",
  }),
});

export const GetTasksByBatchIdResponseSchema = z.array(TaskSchema);

export type UsecaseImageGenerationRequest = z.infer<
  typeof UsecaseImageGenerationRequestSchema
>;

export type UsecaseImageGenerationResponse = z.infer<
  typeof UsecaseImageGenerationResponseSchema
>;

export type ImageEditRequest = z.infer<typeof ImageEditRequestSchema>;
export type ImageEditResponse = z.infer<typeof ImageEditResponseSchema>;

export type ImageGenerationRequest = z.infer<
  typeof ImageGenerationRequestSchema
>;
export type TaskStatus = z.infer<typeof TaskStatusSchema>;
export type GetUserTasksRequest = z.infer<typeof GetUserTasksRequestSchema>;
export type GetUserTasksQuery = z.infer<typeof GetUserTasksQuerySchema>;
export type PaginationMeta = z.infer<typeof PaginationMetaSchema>;
export type GetUserTasksResponse = z.infer<typeof GetUserTasksResponseSchema>;
export type GetTaskByIdRequest = z.infer<typeof GetTaskByIdRequestSchema>;
export type GetTaskByIdResponse = z.infer<typeof GetTaskByIdResponseSchema>;
export type GetTasksByBatchIdRequest = z.infer<
  typeof GetTasksByBatchIdRequestSchema
>;
export type GetTasksByBatchIdResponse = z.infer<
  typeof GetTasksByBatchIdResponseSchema
>;
export type ThirdPartyApiResponse = z.infer<typeof ThirdPartyApiResponseSchema>;
export type ImageGenerationResponse = z.infer<
  typeof ImageGenerationResponseSchema
>;
export type Task = z.infer<typeof TaskSchema>;
