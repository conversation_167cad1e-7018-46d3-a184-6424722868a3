import type { InjectionToken } from "tsyringe";

export const IAnalyticsService: InjectionToken<AnalyticsService> = Symbol("IAnalyticsService");

/**
 * Daily statistics for API endpoints
 */
export interface DailyStats {
  date: string; // YYYY-MM-DD format
  totalCalls: number;
  successCalls: number;
  failureCalls: number;
  lastUpdated: string; // ISO timestamp
}

/**
 * Analytics service interface for tracking API usage statistics
 */
export abstract class AnalyticsService {
  /**
   * Record an API call attempt
   * @param endpoint The endpoint name (e.g., 'generate-image-from-usecase')
   * @param date Optional date string (YYYY-MM-DD), defaults to today
   */
  abstract recordCall(endpoint: string, date?: string): Promise<void>;

  /**
   * Record a successful API call
   * @param endpoint The endpoint name
   * @param date Optional date string (YYYY-MM-DD), defaults to today
   */
  abstract recordSuccess(endpoint: string, date?: string): Promise<void>;

  /**
   * Record a failed API call
   * @param endpoint The endpoint name
   * @param date Optional date string (YYYY-MM-DD), defaults to today
   */
  abstract recordFailure(endpoint: string, date?: string): Promise<void>;

  /**
   * Get daily statistics for an endpoint
   * @param endpoint The endpoint name
   * @param date Optional date string (YYYY-MM-DD), defaults to today
   * @returns Daily statistics or null if no data exists
   */
  abstract getDailyStats(endpoint: string, date?: string): Promise<DailyStats | null>;

  /**
   * Get statistics for multiple days
   * @param endpoint The endpoint name
   * @param startDate Start date (YYYY-MM-DD)
   * @param endDate End date (YYYY-MM-DD)
   * @returns Array of daily statistics
   */
  abstract getStatsRange(endpoint: string, startDate: string, endDate: string): Promise<DailyStats[]>;

  /**
   * Clear statistics for an endpoint and date
   * @param endpoint The endpoint name
   * @param date Optional date string (YYYY-MM-DD), defaults to today
   */
  abstract clearStats(endpoint: string, date?: string): Promise<void>;
}
