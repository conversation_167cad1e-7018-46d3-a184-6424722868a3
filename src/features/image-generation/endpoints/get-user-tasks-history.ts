import { type Context } from "hono";

import { OpenAPIRoute } from "chanfana";

import {
  GenericErrorSchema,
  GetUserTasksRequestSchema,
  GetUserTasksQuerySchema,
  GetUserTasksResponseSchema,
} from "../image-generation.schema";

import type { HonoEnv } from "../../../types";
import {
  IImageGenerationService,
  ImageGenerationService,
} from "../image-generation.interface";

export class GetUserTasksHistoryEndpoint extends OpenAPIRoute {
  schema = {
    summary: "Get user's image generation task history",
    description:
      "Retrieves a paginated list of image generation tasks for a specific user. Optionally filter by batch ID.",
    tags: ["Image Generation"],
    request: {
      params: GetUserTasksRequestSchema,
      query: GetUserTasksQuerySchema,
    },
    responses: {
      "200": {
        description: "A paginated list of user's tasks with metadata.",
        content: {
          "application/json": {
            schema: GetUserTasksResponseSchema,
          },
        },
      },
      "404": {
        description: "Tasks not found for the user.",
        content: {
          "application/json": {
            schema: GenericErrorSchema,
          },
        },
      },
      "500": {
        description: "Failed to retrieve user tasks.",
        content: {
          "application/json": {
            schema: GenericErrorSchema,
          },
        },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    const { params, query } = await this.getValidatedData<typeof this.schema>();

    const container = c.get("container");

    const imageGenerationService = container.resolve<ImageGenerationService>(
      IImageGenerationService
    );

    try {
      const result = await imageGenerationService.getUserTasks(
        params.userId,
        query
      );

      if (result.tasks.length === 0 && result.pagination.currentPage === 1) {
        return c.json({ error: "Tasks not found" }, 404);
      }

      return c.json(result, 200);
    } catch (error: any) {
      console.error(`Error in GET /history/${params.userId} endpoint:`, error);
      return c.json({ error: "Failed to retrieve user tasks." }, 500);
    }
  }
}
