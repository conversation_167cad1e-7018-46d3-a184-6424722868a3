import { type Context } from "hono";
import { OpenAPIRoute } from "chanfana";
import { getFirebaseToken } from "@hono/firebase-auth";
import { z } from "zod";
import {
  ImageGenerationRequestSchema,
  ImageGenerationResponseSchema,
} from "../image-generation.schema";
import type { HonoEnv } from "../../../types";
import {
  IImageGenerationService,
  ImageGenerationService,
} from "../image-generation.interface";

const GenericErrorSchema = z.object({
  error: z.string(),
});

export class CreateImageGenerationTaskEndpoint extends OpenAPIRoute {
  schema = {
    summary: "Create an image generation task",
    description:
      "Initiates a new task to generate images based on provided URLs and a prompt for the authenticated user.",
    tags: ["Image Generation"],
    security: [
      {
        BearerAuth: [],
      },
    ],
    request: {
      body: {
        content: {
          "application/json": {
            schema: ImageGenerationRequestSchema,
          },
        },
      },
    },
    responses: {
      "202": {
        description: "Task accepted for processing.",
        content: {
          "application/json": {
            schema: ImageGenerationResponseSchema,
          },
        },
      },
      "400": {
        description: "Invalid request body.",
        content: {
          "application/json": {
            schema: GenericErrorSchema,
          },
        },
      },
      "401": {
        description: "Authentication required.",
        content: {
          "application/json": {
            schema: GenericErrorSchema,
          },
        },
      },
      "500": {
        description: "Failed to start image generation task.",
        content: {
          "application/json": {
            schema: GenericErrorSchema,
          },
        },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    try {
      // Get Firebase token for user authentication
      const firebaseToken = getFirebaseToken(c);
      if (!firebaseToken) {
        return c.json({ error: "Authentication required" }, 401);
      }

      const userId = firebaseToken.uid;
      const { body } = await this.getValidatedData<typeof this.schema>();

      const container = c.get("container");
      const imageGenerationService = container.resolve<ImageGenerationService>(
        IImageGenerationService
      );

      const { taskId } = await imageGenerationService.createImageGenerationTask(
        userId,
        body, // The body now matches ImageGenerationRequest without userId
        c.executionCtx.waitUntil.bind(c.executionCtx)
      );
      return c.json({ taskId }, 202);
    } catch (error: any) {
      console.error("Error in POST /image-generation endpoint:", error);

      if (error.message.includes("validation")) {
        return c.json(
          {
            error: "Invalid request body",
            details: error.message,
          },
          400
        );
      }

      return c.json({ error: "Failed to start image generation task." }, 500);
    }
  }
}
