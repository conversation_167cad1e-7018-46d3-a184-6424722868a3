import { type Context } from "hono";
import { OpenAPIRoute } from "chanfana";
import { z } from "zod";

import type { HonoEnv } from "../../../types";
import {
  IQuotaService,
  type QuotaService,
} from "../../../infrastructure/quota/quota.interface";
import { QuotaCheckResponseSchema } from "../../../infrastructure/quota/quota.schema";
import { getFirebaseToken } from "@hono/firebase-auth";
import { GenericErrorSchema } from "../image-generation.schema";

export class CheckQuotaEndpoint extends OpenAPIRoute {
  schema = {
    summary: "Check image generation quota",
    description:
      "Check if the authenticated user can use the image generation feature and get quota information.",
    tags: ["Image Generation"],
    security: [
      {
        BearerAuth: [],
      },
    ],
    request: {
      query: z.object({
        userType: z
          .enum(["free", "weekly_pro", "monthly_pro", "yearly_pro"])
          .optional(),
      }),
    },
    responses: {
      "200": {
        description: "Quota information retrieved successfully.",
        content: {
          "application/json": {
            schema: QuotaCheckResponseSchema,
          },
        },
      },
      "400": {
        description: "Invalid device ID.",
        content: {
          "application/json": {
            schema: GenericErrorSchema,
          },
        },
      },
      "500": {
        description: "Failed to check quota.",
        content: {
          "application/json": {
            schema: GenericErrorSchema,
          },
        },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    // Get Firebase token for user authentication
    const firebaseToken = getFirebaseToken(c);
    if (!firebaseToken) {
      return c.json({ error: "Authentication required" }, 401);
    }

    const uid = firebaseToken.uid;
    const container = c.get("container");
    const quotaService = container.resolve<QuotaService>(IQuotaService);

    const { query } = await this.getValidatedData<typeof this.schema>();
    const { userType } = query;

    try {
      const quota = await quotaService.checkQuota(
        uid,
        "image-generation",
        userType
      );
      const canUse = quota.remaining > 0;

      return c.json(
        {
          canUse,
          quota,
        },
        200
      );
    } catch (error: any) {
      console.error("Error in GET /quota/:deviceId endpoint:", error);
      return c.json({ error: "Failed to check quota." }, 500);
    }
  }
}
