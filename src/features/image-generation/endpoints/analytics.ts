import { type Context } from "hono";
import { OpenAPIRoute } from "chanfana";
import { z } from "zod";

import type { HonoEnv } from "../../../types";
import {
  IAnalyticsService,
  type AnalyticsService,
} from "../../../infrastructure/analytics/analytics.interface";

// Request schema for analytics query
const AnalyticsQuerySchema = z.object({
  endpoint: z
    .string()
    .describe("The endpoint name to query statistics for")
    .openapi({ example: "generate-image-from-usecase" }),
  startDate: z
    .string()
    .optional()
    .describe(
      "Start date for query (YYYY-MM-DD). If only startDate is provided, queries from startDate to current date. If neither startDate nor endDate is provided, queries today's data only."
    )
    .openapi({ example: "2025-06-01" }),
  endDate: z
    .string()
    .optional()
    .describe(
      "End date for range query (YYYY-MM-DD). Must be used together with startDate for range queries."
    ),
  // .openapi({ example: "2025-06-30" }),
});

// Response schema
const AnalyticsResponseSchema = z.object({
  success: z.boolean().openapi({ example: true }),
  data: z
    .union([
      z.object({
        date: z.string().openapi({ example: "2024-01-15" }),
        totalCalls: z.number().openapi({ example: 150 }),
        successCalls: z.number().openapi({ example: 142 }),
        failureCalls: z.number().openapi({ example: 8 }),
        lastUpdated: z
          .string()
          .openapi({ example: "2024-01-15T14:30:00.000Z" }),
      }),
      z.array(
        z.object({
          date: z.string().openapi({ example: "2024-01-15" }),
          totalCalls: z.number().openapi({ example: 150 }),
          successCalls: z.number().openapi({ example: 142 }),
          failureCalls: z.number().openapi({ example: 8 }),
          lastUpdated: z
            .string()
            .openapi({ example: "2024-01-15T14:30:00.000Z" }),
        })
      ),
    ])
    .optional(),
  error: z.string().optional().openapi({ example: "Error message if any" }),
});

const ErrorResponseSchema = z.object({
  success: z.boolean().openapi({ example: false }),
  error: z.string().openapi({ example: "Invalid request parameters" }),
});

export class AnalyticsEndpoint extends OpenAPIRoute {
  schema = {
    summary: "Get API usage analytics",
    description:
      "Retrieve daily statistics for API endpoints including call counts, success/failure rates. Supports three query modes: 1) No date parameters - returns today's data, 2) Only startDate - returns data from startDate to current date, 3) Both startDate and endDate - returns data for the specified range.",
    tags: ["Analytics"],
    request: {
      query: AnalyticsQuerySchema,
    },
    responses: {
      "200": {
        description: "Analytics data retrieved successfully",
        content: {
          "application/json": {
            schema: AnalyticsResponseSchema,
            examples: {
              todayOnly: {
                summary: "Today's analytics",
                description:
                  "Analytics data for today (no date parameters provided)",
                value: {
                  success: true,
                  data: {
                    date: "2024-01-15",
                    totalCalls: 150,
                    successCalls: 142,
                    failureCalls: 8,
                    lastUpdated: "2024-01-15T14:30:00.000Z",
                  },
                },
              },
              startDateToNow: {
                summary: "From start date to current date",
                description:
                  "Analytics data from a specific start date to current date (only startDate provided)",
                value: {
                  success: true,
                  data: [
                    {
                      date: "2024-01-15",
                      totalCalls: 150,
                      successCalls: 142,
                      failureCalls: 8,
                      lastUpdated: "2024-01-15T14:30:00.000Z",
                    },
                    {
                      date: "2024-01-16",
                      totalCalls: 203,
                      successCalls: 195,
                      failureCalls: 8,
                      lastUpdated: "2024-01-16T14:30:00.000Z",
                    },
                  ],
                },
              },
              dateRange: {
                summary: "Specific date range analytics",
                description:
                  "Analytics data for a specific date range (both startDate and endDate provided)",
                value: {
                  success: true,
                  data: [
                    {
                      date: "2024-01-15",
                      totalCalls: 150,
                      successCalls: 142,
                      failureCalls: 8,
                      lastUpdated: "2024-01-15T14:30:00.000Z",
                    },
                    {
                      date: "2024-01-16",
                      totalCalls: 203,
                      successCalls: 195,
                      failureCalls: 8,
                      lastUpdated: "2024-01-16T14:30:00.000Z",
                    },
                  ],
                },
              },
              noData: {
                summary: "No data available",
                description: "Response when no analytics data is found",
                value: {
                  success: true,
                  data: {
                    date: "2024-01-15",
                    totalCalls: 0,
                    successCalls: 0,
                    failureCalls: 0,
                    lastUpdated: "2024-01-15T14:30:00.000Z",
                  },
                },
              },
            },
          },
        },
      },
      "400": {
        description: "Invalid request parameters",
        content: {
          "application/json": {
            schema: ErrorResponseSchema,
            examples: {
              invalidDateFormat: {
                summary: "Invalid date format",
                description:
                  "Error when startDate or endDate format is invalid",
                value: {
                  success: false,
                  error:
                    "Invalid date format. Please use YYYY-MM-DD format for startDate and endDate.",
                },
              },
              missingEndpoint: {
                summary: "Missing endpoint parameter",
                description: "Error when endpoint parameter is missing",
                value: {
                  success: false,
                  error: "Endpoint parameter is required",
                },
              },
            },
          },
        },
      },
      "500": {
        description: "Internal server error",
        content: {
          "application/json": {
            schema: ErrorResponseSchema,
            examples: {
              serverError: {
                summary: "Internal server error",
                description: "Error when server encounters an unexpected issue",
                value: {
                  success: false,
                  error: "Failed to retrieve analytics data",
                },
              },
            },
          },
        },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    try {
      const container = c.get("container");
      const analyticsService =
        container.resolve<AnalyticsService>(IAnalyticsService);

      const { query } = await this.getValidatedData<typeof this.schema>();
      const { endpoint, startDate, endDate } = query;

      // Determine query type and date range
      if (startDate && endDate) {
        // Range query with both start and end dates
        const stats = await analyticsService.getStatsRange(
          endpoint,
          startDate,
          endDate
        );
        return c.json({
          success: true,
          data: stats,
        });
      } else if (startDate && !endDate) {
        // Range query from startDate to current date
        const currentDate = new Date().toISOString().split("T")[0];
        const stats = await analyticsService.getStatsRange(
          endpoint,
          startDate,
          currentDate
        );
        return c.json({
          success: true,
          data: stats,
        });
      } else {
        // Single day query for today (backward compatibility)
        const currentDate = new Date().toISOString().split("T")[0];
        const stats = await analyticsService.getDailyStats(
          endpoint,
          currentDate
        );
        if (!stats) {
          return c.json({
            success: true,
            data: {
              date: currentDate,
              totalCalls: 0,
              successCalls: 0,
              failureCalls: 0,
              lastUpdated: new Date().toISOString(),
            },
          });
        }
        return c.json({
          success: true,
          data: stats,
        });
      }
    } catch (error: any) {
      console.error("Error in analytics endpoint:", error);
      return c.json(
        {
          success: false,
          error: "Failed to retrieve analytics data",
        },
        500
      );
    }
  }
}
