import { type Context } from "hono";
import { OpenAPIRoute } from "chanfana";

import {
  GenericErrorSchema,
  ImageEditRequestSchema,
  ImageEditResponseSchema,
} from "../image-generation.schema";
import type { HonoEnv } from "../../../types";
import {
  IImageGenerationService,
  type ImageGenerationService,
} from "../image-generation.interface";

export class EditImageEndpoint extends OpenAPIRoute {
  schema = {
    summary: "Edit image using AI",
    description:
      "Edits an image based on a text prompt using fal-ai's flux-pro/kontext model.",
    tags: ["Image Generation"],
    request: {
      body: {
        content: {
          "application/json": {
            schema: ImageEditRequestSchema,
          },
        },
      },
    },
    responses: {
      200: {
        description: "Image edited successfully",
        content: {
          "application/json": {
            schema: ImageEditResponseSchema,
          },
        },
      },
      400: {
        description: "Bad request - invalid input",
        content: {
          "application/json": {
            schema: GenericErrorSchema,
          },
        },
      },
      500: {
        description: "Internal server error",
        content: {
          "application/json": {
            schema: GenericErrorSchema,
          },
        },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    const container = c.get("container");
    const imageGenerationService = container.resolve<ImageGenerationService>(
      IImageGenerationService
    );

    const { body } = await this.getValidatedData<typeof this.schema>();

    try {
      const result = await imageGenerationService.editImage(body);
      
      if (result.success) {
        return c.json(result, 200);
      } else {
        return c.json(result, 400);
      }
    } catch (error: any) {
      console.error("Error in POST /image-generation/edit endpoint:", error);
      return c.json({ 
        success: false,
        error: "Failed to edit image." 
      }, 500);
    }
  }
}
