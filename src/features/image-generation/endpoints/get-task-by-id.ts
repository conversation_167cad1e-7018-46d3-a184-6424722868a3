import { type Context } from "hono";
import { OpenAPIRoute } from "chanfana";
import { z } from "zod";

import {
  GenericErrorSchema,
  GetTaskByIdRequestSchema,
  GetTaskByIdResponseSchema,
} from "../image-generation.schema";

import type { HonoEnv } from "../../../types";
import {
  IImageGenerationService,
  ImageGenerationService,
} from "../image-generation.interface";

export class GetTaskByIdEndpoint extends OpenAPIRoute {
  schema = {
    summary: "Get task by ID",
    description:
      "Retrieves a specific image generation task by its ID, including current status and results. Supports real-time status polling for task progress tracking.",
    tags: ["Image Generation"],
    request: {
      params: GetTaskByIdRequestSchema,
      query: z.object({
        includeProgress: z
          .string()
          .optional()
          .transform((val) => val === "true")
          .openapi({ example: "true" })
          .describe(
            "Include progress information for better real-time tracking"
          ),
      }),
    },
    responses: {
      "200": {
        description: "Task found and returned successfully.",
        content: {
          "application/json": {
            schema: GetTaskByIdResponseSchema,
          },
        },
      },
      "404": {
        description: "Task not found.",
        content: {
          "application/json": {
            schema: GenericErrorSchema,
          },
        },
      },
      "400": {
        description: "Invalid task ID format.",
        content: {
          "application/json": {
            schema: GenericErrorSchema,
          },
        },
      },
      "500": {
        description: "Failed to retrieve task.",
        content: {
          "application/json": {
            schema: GenericErrorSchema,
          },
        },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    const { params, query } = await this.getValidatedData<typeof this.schema>();

    const container = c.get("container");
    const imageGenerationService = container.resolve<ImageGenerationService>(
      IImageGenerationService
    );

    try {
      const task = await imageGenerationService.getTaskById(params.taskId);

      if (!task) {
        return c.json({ error: "Task not found" }, 404);
      }

      // Add real-time polling metadata
      const response: any = {
        ...task,
      };

      if (query.includeProgress) {
        // Add progress information for real-time tracking
        const now = new Date();
        const createdAt = new Date(task.createdAt);
        const elapsedTime = now.getTime() - createdAt.getTime();

        response.metadata = {
          elapsedTimeMs: elapsedTime,
          elapsedTimeFormatted: formatElapsedTime(elapsedTime),
          isCompleted: ["succeeded", "failed", "no_result"].includes(
            task.status
          ),
          shouldContinuePolling: ["pending", "processing"].includes(
            task.status
          ),
          recommendedPollInterval: task.status === "processing" ? 2000 : 5000, // ms
          lastChecked: now.toISOString(),
        };
      }

      // Set appropriate cache headers for real-time polling
      if (["pending", "processing"].includes(task.status)) {
        // Don't cache incomplete tasks
        c.header("Cache-Control", "no-cache, no-store, must-revalidate");
        c.header("Pragma", "no-cache");
        c.header("Expires", "0");
      } else {
        // Cache completed tasks for a short time
        c.header("Cache-Control", "public, max-age=300"); // 5 minutes
      }

      return c.json(response, 200);
    } catch (error: any) {
      console.error(`Error in GET /task/${params.taskId} endpoint:`, error);
      return c.json({ error: "Failed to retrieve task." }, 500);
    }
  }
}

// Helper function to format elapsed time
function formatElapsedTime(ms: number): string {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);

  if (hours > 0) {
    return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
}
