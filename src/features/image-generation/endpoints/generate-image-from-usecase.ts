// import { type Context } from "hono";
// import { OpenAPIRoute } from "chanfana";

// import {
//   GenericErrorSchema,
//   UsecaseImageGenerationRequestSchema,
//   UsecaseImageGenerationResponseSchema,
// } from "../image-generation.schema";
// import type { HonoEnv } from "../../../types";
// import {
//   IImageGenerationService,
//   type ImageGenerationService,
// } from "../image-generation.interface";
// import { IQuotaService, type QuotaService } from "../quota.interface";
// import {
//   IAnalyticsService,
//   type AnalyticsService,
// } from "../analytics.interface";

// export class GenerateImageFromUsecaseEndpoint extends OpenAPIRoute {
//   schema = {
//     summary: "Generate image from usecase",
//     description:
//       "Generates images based on a usecase name and provided image URLs.",
//     tags: ["Image Generation"],
//     request: {
//       body: {
//         content: {
//           "application/json": {
//             schema: UsecaseImageGenerationRequestSchema,
//           },
//         },
//       },
//     },
//     responses: {
//       "200": {
//         description: "Image(s) generated successfully from usecase.",
//         content: {
//           "application/json": {
//             schema: UsecaseImageGenerationResponseSchema,
//           },
//         },
//       },
//       "400": {
//         description: "Invalid request body.",
//         content: {
//           "application/json": {
//             schema: GenericErrorSchema,
//           },
//         },
//       },
//       "429": {
//         description:
//           "Quota exceeded (daily for free users, weekly for pro users).",
//         content: {
//           "application/json": {
//             schema: GenericErrorSchema,
//           },
//         },
//       },
//       "500": {
//         description: "Failed to generate image from usecase.",
//         content: {
//           "application/json": {
//             schema: GenericErrorSchema,
//           },
//         },
//       },
//     },
//   };

//   async handle(c: Context<HonoEnv>) {
//     const container = c.get("container");
//     const imageGenerationService = container.resolve<ImageGenerationService>(
//       IImageGenerationService
//     );
//     const quotaService = container.resolve<QuotaService>(IQuotaService);
//     const analyticsService =
//       container.resolve<AnalyticsService>(IAnalyticsService);

//     // Record the API call attempt
//     const endpointName = "generate-image-from-usecase";
//     await analyticsService.recordCall(endpointName);

//     const { body } = await this.getValidatedData<typeof this.schema>();
//     const { id, imageUrls, deviceId, userId, userType } = body;

//     try {
//       // Check quota before processing
//       const quota = await quotaService.checkQuota(
//         deviceId,
//         "image-generation",
//         userType
//       );
//       if (quota.remaining <= 0) {
//         const isWeeklyPro =
//           userType === "weekly_pro" || userType === "yearly_pro";
//         const periodText = isWeeklyPro ? "weekly" : "daily";
//         const resetText = isWeeklyPro ? "next week" : "tomorrow";

//         return c.json(
//           {
//             error: `${
//               periodText.charAt(0).toUpperCase() + periodText.slice(1)
//             } quota exceeded. Please try again ${resetText}.`,
//             quota,
//           },
//           429
//         ); // 429 Too Many Requests
//       }

//       // Consume quota before processing
//       await quotaService.consumeQuota(
//         deviceId,
//         "image-generation",
//         1,
//         userType
//       );

//       const result = await imageGenerationService.generateImageFromUsecase({
//         id,
//         imageUrls,
//         deviceId,
//         userId,
//         userType,
//       });

//       if (!result.success) {
//         await quotaService.refundQuota(
//           deviceId,
//           "image-generation",
//           1,
//           userType
//         );
//         // Record failure
//         await analyticsService.recordFailure(endpointName);
//       } else {
//         // Record success
//         await analyticsService.recordSuccess(endpointName);
//       }

//       return c.json(result, 200);
//     } catch (error: any) {
//       console.error("Error in POST /usecase endpoint:", error);

//       // Record failure for any exception
//       await analyticsService.recordFailure(endpointName);

//       // If it's a quota error, return specific error
//       if (error.message?.includes("quota exceeded")) {
//         return c.json({ error: error.message }, 429);
//       }

//       return c.json({ error: "Failed to generate image from usecase." }, 500);
//     }
//   }
// }
