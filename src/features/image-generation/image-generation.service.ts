import "reflect-metadata"; // Needed for tsyringe
import { singleton, inject } from "tsyringe";

import type {
  ImageGenerationRequest,
  Task,
  UsecaseImageGenerationRequest,
  UsecaseImageGenerationResponse,
  GetUserTasksResponse,
  GetUserTasksQuery,
  PaginationMeta,
  ImageEditRequest,
  ImageEditResponse,
} from "./image-generation.schema";
import { v4 as uuidv4 } from "uuid";

import { NotionService } from "../notion/notion.service";
import { ImageGenerationService as ImageGenerationServiceInterface } from "./image-generation.interface"; // Import the interface

import {
  IDbService,
  type DbService,
} from "../../infrastructure/db/db-service.interface";
import type { FirestoreClient } from "firebase-rest-firestore";
import {
  EnvService,
  IEnvService,
} from "../../infrastructure/env/env-service.interface";
import {
  IGptService,
  type GptService,
} from "../../infrastructure/ai/gpt.interface";
import {
  IFalAiService,
  type FalAiService,
} from "../../infrastructure/ai/fal-ai.interface";
import { addWatermark } from "../../infrastructure/common/watermark-service";
import { downloadImageToR2 } from "../../infrastructure/storage/r2-service";

const TASK_COLLECTION = "image-generation-tasks";

@singleton()
export class ImageGenerationService implements ImageGenerationServiceInterface {
  // Implement the interface
  private firestore: FirestoreClient;

  constructor(
    @inject(IDbService) private dbService: DbService,
    @inject(IEnvService) private envService: EnvService,
    @inject(NotionService) private notionService: NotionService,
    @inject(IGptService) private gptService: GptService,
    @inject(IFalAiService) private falAiService: FalAiService
  ) {
    this.firestore = this.dbService.getFirestoreInstance();
  }

  /**
   * Creates a new image generation task in Firestore and triggers the processing asynchronously.
   * @param userId - The ID of the user requesting the generation.
   * @param requestData - The validated request data containing image URLs and prompt.
   * @returns The ID of the created task.
   */
  async createImageGenerationTask(
    userId: string,
    requestData: ImageGenerationRequest,
    _waitUntil: (promise: Promise<any>) => void // Accept waitUntil as a parameter
  ): Promise<{ taskId: string }> {
    const taskId = uuidv4();
    const now = new Date(); // Use server timestamp
    console.log("now", now);
    console.log("taskId", taskId);

    const newTaskData: Omit<Task, "createdAt" | "updatedAt"> & {
      createdAt: Date;
      updatedAt: Date;
    } = {
      taskId,
      userId,
      status: "pending",
      prompt: requestData.prompt,
      inputImageUrls: requestData.imageUrls,
      batchId: requestData.batchId, // Add batchId support
      previousTaskId: requestData.previousTaskId, // Add previousTaskId support
      progress: 0, // Initialize progress to 0
      /**
       * @Warning deniffer: only for type inference  等待后续优化 or not!!!
       * use new Date() instead of serverTimestamp() Because now firebase-runtime is not supported
       * */
      createdAt: now,
      updatedAt: now,
    };

    try {
      // Create the task document in Firestore
      /**
       * @Warning deniffer: 这里使用 update 是因为 firebase-rest-firestore 的 set 行为和 firebase-admin 的 set 行为不一致
       * 导致在创建 task的时候，docId 和 taskId 不一致，深入看了代码之后发现 update 的行为可以 fix 这个问题
       * 所以使用 update 来创建任务
       */
      await this.firestore
        .collection(TASK_COLLECTION)
        .doc(taskId)
        .update(newTaskData);

      const { IMAGE_GENERATION_WORKFLOW } = this.envService.getBindings();

      const workflowInstance = await IMAGE_GENERATION_WORKFLOW.create({
        id: taskId + "-" + Math.random().toString(36).substring(2, 5),
        params: {
          taskId,
        },
      });
      console.log("created workflowInstance id: ", workflowInstance.id);

      return { taskId };
    } catch (error) {
      console.error(
        `[Task ${taskId}] Failed to create task in Firestore:`,
        error
      );
      // Consider how to handle this failure - maybe throw an error to be caught by the route handler
      throw new Error("Failed to create image generation task.");
    }
  }

  /**
   * Retrieves a specific task by its ID.
   * @param taskId - The ID of the task to retrieve.
   * @returns A promise that resolves to a Task object or null if not found.
   */
  public async getTaskById(taskId: string): Promise<Task | null> {
    try {
      const doc = await this.firestore
        .collection(TASK_COLLECTION)
        .doc(taskId)
        .get();

      if (!doc.exists) {
        return null;
      }

      const data = doc.data();
      return {
        taskId: data?.taskId,
        userId: data?.userId,
        status: data?.status,
        prompt: data?.prompt,
        inputImageUrls: data?.inputImageUrls,
        resultImageUrls: data?.resultImageUrls,
        errorMessage: data?.errorMessage,
        batchId: data?.batchId,
        previousTaskId: data?.previousTaskId,
        progress: data?.progress,
        createdAt: data?.createdAt,
        updatedAt: data?.updatedAt,
      } as Task;
    } catch (error) {
      console.error(`Failed to retrieve task ${taskId}:`, error);
      throw new Error("Failed to retrieve task.");
    }
  }

  /**
   * Retrieves tasks for a given user with pagination support and optional batch filtering.
   * @param userId - The ID of the user whose tasks are to be retrieved.
   * @param query - Optional pagination parameters (limit, page) and batch filtering (batchId).
   * @returns A promise that resolves to a paginated response with tasks and pagination metadata.
   */
  public async getUserTasks(
    userId: string,
    query?: GetUserTasksQuery
  ): Promise<GetUserTasksResponse> {
    try {
      const limit = query?.limit || 20;
      const page = query?.page || 1;
      const batchId = query?.batchId;
      const offset = (page - 1) * limit;

      // Build base query for counting
      let countQuery = this.firestore
        .collection(TASK_COLLECTION)
        .where("userId", "==", userId);

      // Add batchId filter if provided
      if (batchId) {
        countQuery = countQuery.where("batchId", "==", batchId);
      }

      // First, get the total count for pagination metadata
      const totalSnapshot = await countQuery.get();
      const totalItems = totalSnapshot.size;
      const totalPages = Math.ceil(totalItems / limit);

      // Build query for paginated results
      let query_builder = this.firestore
        .collection(TASK_COLLECTION)
        .where("userId", "==", userId);

      // Add batchId filter if provided
      if (batchId) {
        query_builder = query_builder.where("batchId", "==", batchId);
      }

      query_builder = query_builder.orderBy("createdAt", "desc");

      // Apply pagination
      if (offset > 0) {
        query_builder = query_builder.offset(offset);
      }
      query_builder = query_builder.limit(limit);

      const snapshot = await query_builder.get();

      const tasks = snapshot.docs.map((doc) => {
        const data = doc.data();
        return {
          ...data,
          taskId: data?.taskId,
          userId: data?.userId,
          status: data?.status,
          prompt: data?.prompt,
          inputImageUrls: data?.inputImageUrls,
          resultImageUrls: data?.resultImageUrls,
          errorMessage: data?.errorMessage,
          batchId: data?.batchId,
          previousTaskId: data?.previousTaskId,
          progress: data?.progress,
          createdAt: data?.createdAt,
          updatedAt: data?.updatedAt,
        } as Task;
      });

      const pagination: PaginationMeta = {
        currentPage: page,
        totalPages,
        totalItems,
        itemsPerPage: limit,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      };

      return {
        tasks,
        pagination,
      };
    } catch (error) {
      console.error(`Failed to retrieve tasks for user ${userId}:`, error);
      throw new Error("Failed to retrieve user tasks.");
    }
  }

  /**
   * Retrieves all tasks that belong to a specific batch.
   * @param batchId - The batch ID to filter tasks by.
   * @returns A promise that resolves to an array of Task objects.
   */
  public async getTasksByBatchId(batchId: string): Promise<Task[]> {
    try {
      const snapshot = await this.firestore
        .collection(TASK_COLLECTION)
        .where("batchId", "==", batchId)
        .orderBy("createdAt", "desc")
        .get();

      const tasks = snapshot.docs.map((doc) => {
        const data = doc.data();
        return {
          ...data,
          taskId: data?.taskId,
          userId: data?.userId,
          status: data?.status,
          prompt: data?.prompt,
          inputImageUrls: data?.inputImageUrls,
          resultImageUrls: data?.resultImageUrls,
          errorMessage: data?.errorMessage,
          batchId: data?.batchId,
          previousTaskId: data?.previousTaskId,
          progress: data?.progress,
          createdAt: data?.createdAt,
          updatedAt: data?.updatedAt,
        } as Task;
      });

      return tasks;
    } catch (error) {
      console.error(`Failed to retrieve tasks for batch ${batchId}:`, error);
      throw new Error("Failed to retrieve batch tasks.");
    }
  }

  /**
   * Generates an image directly based on a usecase ID and input image URLs.
   * Fetches the prompt from the UsecaseService (utilizing cache) and calls GPTService.
   * Adds watermark to the generated image and uploads both original and watermarked images to R2.
   * @param requestData - The request data containing usecaseId and imageUrls.
   * @returns The response containing both R2 storage URL and watermarked image URL.
   * @throws Error if usecase or prompt is not found, or if the API call fails.
   */
  public async generateImageFromUsecase(
    requestData: UsecaseImageGenerationRequest
  ): Promise<UsecaseImageGenerationResponse> {
    const { id, imageUrls } = requestData;

    // const usecase = await this.usecaseService.getGhibliUsecaseByName(name);
    const usecase = await this.notionService.getGhibliNotionTableEntryById(id);

    if (!usecase) {
      console.error(`Usecase not found for ID (name): ${id}`);
      throw new Error(`Usecase not found for ID: ${id}`);
    }

    if (!usecase.prompt) {
      console.error(`Prompt not found for usecase ID (name): ${id}`);
      throw new Error(`Prompt not found for usecase ID: ${id}`);
    }

    const prompt = usecase.prompt;
    console.log(
      `Generating image for usecaseId: ${id} with prompt: "${prompt}"`
    );

    // 2. Call GPTService to generate image
    try {
      console.log(
        `[Usecase ${id}] Calling GPTService.generateImage... with imageUrls: ${imageUrls}`
      );

      const generatedImageUrl = await this.gptService.generateImage(
        prompt,
        imageUrls
      );

      if (!generatedImageUrl) {
        console.error(`[Usecase ${id}] GPTService returned no image URL`);
        return {
          success: false,
          message: "Failed to generate image - no image URL returned",
        };
      }

      console.log(`[Usecase ${id}] Generated image URL: ${generatedImageUrl}`);

      // 3. Upload original image to R2 storage
      const env = this.envService.getBindings();
      const r2StorageUrl = await downloadImageToR2(
        generatedImageUrl,
        env.R2Bucket,
        env.R2_PUBLIC_URL
      );

      console.log(`[Usecase ${id}] Uploaded to R2: ${r2StorageUrl}`);

      // 4. Add watermark and upload watermarked image to R2
      const watermarkResult = await addWatermark({
        imageUrl: generatedImageUrl,
        env,
      });

      if (!watermarkResult.success || !watermarkResult.data) {
        console.error(
          `[Usecase ${id}] Failed to add watermark:`,
          watermarkResult.error
        );
        return {
          success: true,
          imageUrl: r2StorageUrl,
          message:
            "Image generated and uploaded to R2, but watermarking failed",
        };
      }

      const watermarkedImageUrl = watermarkResult.data.publicUrl;
      console.log(
        `[Usecase ${id}] Watermarked image URL: ${watermarkedImageUrl}`
      );

      return {
        success: true,
        imageUrl: r2StorageUrl,
        watermarkedImageUrl,
        message: "Image generated, watermarked, and uploaded successfully",
      };
    } catch (error: any) {
      console.error(
        `[Usecase ${id}] Error in generateImageFromUsecase:`,
        error
      );
      return {
        success: false,
        message: `Failed to generate image: ${error.message}`,
      };
    }
  }

  /**
   * Edits an image using fal-ai's flux-pro/kontext model.
   * @param requestData - The request data containing prompt and image URL.
   * @returns The response containing the edited image URL.
   */
  public async editImage(
    requestData: ImageEditRequest
  ): Promise<ImageEditResponse> {
    const { prompt, image_url } = requestData;

    try {
      console.log(`[ImageEdit] Starting image edit with prompt: "${prompt}"`);
      console.log(`[ImageEdit] Image URL: ${image_url}`);

      // 1. Call FalAI service to edit image
      const editedImageUrl = await this.falAiService.editImage({
        prompt,
        image_url,
      });

      if (!editedImageUrl) {
        console.error("[ImageEdit] FalAI service returned no image URL");
        return {
          success: false,
          message: "Failed to edit image - no image URL returned",
        };
      }

      console.log(`[ImageEdit] Edited image URL: ${editedImageUrl}`);

      // 2. Upload edited image to R2 storage
      const env = this.envService.getBindings();
      const r2StorageUrl = await downloadImageToR2(
        editedImageUrl,
        env.R2Bucket,
        env.R2_PUBLIC_URL
      );

      console.log(`[ImageEdit] Uploaded to R2: ${r2StorageUrl}`);

      return {
        success: true,
        imageUrl: r2StorageUrl,
        message: "Image edited and uploaded successfully",
      };
    } catch (error: any) {
      console.error("[ImageEdit] Error in editImage:", error);
      return {
        success: false,
        message: `Failed to edit image: ${error.message}`,
      };
    }
  }
}
