import { CreateImageGenerationTaskEndpoint } from "./endpoints/create-image-generation-task";
import { GetUserTasksHistoryEndpoint } from "./endpoints/get-user-tasks-history";
import { GetTaskByIdEndpoint } from "./endpoints/get-task-by-id";
import { GetTasksByBatchIdEndpoint } from "./endpoints/get-tasks-by-batch-id";
// import { GenerateImageFromUsecaseEndpoint } from "./endpoints/generate-image-from-usecase";
import { CheckQuotaEndpoint } from "./endpoints/check-quota";
import { AnalyticsEndpoint } from "./endpoints/analytics";
import { EditImageEndpoint } from "./endpoints/edit-image";
import { authMiddleware } from "../../middleware/auth.middleware";

export function registerImageGenerationRoutes(
  openapi: any,
  routePrefix: string = "/image-generation"
) {
  const base = routePrefix.startsWith("/") ? routePrefix : `/${routePrefix}`;

  openapi.post(base, authMiddleware, CreateImageGenerationTaskEndpoint);
  openapi.get(`${base}/history/:userId`, GetUserTasksHistoryEndpoint);
  openapi.get(`${base}/task/:taskId`, GetTaskByIdEndpoint);
  openapi.get(`${base}/batch/:batchId`, GetTasksByBatchIdEndpoint);
  // openapi.post(`${base}/usecase`, GenerateImageFromUsecaseEndpoint);
  openapi.get(`${base}/quota`, authMiddleware, CheckQuotaEndpoint);
  openapi.get(`${base}/analytics`, AnalyticsEndpoint);
  openapi.post(`${base}/edit`, EditImageEndpoint);
}
