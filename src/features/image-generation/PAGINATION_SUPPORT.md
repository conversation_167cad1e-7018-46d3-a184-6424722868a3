# Pagination Support for Image Generation History

This document describes the pagination functionality added to the user task history endpoint.

## Overview

The user task history endpoint now supports pagination to efficiently handle large numbers of tasks. This allows clients to:

- Retrieve tasks in manageable chunks
- Navigate through pages of results
- Get metadata about the total number of tasks and pages
- Implement efficient UI pagination controls

## API Changes

### Enhanced User Task History Endpoint

**Endpoint:** `GET /image-generation/history/{userId}`

**Query Parameters:**

- `limit` (optional): Number of tasks to return per page

  - Type: `string` (converted to number)
  - Range: 1-100
  - Default: 20
  - Example: `?limit=10`

- `page` (optional): Page number to retrieve

  - Type: `string` (converted to number)
  - Minimum: 1
  - Default: 1
  - Example: `?page=2`

- `batchId` (optional): Filter tasks by specific batch ID
  - Type: `string`
  - Example: `?batchId=batch-2024-01-15-001`
  - When provided, only returns tasks belonging to that batch

**Request Examples:**

```http
# Default pagination (20 items, page 1, all batches)
GET /image-generation/history/user-123

# Custom page size
GET /image-generation/history/user-123?limit=10

# Specific page
GET /image-generation/history/user-123?limit=10&page=2

# Filter by batch ID only
GET /image-generation/history/user-123?batchId=batch-2024-01-15-001

# Filter by batch ID with pagination
GET /image-generation/history/user-123?batchId=batch-2024-01-15-001&limit=5&page=1

# Complex query: specific batch, custom page size, second page
GET /image-generation/history/user-123?batchId=batch-2024-01-15-001&limit=10&page=2
```

**Response Format:**

```json
{
  "tasks": [
    {
      "taskId": "52df2e8e-e72d-455a-82fb-464fc7504040",
      "userId": "user-123",
      "status": "succeeded",
      "prompt": "replace the cute dragon with holy tiger",
      "inputImageUrls": ["https://example.com/input.jpg"],
      "resultImageUrls": ["https://example.com/result.jpg"],
      "batchId": "batch-2024-01-15-001",
      "createdAt": "2024-01-15T10:00:00Z",
      "updatedAt": "2024-01-15T10:05:00Z"
    }
  ],
  "pagination": {
    "currentPage": 1,
    "totalPages": 5,
    "totalItems": 47,
    "itemsPerPage": 10,
    "hasNextPage": true,
    "hasPreviousPage": false
  }
}
```

**Pagination Metadata:**

- `currentPage`: The current page number
- `totalPages`: Total number of pages available
- `totalItems`: Total number of tasks for the user
- `itemsPerPage`: Number of items requested per page
- `hasNextPage`: Boolean indicating if there's a next page
- `hasPreviousPage`: Boolean indicating if there's a previous page

**Status Codes:**

- `200`: Successfully retrieved paginated tasks
- `404`: No tasks found for the user (only on first page with no results)
- `400`: Invalid query parameters (limit/page out of range)
- `500`: Server error

## Validation Rules

### Limit Parameter

- Must be between 1 and 100
- Invalid values will result in a 400 error
- If not provided, defaults to 20

### Page Parameter

- Must be greater than 0
- Invalid values will result in a 400 error
- If not provided, defaults to 1
- Requesting a page beyond available data returns empty results with valid pagination metadata

## Implementation Details

### Database Queries

The implementation uses two Firestore queries:

1. **Count Query**: Gets total number of tasks for pagination metadata
2. **Data Query**: Gets the actual paginated results using `offset()` and `limit()`

### Performance Considerations

- The total count query is executed for every request to ensure accurate pagination metadata
- For large datasets, consider implementing cursor-based pagination in the future
- Results are ordered by `createdAt` in descending order (newest first)

## Usage Examples

### Basic Pagination UI

```javascript
async function loadUserTasks(userId, page = 1, limit = 20, batchId = null) {
  let url = `/image-generation/history/${userId}?limit=${limit}&page=${page}`;
  if (batchId) {
    url += `&batchId=${encodeURIComponent(batchId)}`;
  }

  const response = await fetch(url);
  const data = await response.json();

  return {
    tasks: data.tasks,
    pagination: data.pagination,
  };
}

// Usage examples
// Load all tasks for user
const { tasks, pagination } = await loadUserTasks("user-123", 1, 10);
console.log(`Page ${pagination.currentPage} of ${pagination.totalPages}`);
console.log(`Showing ${tasks.length} of ${pagination.totalItems} tasks`);

// Load tasks for specific batch
const batchTasks = await loadUserTasks(
  "user-123",
  1,
  10,
  "batch-2024-01-15-001"
);
console.log(
  `Batch tasks: ${batchTasks.tasks.length} of ${batchTasks.pagination.totalItems}`
);
```

### Pagination Controls

```javascript
function renderPaginationControls(pagination) {
  return {
    showPrevious: pagination.hasPreviousPage,
    showNext: pagination.hasNextPage,
    currentPage: pagination.currentPage,
    totalPages: pagination.totalPages,
    pageInfo: `${pagination.currentPage} of ${pagination.totalPages}`,
  };
}
```

## Testing

Use the provided `api-pagination-batch.http` file to test various pagination and batch filtering scenarios:

- Default pagination (all batches)
- Custom page sizes
- Batch filtering without pagination
- Batch filtering with pagination
- Edge cases (invalid parameters, empty results, non-existent batches)
- Navigation between pages within filtered results

## Backward Compatibility

The pagination parameters are optional, so existing clients will continue to work with default pagination settings (20 items per page, page 1).
