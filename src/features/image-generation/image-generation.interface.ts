import type { InjectionToken } from "tsyringe";
import type {
  UsecaseImageGenerationRequest,
  Task,
  ImageGenerationRequest,
  UsecaseImageGenerationResponse,
  GetUserTasksResponse,
  GetUserTasksQuery,
  ImageEditRequest,
  ImageEditResponse,
} from "./image-generation.schema"; // Added imports for schema types

// Define the injection token for ImageGenerationService
export const IImageGenerationService: InjectionToken<ImageGenerationService> =
  Symbol("IImageGenerationService");

/**
 * Abstract class representing the contract for an image generation service.
 * It should define methods for generating images, possibly from text prompts.
 */
export abstract class ImageGenerationService {
  abstract generateImageFromUsecase(
    requestData: UsecaseImageGenerationRequest
  ): Promise<UsecaseImageGenerationResponse>;
  abstract getUserTasks(
    userId: string,
    query?: GetUserTasksQuery
  ): Promise<GetUserTasksResponse>;
  abstract getTaskById(taskId: string): Promise<Task | null>;
  abstract getTasksByBatchId(batchId: string): Promise<Task[]>;
  abstract createImageGenerationTask(
    userId: string,
    requestData: ImageGenerationRequest,
    waitUntil: (promise: Promise<any>) => void // Accept waitUntil as a parameter
  ): Promise<{ taskId: string }>;
  abstract editImage(requestData: ImageEditRequest): Promise<ImageEditResponse>;
}
