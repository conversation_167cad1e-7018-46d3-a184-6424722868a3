@baseUrl = http://localhost:3000/api/v1
# @baseUrl = https://chat-to-design-backend.fly.dev/api/v1

### Test generate image from usecase endpoint (this will record analytics)
POST {{baseUrl}}/image-generation/usecase
Content-Type: application/json

{
    "id": "studio-ghibli",
    "imageUrls": ["https://pbs.twimg.com/media/Gpf1QX_a0AAyeia.jpg"],
    "deviceId": "test-device-123",
    "userId": "test-user-456",
    "userType": "free"
}

### Get analytics for today
GET {{baseUrl}}/image-generation/analytics?endpoint=generate-image-from-usecase

### Get analytics for a specific date
GET {{baseUrl}}/image-generation/analytics?endpoint=generate-image-from-usecase&date=2024-01-15

### Get analytics for a date range
GET {{baseUrl}}/image-generation/analytics?endpoint=generate-image-from-usecase&startDate=2024-01-01&endDate=2024-01-31
