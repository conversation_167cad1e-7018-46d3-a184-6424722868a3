# Batch Support and Pagination Summary

This document provides a quick overview of the batch support and pagination features implemented for the image generation system.

## Features Implemented

### 1. Batch Support
- **Task Creation**: Add optional `batchId` parameter when creating tasks
- **Batch Querying**: Retrieve all tasks belonging to a specific batch
- **User-Defined IDs**: Users can provide any string as `batchId`

### 2. Pagination Support
- **Configurable Page Size**: `limit` parameter (1-100, default: 20)
- **Page Navigation**: `page` parameter (starting from 1)
- **Metadata**: Complete pagination information in responses

### 3. Combined Filtering
- **Batch + Pagination**: Filter by `batchId` with pagination support
- **Flexible Queries**: All parameters are optional and can be combined

## API Endpoints

### Create Task with Batch ID
```http
POST /image-generation
{
  "imageUrls": ["https://example.com/image.jpg"],
  "prompt": "your prompt",
  "userId": "user-123",
  "batchId": "batch-2024-01-15-001"  // Optional
}
```

### Get Tasks by Batch ID
```http
GET /image-generation/batch/{batchId}
```

### Get User Tasks with Pagination and Batch Filtering
```http
# All combinations are supported:
GET /image-generation/history/{userId}
GET /image-generation/history/{userId}?limit=10
GET /image-generation/history/{userId}?limit=10&page=2
GET /image-generation/history/{userId}?batchId=batch-123
GET /image-generation/history/{userId}?batchId=batch-123&limit=5&page=1
```

## Response Format

### Paginated User Tasks Response
```json
{
  "tasks": [
    {
      "taskId": "uuid",
      "userId": "user-123",
      "status": "succeeded",
      "prompt": "task prompt",
      "batchId": "batch-123",
      "createdAt": "2024-01-15T10:00:00Z",
      "updatedAt": "2024-01-15T10:05:00Z"
    }
  ],
  "pagination": {
    "currentPage": 1,
    "totalPages": 5,
    "totalItems": 47,
    "itemsPerPage": 10,
    "hasNextPage": true,
    "hasPreviousPage": false
  }
}
```

## Key Benefits

1. **Batch Management**: Group related tasks for better organization
2. **Efficient Pagination**: Handle large datasets without performance issues
3. **Flexible Filtering**: Combine user, batch, and pagination filters
4. **Backward Compatibility**: Existing clients continue to work unchanged
5. **Rich Metadata**: Complete information for building UIs

## Use Cases

### Batch Processing Workflow
```javascript
const batchId = `batch-${Date.now()}`;

// Create multiple tasks
for (const task of tasks) {
  await createTask({ ...task, batchId });
}

// Monitor batch progress
const batchTasks = await getUserTasks(userId, { batchId });
const completed = batchTasks.tasks.filter(t => t.status === 'succeeded').length;
console.log(`Batch progress: ${completed}/${batchTasks.pagination.totalItems}`);
```

### Paginated UI
```javascript
// Load page with batch filter
const { tasks, pagination } = await getUserTasks(userId, {
  batchId: selectedBatch,
  limit: 10,
  page: currentPage
});

// Render pagination controls
renderPagination({
  current: pagination.currentPage,
  total: pagination.totalPages,
  hasNext: pagination.hasNextPage,
  hasPrev: pagination.hasPreviousPage
});
```

## Testing

Use the provided test files:
- `api-pagination-batch.http` - Comprehensive testing scenarios
- Test all parameter combinations
- Verify edge cases and error handling

## Files Modified

- `image-generation.schema.ts` - Added batch and pagination schemas
- `image-generation.interface.ts` - Updated service interface
- `image-generation.service.ts` - Implemented batch filtering and pagination
- `get-user-tasks-history.ts` - Updated endpoint to support new parameters
- `get-tasks-by-batch-id.ts` - New endpoint for batch queries
- `image-generation.routes.ts` - Added new route

## Backward Compatibility

✅ All existing functionality remains unchanged
✅ New parameters are optional
✅ Default behavior preserved
✅ Existing clients work without modification
