# Image Generation Analytics

这个模块为 `GenerateImageFromUsecaseEndpoint` 接口实现了调用统计功能，可以跟踪每天的调用次数、成功次数和失败次数，并将数据存储在 Cloudflare KV 中。

## 功能特性

- **自动统计记录**: 每次调用 `GenerateImageFromUsecaseEndpoint` 时自动记录统计数据
- **详细分类**: 分别记录总调用次数、成功次数、失败次数
- **KV 存储**: 使用 Cloudflare KV 进行持久化存储
- **按日期分组**: 统计数据按日期（YYYY-MM-DD）分组
- **查询接口**: 提供 REST API 查询统计数据

## 架构设计

### 核心组件

1. **AnalyticsService 接口** (`analytics.interface.ts`)

   - 定义统计服务的抽象接口
   - 包含记录调用、查询统计等方法

2. **KVAnalyticsService 实现** (`kv-analytics.service.ts`)

   - 基于 Cloudflare KV 的具体实现
   - 使用缓存服务进行数据存储

3. **AnalyticsEndpoint** (`endpoints/analytics.ts`)
   - 提供查询统计数据的 REST API
   - 支持单日查询和日期范围查询

### 数据结构

```typescript
interface DailyStats {
  date: string; // YYYY-MM-DD 格式
  totalCalls: number; // 总调用次数
  successCalls: number; // 成功次数
  failureCalls: number; // 失败次数
  lastUpdated: string; // 最后更新时间 (ISO 格式)
}
```

## 使用方法

### 1. 自动统计记录

统计功能已集成到 `GenerateImageFromUsecaseEndpoint` 中，无需额外配置：

- 每次接口调用时自动记录 `totalCalls`
- 根据处理结果自动记录 `successCalls` 或 `failureCalls`
- 异常情况也会被记录为失败

### 2. 查询统计数据

#### 查询今天的统计

```http
GET /api/v1/image-generation/analytics?endpoint=generate-image-from-usecase
```

#### 查询从指定日期到当前日期的统计

```http
GET /api/v1/image-generation/analytics?endpoint=generate-image-from-usecase&startDate=2024-01-15
```

#### 查询指定日期范围的统计

```http
GET /api/v1/image-generation/analytics?endpoint=generate-image-from-usecase&startDate=2024-01-01&endDate=2024-01-31
```

### 3. 响应格式

```json
{
  "success": true,
  "data": {
    "date": "2024-01-15",
    "totalCalls": 150,
    "successCalls": 142,
    "failureCalls": 8,
    "lastUpdated": "2024-01-15T14:30:00.000Z"
  }
}
```

对于日期范围查询，`data` 字段将是一个数组。

## 配置要求

### 1. KV 命名空间

确保在 `wrangler.jsonc` 中配置了 KV 命名空间：

```json
{
  "kv_namespaces": [
    {
      "binding": "KV",
      "id": "your-kv-namespace-id",
      "preview_id": "your-preview-kv-namespace-id"
    }
  ]
}
```

### 2. 依赖注入

统计服务已在 `container.setup.ts` 中注册：

```typescript
container.registerSingleton(IAnalyticsService, KVAnalyticsService);
```

## 扩展其他接口

要为其他接口添加统计功能，只需：

1. 在接口处理器中注入 `AnalyticsService`
2. 在适当的位置调用统计方法：

```typescript
// 记录调用
await analyticsService.recordCall("your-endpoint-name");

// 记录成功
await analyticsService.recordSuccess("your-endpoint-name");

// 记录失败
await analyticsService.recordFailure("your-endpoint-name");
```

## 性能考虑

- 统计数据缓存 24 小时，减少 KV 读取次数
- 异步记录，不影响主要业务逻辑性能
- KV 存储具有全球分布式特性，查询速度快

## 监控和维护

- 统计数据自动按日期分组，便于分析
- 可通过 Cloudflare 控制台直接查看 KV 存储的原始数据
- 建议定期备份重要的统计数据
