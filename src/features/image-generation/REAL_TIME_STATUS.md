# 图片生成任务实时状态查询

本文档介绍如何使用图片生成服务的实时状态查询功能。

## 概述

我们提供了一个优化的轮询方案来获取图片生成任务的实时状态，这个方案：

- ✅ **简单可靠**：基于标准的 HTTP GET 请求
- ✅ **适合 Cloudflare Workers**：无需长连接或复杂的状态管理
- ✅ **智能轮询**：根据任务状态自动调整轮询频率
- ✅ **缓存优化**：完成的任务会被缓存，进行中的任务不会被缓存
- ✅ **丰富的元数据**：提供进度信息和轮询建议

## API 接口

### GET /api/v1/image-generation/task/:taskId

获取指定任务的状态信息。

#### 请求参数

- **路径参数**：

  - `taskId` (string, required): 任务的 UUID

- **查询参数**：
  - `includeProgress` (boolean, optional): 是否包含进度信息，用于实时轮询优化

#### 响应格式

```typescript
interface TaskResponse {
  taskId: string;
  userId: string;
  status:
    | "pending"
    | "processing"
    | "succeeded"
    | "failed"
    | "no_result"
    | "deleted";
  prompt: string;
  inputImageUrls?: string[];
  resultImageUrls?: string[];
  errorMessage?: string;
  createdAt: string;
  updatedAt: string;

  // 当 includeProgress=true 时包含
  metadata?: {
    elapsedTimeMs: number; // 已用时间（毫秒）
    elapsedTimeFormatted: string; // 格式化的已用时间
    isCompleted: boolean; // 任务是否已完成
    shouldContinuePolling: boolean; // 是否应该继续轮询
    recommendedPollInterval: number; // 建议的轮询间隔（毫秒）
    lastChecked: string; // 最后检查时间
  };
}
```

#### 状态说明

- `pending`: 任务已创建，等待处理
- `processing`: 任务正在处理中
- `succeeded`: 任务成功完成
- `failed`: 任务处理失败
- `no_result`: 任务完成但没有生成结果
- `deleted`: 任务已被软删除

## 使用示例

### 1. 基本状态查询

```bash
curl "https://pic-api.a1d.ai/api/v1/image-generation/task/550e8400-e29b-41d4-a716-446655440000"
```

### 2. 包含进度信息的查询（推荐用于轮询）

```bash
curl "https://pic-api.a1d.ai/api/v1/image-generation/task/550e8400-e29b-41d4-a716-446655440000?includeProgress=true"
```

### 3. JavaScript 客户端轮询示例

```javascript
async function pollTaskStatus(
  taskId,
  baseUrl = "https://pic-api.a1d.ai/api/v1"
) {
  const maxAttempts = 150; // 最多轮询 150 次（约 5 分钟）
  let attempts = 0;

  while (attempts < maxAttempts) {
    try {
      const response = await fetch(
        `${baseUrl}/image-generation/task/${taskId}?includeProgress=true`
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const task = await response.json();
      console.log(`Task ${taskId} status: ${task.status}`);

      // 检查任务是否完成
      if (task.metadata?.isCompleted) {
        console.log("Task completed!", task);
        return task;
      }

      // 使用推荐的轮询间隔
      const pollInterval = task.metadata?.recommendedPollInterval || 3000;
      console.log(`Waiting ${pollInterval}ms before next check...`);

      await new Promise((resolve) => setTimeout(resolve, pollInterval));
      attempts++;
    } catch (error) {
      console.error("Polling error:", error);
      // 错误时使用指数退避
      await new Promise((resolve) =>
        setTimeout(resolve, Math.min(2000 * attempts, 10000))
      );
      attempts++;
    }
  }

  throw new Error("Polling timeout exceeded");
}

// 使用示例
pollTaskStatus("your-task-id")
  .then((task) => {
    if (task.status === "succeeded") {
      console.log("Generated images:", task.resultImageUrls);
    } else {
      console.log("Task failed or no result");
    }
  })
  .catch((error) => {
    console.error("Failed to get task result:", error);
  });
```

### 4. React Hook 示例

```javascript
import { useState, useEffect, useCallback } from "react";

function useTaskStatus(taskId, baseUrl = "https://pic-api.a1d.ai/api/v1") {
  const [task, setTask] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchTask = useCallback(async () => {
    try {
      const response = await fetch(
        `${baseUrl}/image-generation/task/${taskId}?includeProgress=true`
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const taskData = await response.json();
      setTask(taskData);
      setLoading(false);

      return taskData;
    } catch (err) {
      setError(err);
      setLoading(false);
      throw err;
    }
  }, [taskId, baseUrl]);

  useEffect(() => {
    if (!taskId) return;

    let timeoutId;

    const poll = async () => {
      try {
        const taskData = await fetchTask();

        // 如果任务未完成，继续轮询
        if (taskData.metadata?.shouldContinuePolling) {
          const interval = taskData.metadata.recommendedPollInterval || 3000;
          timeoutId = setTimeout(poll, interval);
        }
      } catch (err) {
        // 错误时重试
        timeoutId = setTimeout(poll, 5000);
      }
    };

    poll();

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [taskId, fetchTask]);

  return { task, loading, error, refetch: fetchTask };
}

// 在组件中使用
function TaskStatusComponent({ taskId }) {
  const { task, loading, error } = useTaskStatus(taskId);

  if (loading) return <div>Loading task status...</div>;
  if (error) return <div>Error: {error.message}</div>;
  if (!task) return <div>Task not found</div>;

  return (
    <div>
      <h3>Task Status: {task.status}</h3>
      {task.metadata && (
        <p>Elapsed time: {task.metadata.elapsedTimeFormatted}</p>
      )}
      {task.status === "succeeded" && task.resultImageUrls && (
        <div>
          <h4>Generated Images:</h4>
          {task.resultImageUrls.map((url, index) => (
            <img key={index} src={url} alt={`Generated ${index + 1}`} />
          ))}
        </div>
      )}
      {task.status === "failed" && <p>Error: {task.errorMessage}</p>}
    </div>
  );
}
```

## 最佳实践

### 1. 轮询频率建议

- **pending 状态**：每 5 秒轮询一次
- **processing 状态**：每 2 秒轮询一次
- **完成状态**：停止轮询

### 2. 错误处理

- 使用指数退避策略处理网络错误
- 设置最大轮询时间（建议 5-10 分钟）
- 记录错误日志便于调试

### 3. 性能优化

- 使用 `includeProgress=true` 获取智能轮询建议
- 完成的任务会被缓存，可以安全地重复查询
- 避免过于频繁的轮询（最小间隔建议 1 秒）

### 4. 用户体验

- 显示进度信息和已用时间
- 提供取消轮询的选项
- 在长时间等待时给用户适当的提示

## 高级用法

项目中提供了一个完整的轮询客户端实现，位于：
`src/features/image-generation/examples/real-time-polling-client.ts`

这个客户端包含了所有最佳实践，可以直接在你的项目中使用或作为参考。
