import { inject, singleton } from "tsyringe";
import type { CollectionReference } from "firebase-rest-firestore";
import { v4 as uuidv4 } from "uuid";

import { SocialService } from "./social.interface";
import type {
  AssetInteraction,
  CreateAssetInteractionData,
  InteractionType,
  AssetStats,
  UserInteractionQuery,
  UserInteractionListResponse,
} from "./social.schema";
import type { UserInteractionStates } from "./social.interface";
import {
  IDbService,
  type DbService,
} from "../../infrastructure/db/db-service.interface";
import { IAssetService, type AssetService } from "../assets/asset.interface";

@singleton()
export class FirestoreSocialService implements SocialService {
  private interactionsCollection: CollectionReference;
  private assetsCollection: CollectionReference;

  constructor(
    @inject(IDbService) private dbService: DbService,
    @inject(IAssetService) private assetService: AssetService
  ) {
    const firestore = this.dbService.getFirestoreInstance();
    this.interactionsCollection = firestore.collection("asset_interactions");
    this.assetsCollection = firestore.collection("assets");
  }

  async createInteraction(
    interactionData: CreateAssetInteractionData
  ): Promise<AssetInteraction> {
    try {
      const interaction: AssetInteraction = {
        id: uuidv4(),
        ...interactionData,
        createdAt: new Date(),
      };

      await this.interactionsCollection.doc(interaction.id).set({
        ...interaction,
        createdAt: interaction.createdAt.toISOString(),
      });

      console.log(`[SocialService] Created interaction: ${interaction.id}`);
      return interaction;
    } catch (error) {
      console.error("[SocialService] Error creating interaction:", error);
      throw new Error(
        `Failed to create interaction: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  async getInteraction(
    assetId: string,
    userId: string,
    type: InteractionType
  ): Promise<AssetInteraction | null> {
    try {
      const snapshot = await this.interactionsCollection
        .where("assetId", "==", assetId)
        .where("userId", "==", userId)
        .where("type", "==", type)
        .limit(1)
        .get();

      if (snapshot.docs.length === 0) {
        return null;
      }

      const doc = snapshot.docs[0];
      const data = doc.data();

      if (!data) {
        return null;
      }

      return {
        id: data.id,
        assetId: data.assetId,
        userId: data.userId,
        type: data.type,
        createdAt: new Date(data.createdAt),
        metadata: data.metadata,
      };
    } catch (error) {
      console.error("[SocialService] Error getting interaction:", error);
      throw new Error(
        `Failed to get interaction: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  async deleteInteraction(
    assetId: string,
    userId: string,
    type: InteractionType
  ): Promise<boolean> {
    try {
      const snapshot = await this.interactionsCollection
        .where("assetId", "==", assetId)
        .where("userId", "==", userId)
        .where("type", "==", type)
        .limit(1)
        .get();

      if (snapshot.docs.length === 0) {
        return false;
      }

      const doc = snapshot.docs[0];
      await this.interactionsCollection.doc(doc.id).delete();

      console.log(`[SocialService] Deleted interaction: ${doc.id}`);
      return true;
    } catch (error) {
      console.error("[SocialService] Error deleting interaction:", error);
      throw new Error(
        `Failed to delete interaction: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  async getUserInteractionStates(
    userId: string,
    assetIds: string[]
  ): Promise<UserInteractionStates> {
    try {
      const result: UserInteractionStates = {};

      // Initialize all assets with false states
      for (const assetId of assetIds) {
        result[assetId] = {
          assetId,
          isLikedByCurrentUser: false,
          isFavoritedByCurrentUser: false,
        };
      }

      if (assetIds.length === 0) {
        return result;
      }

      // Firestore 'in' query supports max 10 values, so we need to batch
      const batchSize = 10;
      const batches = [];

      for (let i = 0; i < assetIds.length; i += batchSize) {
        const batch = assetIds.slice(i, i + batchSize);
        batches.push(batch);
      }

      for (const batch of batches) {
        const snapshot = await this.interactionsCollection
          .where("userId", "==", userId)
          .where("assetId", "in", batch)
          .where("type", "in", ["like", "favorite"])
          .get();

        snapshot.docs.forEach((doc) => {
          const data = doc.data();
          if (data && result[data.assetId]) {
            if (data.type === "like") {
              result[data.assetId].isLikedByCurrentUser = true;
            } else if (data.type === "favorite") {
              result[data.assetId].isFavoritedByCurrentUser = true;
            }
          }
        });
      }

      return result;
    } catch (error) {
      console.error(
        "[SocialService] Error getting user interaction states:",
        error
      );
      throw new Error(
        `Failed to get user interaction states: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  async getAssetStats(assetId: string): Promise<AssetStats> {
    try {
      // Get asset to verify it exists and get current counts
      const asset = await this.assetService.getAssetById(assetId);
      if (!asset) {
        throw new Error("Asset not found");
      }

      return {
        assetId,
        stats: {
          likeCount: asset.likeCount || 0,
          favoriteCount: asset.favoriteCount || 0,
          shareCount: 0, // TODO: Implement share tracking
          viewCount: 0, // TODO: Implement view tracking
        },
      };
    } catch (error) {
      console.error("[SocialService] Error getting asset stats:", error);
      throw new Error(
        `Failed to get asset stats: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  async likeAsset(
    assetId: string,
    userId: string
  ): Promise<{ success: boolean; likeCount: number }> {
    try {
      // 1. Check if already liked
      const existingLike = await this.getInteraction(assetId, userId, "like");
      if (existingLike) {
        // Already liked, return current count
        const asset = await this.assetService.getAssetById(assetId);
        return {
          success: false,
          likeCount: asset?.likeCount || 0,
        };
      }

      // 2. First create interaction record (this is the source of truth)
      await this.createInteraction({
        assetId,
        userId,
        type: "like",
      });

      // 3. Then update asset stats (failure is tolerable)
      try {
        const asset = await this.assetService.getAssetById(assetId);
        if (asset) {
          const newLikeCount = (asset.likeCount || 0) + 1;
          // Use direct Firestore update for social stats (bypass user permission check)
          await this.updateAssetStatsDirect(assetId, {
            likeCount: newLikeCount,
          });

          console.log(
            `[SocialService] User ${userId} liked asset ${assetId}, count: ${newLikeCount}`
          );
          return { success: true, likeCount: newLikeCount };
        }
      } catch (updateError) {
        console.warn(
          `[SocialService] Stats update failed for asset ${assetId}, but interaction recorded:`,
          updateError
        );
        // Stats update failed, but interaction is recorded
        // Return estimated count or trigger background repair
      }

      // 4. If stats update failed, return estimated count
      const estimatedCount = await this.countInteractions(assetId, "like");
      console.log(
        `[SocialService] User ${userId} liked asset ${assetId}, estimated count: ${estimatedCount}`
      );
      return { success: true, likeCount: estimatedCount };
    } catch (error) {
      console.error("[SocialService] Error liking asset:", error);
      throw new Error(
        `Failed to like asset: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  async unlikeAsset(
    assetId: string,
    userId: string
  ): Promise<{ success: boolean; likeCount: number }> {
    try {
      // 1. Check if liked
      const existingLike = await this.getInteraction(assetId, userId, "like");
      if (!existingLike) {
        // Not liked, return current count
        const asset = await this.assetService.getAssetById(assetId);
        return {
          success: false,
          likeCount: asset?.likeCount || 0,
        };
      }

      // 2. First delete interaction record (this is the source of truth)
      const deleted = await this.deleteInteraction(assetId, userId, "like");
      if (!deleted) {
        throw new Error("Failed to delete like interaction");
      }

      // 3. Then update asset stats (failure is tolerable)
      try {
        const asset = await this.assetService.getAssetById(assetId);
        if (asset) {
          const newLikeCount = Math.max(0, (asset.likeCount || 0) - 1);
          await this.updateAssetStatsDirect(assetId, {
            likeCount: newLikeCount,
          });

          console.log(
            `[SocialService] User ${userId} unliked asset ${assetId}, count: ${newLikeCount}`
          );
          return { success: true, likeCount: newLikeCount };
        }
      } catch (updateError) {
        console.warn(
          `[SocialService] Stats update failed for asset ${assetId}, but interaction deleted:`,
          updateError
        );
        // Stats update failed, but interaction is deleted
      }

      // 4. If stats update failed, return estimated count
      const estimatedCount = await this.countInteractions(assetId, "like");
      console.log(
        `[SocialService] User ${userId} unliked asset ${assetId}, estimated count: ${estimatedCount}`
      );
      return { success: true, likeCount: estimatedCount };
    } catch (error) {
      console.error("[SocialService] Error unliking asset:", error);
      throw new Error(
        `Failed to unlike asset: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  async favoriteAsset(
    assetId: string,
    userId: string
  ): Promise<{ success: boolean; favoriteCount: number }> {
    try {
      // 1. Check if already favorited
      const existingFavorite = await this.getInteraction(
        assetId,
        userId,
        "favorite"
      );
      if (existingFavorite) {
        // Already favorited, return current count
        const asset = await this.assetService.getAssetById(assetId);
        return {
          success: false,
          favoriteCount: asset?.favoriteCount || 0,
        };
      }

      // 2. First create interaction record (this is the source of truth)
      await this.createInteraction({
        assetId,
        userId,
        type: "favorite",
      });

      // 3. Then update asset stats (failure is tolerable)
      try {
        const asset = await this.assetService.getAssetById(assetId);
        if (asset) {
          const newFavoriteCount = (asset.favoriteCount || 0) + 1;
          await this.updateAssetStatsDirect(assetId, {
            favoriteCount: newFavoriteCount,
          });

          console.log(
            `[SocialService] User ${userId} favorited asset ${assetId}, count: ${newFavoriteCount}`
          );
          return { success: true, favoriteCount: newFavoriteCount };
        }
      } catch (updateError) {
        console.warn(
          `[SocialService] Stats update failed for asset ${assetId}, but interaction recorded:`,
          updateError
        );
        // Stats update failed, but interaction is recorded
      }

      // 4. If stats update failed, return estimated count
      const estimatedCount = await this.countInteractions(assetId, "favorite");
      console.log(
        `[SocialService] User ${userId} favorited asset ${assetId}, estimated count: ${estimatedCount}`
      );
      return { success: true, favoriteCount: estimatedCount };
    } catch (error) {
      console.error("[SocialService] Error favoriting asset:", error);
      throw new Error(
        `Failed to favorite asset: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  async unfavoriteAsset(
    assetId: string,
    userId: string
  ): Promise<{ success: boolean; favoriteCount: number }> {
    try {
      // 1. Check if favorited
      const existingFavorite = await this.getInteraction(
        assetId,
        userId,
        "favorite"
      );
      if (!existingFavorite) {
        // Not favorited, return current count
        const asset = await this.assetService.getAssetById(assetId);
        return {
          success: false,
          favoriteCount: asset?.favoriteCount || 0,
        };
      }

      // 2. First delete interaction record (this is the source of truth)
      const deleted = await this.deleteInteraction(assetId, userId, "favorite");
      if (!deleted) {
        throw new Error("Failed to delete favorite interaction");
      }

      // 3. Then update asset stats (failure is tolerable)
      try {
        const asset = await this.assetService.getAssetById(assetId);
        if (asset) {
          const newFavoriteCount = Math.max(0, (asset.favoriteCount || 0) - 1);
          await this.updateAssetStatsDirect(assetId, {
            favoriteCount: newFavoriteCount,
          });

          console.log(
            `[SocialService] User ${userId} unfavorited asset ${assetId}, count: ${newFavoriteCount}`
          );
          return { success: true, favoriteCount: newFavoriteCount };
        }
      } catch (updateError) {
        console.warn(
          `[SocialService] Stats update failed for asset ${assetId}, but interaction deleted:`,
          updateError
        );
        // Stats update failed, but interaction is deleted
      }

      // 4. If stats update failed, return estimated count
      const estimatedCount = await this.countInteractions(assetId, "favorite");
      console.log(
        `[SocialService] User ${userId} unfavorited asset ${assetId}, estimated count: ${estimatedCount}`
      );
      return { success: true, favoriteCount: estimatedCount };
    } catch (error) {
      console.error("[SocialService] Error unfavoriting asset:", error);
      throw new Error(
        `Failed to unfavorite asset: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  async getUserInteractions(
    userId: string,
    type: InteractionType,
    query?: UserInteractionQuery
  ): Promise<UserInteractionListResponse> {
    try {
      const page = query?.page || 1;
      const limit = query?.limit || 20;
      const offset = (page - 1) * limit;

      // Get user interactions with pagination
      let interactionQuery = this.interactionsCollection
        .where("userId", "==", userId)
        .where("type", "==", type)
        .orderBy("createdAt", "desc")
        .limit(limit);

      if (offset > 0) {
        // For pagination, we need to use cursor-based pagination in production
        // For now, we'll use offset (not efficient for large datasets)
        interactionQuery = interactionQuery.offset(offset);
      }

      const snapshot = await interactionQuery.get();

      // Get total count for pagination
      const totalSnapshot = await this.interactionsCollection
        .where("userId", "==", userId)
        .where("type", "==", type)
        .get();

      const totalItems = totalSnapshot.docs.length;
      const totalPages = Math.ceil(totalItems / limit);

      // Get asset details for each interaction
      const items = [];
      for (const doc of snapshot.docs) {
        const interactionData = doc.data();
        if (!interactionData) continue;

        const asset = await this.assetService.getAssetById(
          interactionData.assetId
        );
        if (!asset) continue;

        items.push({
          id: interactionData.id,
          assetId: interactionData.assetId,
          type: interactionData.type,
          createdAt: interactionData.createdAt,
          asset: {
            id: asset.id,
            name: asset.name,
            type: asset.type,
            url: asset.url,
            thumbnailUrl: asset.thumbnailUrl,
            sourceType: asset.sourceType,
            generationPrompt: asset.generationPrompt,
            likeCount: asset.likeCount,
            favoriteCount: asset.favoriteCount,
            createdAt: asset.createdAt.toISOString(),
          },
        });
      }

      return {
        items,
        pagination: {
          currentPage: page,
          totalPages,
          totalItems,
          itemsPerPage: limit,
          hasNextPage: page < totalPages,
          hasPreviousPage: page > 1,
        },
      };
    } catch (error) {
      console.error("[SocialService] Error getting user interactions:", error);
      throw new Error(
        `Failed to get user interactions: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  async getAssetInteractions(
    assetId: string,
    type?: InteractionType,
    query?: UserInteractionQuery
  ): Promise<any> {
    try {
      const page = query?.page || 1;
      const limit = query?.limit || 20;
      const offset = (page - 1) * limit;

      // Build query
      let interactionQuery = this.interactionsCollection
        .where("assetId", "==", assetId)
        .orderBy("createdAt", "desc")
        .limit(limit);

      if (type) {
        interactionQuery = interactionQuery.where("type", "==", type);
      }

      if (offset > 0) {
        interactionQuery = interactionQuery.offset(offset);
      }

      const snapshot = await interactionQuery.get();

      // Get total count
      let totalQuery = this.interactionsCollection.where(
        "assetId",
        "==",
        assetId
      );
      if (type) {
        totalQuery = totalQuery.where("type", "==", type);
      }
      const totalSnapshot = await totalQuery.get();

      const totalItems = totalSnapshot.docs.length;
      const totalPages = Math.ceil(totalItems / limit);

      const interactions = snapshot.docs
        .map((doc) => {
          const data = doc.data();
          return {
            id: data?.id,
            assetId: data?.assetId,
            userId: data?.userId,
            type: data?.type,
            createdAt: new Date(data?.createdAt),
            metadata: data?.metadata,
          };
        })
        .filter(Boolean);

      return {
        interactions,
        pagination: {
          page,
          limit,
          total: totalItems,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      };
    } catch (error) {
      console.error("[SocialService] Error getting asset interactions:", error);
      throw new Error(
        `Failed to get asset interactions: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  async getAssetInteractionCounts(
    assetIds: string[]
  ): Promise<Record<string, { likeCount: number; favoriteCount: number }>> {
    try {
      const result: Record<
        string,
        { likeCount: number; favoriteCount: number }
      > = {};

      // Initialize all assets with zero counts
      for (const assetId of assetIds) {
        result[assetId] = { likeCount: 0, favoriteCount: 0 };
      }

      if (assetIds.length === 0) {
        return result;
      }

      // Get assets to get current counts (more efficient than counting interactions)
      const assets = await Promise.all(
        assetIds.map((id) => this.assetService.getAssetById(id))
      );

      assets.forEach((asset, index) => {
        if (asset) {
          const assetId = assetIds[index];
          result[assetId] = {
            likeCount: asset.likeCount || 0,
            favoriteCount: asset.favoriteCount || 0,
          };
        }
      });

      return result;
    } catch (error) {
      console.error(
        "[SocialService] Error getting asset interaction counts:",
        error
      );
      throw new Error(
        `Failed to get asset interaction counts: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Update asset statistics directly (bypasses user permission checks)
   * @param assetId - The asset ID
   * @param stats - The statistics to update
   * @returns Promise<void>
   */
  private async updateAssetStatsDirect(
    assetId: string,
    stats: { likeCount?: number; favoriteCount?: number }
  ): Promise<void> {
    try {
      await this.assetsCollection.doc(assetId).update(stats);
    } catch (error) {
      console.error(
        `[SocialService] Error updating asset stats for ${assetId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Count interactions for a specific asset and type
   * @param assetId - The asset ID
   * @param type - The interaction type
   * @returns Promise<number> - The count of interactions
   */
  private async countInteractions(
    assetId: string,
    type: InteractionType
  ): Promise<number> {
    try {
      const snapshot = await this.interactionsCollection
        .where("assetId", "==", assetId)
        .where("type", "==", type)
        .get();

      return snapshot.docs.length;
    } catch (error) {
      console.error(
        `[SocialService] Error counting ${type} interactions for asset ${assetId}:`,
        error
      );
      return 0; // Return 0 on error to avoid breaking the flow
    }
  }

  /**
   * Repair asset statistics by recalculating from interaction records
   * @param assetId - The asset ID to repair
   * @returns Promise<void>
   */
  async repairAssetStats(assetId: string): Promise<void> {
    try {
      const [likeCount, favoriteCount] = await Promise.all([
        this.countInteractions(assetId, "like"),
        this.countInteractions(assetId, "favorite"),
      ]);

      await this.updateAssetStatsDirect(assetId, {
        likeCount,
        favoriteCount,
      });

      console.log(
        `[SocialService] Repaired stats for asset ${assetId}: likes=${likeCount}, favorites=${favoriteCount}`
      );
    } catch (error) {
      console.error(
        `[SocialService] Failed to repair stats for asset ${assetId}:`,
        error
      );
      throw error;
    }
  }
}
