// Import endpoints
import { LikeAssetEndpoint, UnlikeAssetEndpoint } from "./endpoints/like-asset";
import { FavoriteAssetEndpoint, UnfavoriteAssetEndpoint } from "./endpoints/favorite-asset";
import { GetUserLikesEndpoint, GetUserFavoritesEndpoint } from "./endpoints/get-user-interactions";
import { GetAssetStatsEndpoint } from "./endpoints/get-asset-stats";

import { authMiddleware } from "../../middleware/auth.middleware";

/**
 * Register all social-related routes
 * @param openapi - The OpenAPI Hono instance
 * @param assetsBasePath - The base path for asset routes (e.g., "/api/v1/assets")
 * @param usersBasePath - The base path for user routes (e.g., "/api/v1/users")
 */
export function registerSocialRoutes(
  openapi: any,
  assetsBasePath: string,
  usersBasePath: string
) {
  // Asset-related social endpoints
  
  // POST /assets/:assetId/like - 点赞资产（需要认证）
  openapi.post(
    `${assetsBasePath}/:assetId/like`,
    authMiddleware,
    LikeAssetEndpoint
  );

  // DELETE /assets/:assetId/like - 取消点赞资产（需要认证）
  openapi.delete(
    `${assetsBasePath}/:assetId/like`,
    authMiddleware,
    UnlikeAssetEndpoint
  );

  // POST /assets/:assetId/favorite - 收藏资产（需要认证）
  openapi.post(
    `${assetsBasePath}/:assetId/favorite`,
    authMiddleware,
    FavoriteAssetEndpoint
  );

  // DELETE /assets/:assetId/favorite - 取消收藏资产（需要认证）
  openapi.delete(
    `${assetsBasePath}/:assetId/favorite`,
    authMiddleware,
    UnfavoriteAssetEndpoint
  );

  // GET /assets/:assetId/stats - 获取资产统计信息（无需认证）
  openapi.get(
    `${assetsBasePath}/:assetId/stats`,
    GetAssetStatsEndpoint
  );

  // User-related social endpoints

  // GET /users/me/likes - 获取用户点赞列表（需要认证）
  openapi.get(
    `${usersBasePath}/me/likes`,
    authMiddleware,
    GetUserLikesEndpoint
  );

  // GET /users/me/favorites - 获取用户收藏列表（需要认证）
  openapi.get(
    `${usersBasePath}/me/favorites`,
    authMiddleware,
    GetUserFavoritesEndpoint
  );
}
