### Social Features API Testing

# Implementation Note:
# This implementation uses eventual consistency approach since firebase-rest-firestore
# doesn't support transactions. The system prioritizes interaction records as the
# source of truth, with asset statistics updated separately (failure tolerant).

# Variables
@baseUrl = http://localhost:8787/api/v1
@authToken = YOUR_FIREBASE_JWT_TOKEN_HERE
@assetId = YOUR_ASSET_ID_HERE

### 1. Like an asset
POST {{baseUrl}}/assets/{{assetId}}/like
Authorization: Bearer {{authToken}}
Content-Type: application/json

### 2. Unlike an asset
DELETE {{baseUrl}}/assets/{{assetId}}/like
Authorization: Bearer {{authToken}}

### 3. Favorite an asset
POST {{baseUrl}}/assets/{{assetId}}/favorite
Authorization: Bearer {{authToken}}
Content-Type: application/json

### 4. Unfavorite an asset
DELETE {{baseUrl}}/assets/{{assetId}}/favorite
Authorization: Bearer {{authToken}}

### 5. Get asset statistics
GET {{baseUrl}}/assets/{{assetId}}/stats

### 6. Get user's likes
GET {{baseUrl}}/users/me/likes?page=1&limit=10
Authorization: Bearer {{authToken}}

### 7. Get user's favorites
GET {{baseUrl}}/users/me/favorites?page=1&limit=10
Authorization: Bearer {{authToken}}

### 8. Get user interaction status for an asset (from explore feature)
GET {{baseUrl}}/explore/{{assetId}}/interaction-status
Authorization: Bearer {{authToken}}

### 9. Get explore content with social stats
GET {{baseUrl}}/explore?page=1&limit=20&sortBy=popular

### Test Scenarios

### Scenario 1: Complete like workflow
# Step 1: Like an asset
POST {{baseUrl}}/assets/{{assetId}}/like
Authorization: Bearer {{authToken}}

# Step 2: Check asset stats (should show increased like count)
GET {{baseUrl}}/assets/{{assetId}}/stats

# Step 3: Check user's likes (should include this asset)
GET {{baseUrl}}/users/me/likes
Authorization: Bearer {{authToken}}

# Step 4: Check interaction status (should show liked)
GET {{baseUrl}}/explore/{{assetId}}/interaction-status
Authorization: Bearer {{authToken}}

# Step 5: Unlike the asset
DELETE {{baseUrl}}/assets/{{assetId}}/like
Authorization: Bearer {{authToken}}

# Step 6: Verify stats updated (should show decreased like count)
GET {{baseUrl}}/assets/{{assetId}}/stats

### Scenario 2: Complete favorite workflow
# Step 1: Favorite an asset
POST {{baseUrl}}/assets/{{assetId}}/favorite
Authorization: Bearer {{authToken}}

# Step 2: Check asset stats (should show increased favorite count)
GET {{baseUrl}}/assets/{{assetId}}/stats

# Step 3: Check user's favorites (should include this asset)
GET {{baseUrl}}/users/me/favorites
Authorization: Bearer {{authToken}}

# Step 4: Check interaction status (should show favorited)
GET {{baseUrl}}/explore/{{assetId}}/interaction-status
Authorization: Bearer {{authToken}}

# Step 5: Unfavorite the asset
DELETE {{baseUrl}}/assets/{{assetId}}/favorite
Authorization: Bearer {{authToken}}

# Step 6: Verify stats updated (should show decreased favorite count)
GET {{baseUrl}}/assets/{{assetId}}/stats

### Error Testing

### Test 1: Try to like without authentication
POST {{baseUrl}}/assets/{{assetId}}/like
Content-Type: application/json

### Test 2: Try to like non-existent asset
POST {{baseUrl}}/assets/non-existent-asset-id/like
Authorization: Bearer {{authToken}}
Content-Type: application/json

### Test 3: Try to get stats for non-existent asset
GET {{baseUrl}}/assets/non-existent-asset-id/stats

### Test 4: Try to get user interactions without authentication
GET {{baseUrl}}/users/me/likes

### Pagination Testing

### Test pagination for user likes
GET {{baseUrl}}/users/me/likes?page=1&limit=5
Authorization: Bearer {{authToken}}

### Test pagination for user favorites
GET {{baseUrl}}/users/me/favorites?page=1&limit=5
Authorization: Bearer {{authToken}}

### Integration Testing with Explore

### Test explore content with social features
GET {{baseUrl}}/explore?sortBy=popular&limit=10

### Test explore content filtering
GET {{baseUrl}}/explore?sourceType=ai_generated&type=image/*&sortBy=popular
