import type { InjectionToken } from "tsyringe";
import type {
  AssetInteraction,
  CreateAssetInteractionData,
  UpdateAssetInteractionData,
  InteractionType,
  AssetStats,
  UserInteractionQuery,
  UserInteractionListResponse,
} from "./social.schema";

// Define the injection token for SocialService
export const ISocialService: InjectionToken<SocialService> = Symbol("ISocialService");

/**
 * Social interaction list response interface
 */
export interface SocialInteractionListResponse {
  interactions: AssetInteraction[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

/**
 * User interaction state for multiple assets
 */
export interface UserInteractionStates {
  [assetId: string]: {
    assetId: string;
    isLikedByCurrentUser: boolean;
    isFavoritedByCurrentUser: boolean;
  };
}

/**
 * Abstract class representing the contract for a social service.
 * It defines methods for social interaction operations.
 */
export abstract class SocialService {
  /**
   * Create a new asset interaction
   * @param interactionData - The interaction data to create
   * @returns Promise<AssetInteraction> - The created interaction
   */
  abstract createInteraction(interactionData: CreateAssetInteractionData): Promise<AssetInteraction>;

  /**
   * Get an interaction by asset ID, user ID, and type
   * @param assetId - The asset ID
   * @param userId - The user ID
   * @param type - The interaction type
   * @returns Promise<AssetInteraction | null> - The interaction or null if not found
   */
  abstract getInteraction(
    assetId: string,
    userId: string,
    type: InteractionType
  ): Promise<AssetInteraction | null>;

  /**
   * Delete an interaction
   * @param assetId - The asset ID
   * @param userId - The user ID
   * @param type - The interaction type
   * @returns Promise<boolean> - True if deleted, false if not found
   */
  abstract deleteInteraction(
    assetId: string,
    userId: string,
    type: InteractionType
  ): Promise<boolean>;

  /**
   * Get user's interactions by type
   * @param userId - The user ID
   * @param type - The interaction type
   * @param query - Query parameters for pagination
   * @returns Promise<UserInteractionListResponse> - Paginated list of user interactions with asset details
   */
  abstract getUserInteractions(
    userId: string,
    type: InteractionType,
    query?: UserInteractionQuery
  ): Promise<UserInteractionListResponse>;

  /**
   * Get user's interaction states for multiple assets
   * @param userId - The user ID
   * @param assetIds - Array of asset IDs
   * @returns Promise<UserInteractionStates> - Map of asset ID to interaction state
   */
  abstract getUserInteractionStates(
    userId: string,
    assetIds: string[]
  ): Promise<UserInteractionStates>;

  /**
   * Get asset statistics
   * @param assetId - The asset ID
   * @returns Promise<AssetStats> - Asset statistics including like count, favorite count, etc.
   */
  abstract getAssetStats(assetId: string): Promise<AssetStats>;

  /**
   * Like an asset (with transaction to update asset like count)
   * @param assetId - The asset ID
   * @param userId - The user ID
   * @returns Promise<{ success: boolean; likeCount: number }> - Result with updated like count
   */
  abstract likeAsset(assetId: string, userId: string): Promise<{ success: boolean; likeCount: number }>;

  /**
   * Unlike an asset (with transaction to update asset like count)
   * @param assetId - The asset ID
   * @param userId - The user ID
   * @returns Promise<{ success: boolean; likeCount: number }> - Result with updated like count
   */
  abstract unlikeAsset(assetId: string, userId: string): Promise<{ success: boolean; likeCount: number }>;

  /**
   * Favorite an asset (with transaction to update asset favorite count)
   * @param assetId - The asset ID
   * @param userId - The user ID
   * @returns Promise<{ success: boolean; favoriteCount: number }> - Result with updated favorite count
   */
  abstract favoriteAsset(assetId: string, userId: string): Promise<{ success: boolean; favoriteCount: number }>;

  /**
   * Unfavorite an asset (with transaction to update asset favorite count)
   * @param assetId - The asset ID
   * @param userId - The user ID
   * @returns Promise<{ success: boolean; favoriteCount: number }> - Result with updated favorite count
   */
  abstract unfavoriteAsset(assetId: string, userId: string): Promise<{ success: boolean; favoriteCount: number }>;

  /**
   * Get interactions for an asset
   * @param assetId - The asset ID
   * @param type - Optional interaction type filter
   * @param query - Query parameters for pagination
   * @returns Promise<SocialInteractionListResponse> - Paginated list of interactions
   */
  abstract getAssetInteractions(
    assetId: string,
    type?: InteractionType,
    query?: UserInteractionQuery
  ): Promise<SocialInteractionListResponse>;

  /**
   * Get interaction counts for multiple assets
   * @param assetIds - Array of asset IDs
   * @returns Promise<Record<string, { likeCount: number; favoriteCount: number }>> - Map of asset ID to counts
   */
  abstract getAssetInteractionCounts(
    assetIds: string[]
  ): Promise<Record<string, { likeCount: number; favoriteCount: number }>>;
}
