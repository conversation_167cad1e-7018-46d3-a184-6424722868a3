import { z } from "zod";

// Asset interaction types
export const InteractionTypeSchema = z.enum(["like", "favorite", "view"]);
export type InteractionType = z.infer<typeof InteractionTypeSchema>;

// Asset interaction interface
export interface AssetInteraction {
  id: string;
  assetId: string; // 关联 Asset.id
  userId: string; // 操作用户ID
  type: InteractionType;
  createdAt: Date;
  metadata?: Record<string, any>; // 扩展信息（如分享平台等）
}

// Zod schema for AssetInteraction validation
export const AssetInteractionSchema = z.object({
  id: z.string().uuid(),
  assetId: z.string().min(1, "Asset ID is required"),
  userId: z.string().min(1, "User ID is required"),
  type: InteractionTypeSchema,
  createdAt: z.date(),
  metadata: z.record(z.any()).optional(),
});

// Schema for creating an interaction (without id and createdAt)
export const CreateAssetInteractionSchema = z.object({
  assetId: z.string().min(1, "Asset ID is required"),
  userId: z.string().min(1, "User ID is required"),
  type: InteractionTypeSchema,
  metadata: z.record(z.any()).optional(),
});

// Schema for updating an interaction
export const UpdateAssetInteractionSchema = z.object({
  metadata: z.record(z.any()).optional(),
});

// Type definitions
export type CreateAssetInteractionData = z.infer<typeof CreateAssetInteractionSchema>;
export type UpdateAssetInteractionData = z.infer<typeof UpdateAssetInteractionSchema>;

// Social action response schema
export const SocialActionResponseSchema = z.object({
  success: z.boolean(),
  likeCount: z.number().min(0).optional(),
  favoriteCount: z.number().min(0).optional(),
  message: z.string().optional(),
});

export type SocialActionResponse = z.infer<typeof SocialActionResponseSchema>;

// Asset statistics schema
export const AssetStatsSchema = z.object({
  assetId: z.string(),
  stats: z.object({
    likeCount: z.number().min(0),
    favoriteCount: z.number().min(0),
    shareCount: z.number().min(0).default(0),
    viewCount: z.number().min(0).default(0),
  }),
});

export type AssetStats = z.infer<typeof AssetStatsSchema>;

// User interaction list query schema
export const UserInteractionQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(50).default(20),
  type: InteractionTypeSchema.optional(),
});

export type UserInteractionQuery = z.infer<typeof UserInteractionQuerySchema>;

// User interaction list response schema
export const UserInteractionListResponseSchema = z.object({
  items: z.array(z.object({
    id: z.string(),
    assetId: z.string(),
    type: InteractionTypeSchema,
    createdAt: z.string().datetime(),
    asset: z.object({
      id: z.string(),
      name: z.string(),
      type: z.string(), // MIME type
      url: z.string().url(),
      thumbnailUrl: z.string().url().optional(),
      sourceType: z.enum(["ai_generated", "user_upload"]),
      generationPrompt: z.string().optional(),
      likeCount: z.number().min(0),
      favoriteCount: z.number().min(0),
      createdAt: z.string().datetime(),
    }),
  })),
  pagination: z.object({
    currentPage: z.number(),
    totalPages: z.number(),
    totalItems: z.number(),
    itemsPerPage: z.number(),
    hasNextPage: z.boolean(),
    hasPreviousPage: z.boolean(),
  }),
});

export type UserInteractionListResponse = z.infer<typeof UserInteractionListResponseSchema>;

// Batch interaction status query
export const BatchInteractionStatusQuerySchema = z.object({
  assetIds: z.array(z.string().min(1)).min(1).max(50), // 最多查询50个资产的状态
});

export type BatchInteractionStatusQuery = z.infer<typeof BatchInteractionStatusQuerySchema>;

// Batch interaction status response
export const BatchInteractionStatusResponseSchema = z.object({
  interactions: z.record(z.string(), z.object({
    assetId: z.string(),
    isLikedByCurrentUser: z.boolean(),
    isFavoritedByCurrentUser: z.boolean(),
  })),
});

export type BatchInteractionStatusResponse = z.infer<typeof BatchInteractionStatusResponseSchema>;
