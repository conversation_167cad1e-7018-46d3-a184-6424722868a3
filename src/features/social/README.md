# Social Features

## 概述

社交功能模块为 Explore 功能提供完整的用户交互支持，包括点赞、收藏、用户交互历史查询等功能。该模块与 Asset 系统和 Explore 功能深度集成，提供实时的社交统计和用户交互状态。

## 🚀 核心功能

### 1. 点赞功能

- ✅ 点赞资产 (`POST /api/v1/assets/:assetId/like`)
- ✅ 取消点赞 (`DELETE /api/v1/assets/:assetId/like`)
- ✅ 事务性统计更新，确保数据一致性
- ✅ 防重复点赞机制

### 2. 收藏功能

- ✅ 收藏资产 (`POST /api/v1/assets/:assetId/favorite`)
- ✅ 取消收藏 (`DELETE /api/v1/assets/:assetId/favorite`)
- ✅ 事务性统计更新，确保数据一致性
- ✅ 防重复收藏机制

### 3. 用户交互历史

- ✅ 获取用户点赞列表 (`GET /api/v1/users/me/likes`)
- ✅ 获取用户收藏列表 (`GET /api/v1/users/me/favorites`)
- ✅ 支持分页查询
- ✅ 包含完整的资产详细信息

### 4. 资产统计

- ✅ 获取资产统计信息 (`GET /api/v1/assets/:assetId/stats`)
- ✅ 实时点赞数和收藏数
- ✅ 为未来扩展预留字段（分享数、浏览数等）

### 5. 用户交互状态

- ✅ 批量查询用户对多个资产的交互状态
- ✅ 与 Explore 功能集成
- ✅ 高效的批量查询优化

## 📊 数据模型

### AssetInteraction

```typescript
interface AssetInteraction {
  id: string; // 交互记录ID
  assetId: string; // 关联的资产ID
  userId: string; // 用户ID
  type: "like" | "favorite"; // 交互类型
  createdAt: Date; // 创建时间
  metadata?: Record<string, any>; // 扩展元数据
}
```

### 数据库集合

- **asset_interactions**: 存储所有用户交互记录
- **assets**: 扩展了 `likeCount` 和 `favoriteCount` 字段

## 🏗️ 架构设计

### 服务层

- **SocialService** - 抽象接口定义
- **FirestoreSocialService** - Firestore 数据库实现

### API 层

- **LikeAssetEndpoint** / **UnlikeAssetEndpoint** - 点赞功能
- **FavoriteAssetEndpoint** / **UnfavoriteAssetEndpoint** - 收藏功能
- **GetUserLikesEndpoint** / **GetUserFavoritesEndpoint** - 用户历史
- **GetAssetStatsEndpoint** - 资产统计

### 数据层

- 使用 Firestore 事务确保统计数据一致性
- 批量查询优化，避免 N+1 问题
- 复合索引支持高效查询

## 🔄 与现有系统集成

### Asset 系统集成

- 扩展 Asset 数据模型，添加社交统计字段
- 事务性更新确保统计数据准确性
- 复用现有的 AssetService 进行资产验证

### Explore 系统集成

- ExploreService 集成 SocialService
- 提供用户交互状态查询
- 支持按热门度排序（基于点赞数和收藏数）

### 用户系统集成

- 基于 Firebase 认证的用户身份验证
- 用户交互历史查询
- 支持用户隐私控制

## 🚀 API 端点

### 社交交互

```http
POST   /api/v1/assets/:assetId/like      # 点赞资产
DELETE /api/v1/assets/:assetId/like      # 取消点赞
POST   /api/v1/assets/:assetId/favorite  # 收藏资产
DELETE /api/v1/assets/:assetId/favorite  # 取消收藏
```

### 统计查询

```http
GET /api/v1/assets/:assetId/stats        # 获取资产统计
```

### 用户历史

```http
GET /api/v1/users/me/likes               # 用户点赞列表
GET /api/v1/users/me/favorites           # 用户收藏列表
```

### 交互状态（Explore 集成）

```http
GET /api/v1/explore/:assetId/interaction-status  # 用户交互状态
```

## 🔧 技术实现

### 最终一致性操作

由于 `firebase-rest-firestore` 不支持事务，采用最终一致性方案：

```typescript
// 1. 优先保证交互记录（真相来源）
await this.createInteraction({ assetId, userId, type: "like" });

// 2. 然后更新统计（失败可容忍）
try {
  await this.updateAssetStatsDirect(assetId, { likeCount: newCount });
} catch (error) {
  // 统计更新失败，但交互记录已保存
  // 可以通过 repairAssetStats 方法修复
}
```

### 批量查询优化

用户交互状态查询支持批量操作，避免 N+1 问题：

```typescript
// 支持一次查询多个资产的用户交互状态
const states = await socialService.getUserInteractionStates(userId, assetIds);
```

### 分页支持

所有列表查询都支持分页：

```typescript
interface UserInteractionQuery {
  page?: number; // 页码，默认 1
  limit?: number; // 每页数量，默认 20，最大 50
}
```

### 数据修复机制

提供统计数据修复功能，确保最终一致性：

```typescript
// 修复单个资产的统计数据
await socialService.repairAssetStats(assetId);

// 该方法会：
// 1. 重新计算真实的点赞数和收藏数
// 2. 更新 Asset 表中的统计字段
// 3. 确保数据最终一致
```

## 🛡️ 安全特性

### 认证和授权

- 所有写操作都需要 Firebase JWT 认证
- 用户只能操作自己的交互记录
- 资产统计查询无需认证（公开信息）

### 防重复操作

- 点赞/收藏前检查是否已存在
- 取消操作前检查是否已点赞/收藏
- 返回当前统计数据而不是错误

### 数据验证

- 使用 Zod 进行请求参数验证
- 资产 ID 格式验证
- 分页参数范围限制

## 📈 性能优化

### 数据库索引

```javascript
// asset_interactions 集合索引
-(assetId, type) - // 按资产查询交互
  (userId, type, createdAt) - // 用户交互历史
  (userId, assetId); // 用户对特定资产的交互
```

### 查询优化

- 批量查询减少数据库调用
- 使用资产统计字段而非实时计算
- 分页查询减少数据传输

### 缓存策略

- 热门资产统计可缓存
- 用户交互状态可短期缓存
- 支持 CDN 缓存公开统计数据

## 🧪 测试

### API 测试

使用 `api.http` 文件进行完整的 API 测试：

1. 替换 `{{authToken}}` 为有效的 Firebase JWT
2. 替换 `{{assetId}}` 为实际的资产 ID
3. 运行各个测试场景

### 测试场景

- ✅ 完整的点赞/取消点赞流程
- ✅ 完整的收藏/取消收藏流程
- ✅ 用户交互历史查询
- ✅ 资产统计查询
- ✅ 错误处理测试
- ✅ 分页功能测试

## 🔄 扩展性

### 未来功能

- 分享功能和分享统计
- 浏览量统计
- 用户关注和粉丝系统
- 评论和回复功能
- 内容推荐算法

### 数据扩展

- AssetInteraction 支持更多交互类型
- metadata 字段支持扩展信息
- 支持交互来源追踪（如分享平台）

## 📝 使用示例

### 基础操作

```javascript
// 点赞资产
const response = await fetch("/api/v1/assets/asset-123/like", {
  method: "POST",
  headers: { Authorization: "Bearer " + token },
});

// 获取用户收藏列表
const favorites = await fetch("/api/v1/users/me/favorites?page=1&limit=10", {
  headers: { Authorization: "Bearer " + token },
});
```

### 与 Explore 集成

```javascript
// 获取热门内容（基于社交统计）
const explore = await fetch("/api/v1/explore?sortBy=popular&limit=20");

// 检查用户交互状态
const status = await fetch("/api/v1/explore/asset-123/interaction-status", {
  headers: { Authorization: "Bearer " + token },
});
```
