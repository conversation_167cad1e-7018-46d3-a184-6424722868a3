import { type Context } from "hono";
import { OpenAPIRoute } from "chanfana";
import { z } from "zod";

import { SocialActionResponseSchema } from "../social.schema";
import { SocialService, ISocialService } from "../social.interface";
import { ErrorResponseSchema } from "../../../shared/error-response.schema";
import type { HonoEnv } from "../../../types";
import { getFirebaseToken } from "@hono/firebase-auth";

// Path parameter schema
const LikeAssetParamsSchema = z.object({
  assetId: z.string().min(1, "Asset ID is required"),
});

export class LikeAssetEndpoint extends OpenAPIRoute {
  schema = {
    summary: "点赞资产",
    description: "为指定资产添加点赞，如果已经点赞则返回当前点赞数",
    tags: ["Social"],
    security: [
      {
        BearerAuth: [],
      },
    ],
    request: {
      params: LikeAssetParamsSchema,
    },
    responses: {
      "200": {
        description: "成功点赞或已经点赞",
        content: {
          "application/json": {
            schema: SocialActionResponseSchema,
          },
        },
      },
      "401": {
        description: "未认证或认证失效",
        content: {
          "application/json": {
            schema: ErrorResponseSchema,
          },
        },
      },
      "404": {
        description: "资产不存在",
        content: {
          "application/json": {
            schema: ErrorResponseSchema,
          },
        },
      },
      "500": {
        description: "服务器内部错误",
        content: {
          "application/json": {
            schema: ErrorResponseSchema,
          },
        },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    try {
      const container = c.get("container");
      const socialService = container.resolve<SocialService>(ISocialService);

      const firebaseToken = getFirebaseToken(c);
      if (!firebaseToken) {
        return c.json({ error: "Authentication required" }, 401);
      }
      const userId = firebaseToken.uid;

      const { params } = await this.getValidatedData<typeof this.schema>();
      const { assetId } = params;

      console.log(
        `[LikeAsset] User ${userId} attempting to like asset ${assetId}`
      );

      // Like the asset
      const result = await socialService.likeAsset(assetId, userId);

      const response = {
        success: result.success,
        likeCount: result.likeCount,
        message: result.success ? "Asset liked successfully" : "Asset already liked",
      };

      console.log(
        `[LikeAsset] ${result.success ? "Successfully liked" : "Already liked"} asset ${assetId} by user ${userId}`
      );

      return c.json(response, 200);
    } catch (error) {
      console.error("[LikeAsset] Error:", error);

      if (error instanceof Error && error.message.includes("not found")) {
        return c.json(
          {
            error: "Asset not found",
            code: "NOT_FOUND",
            details: "The requested asset does not exist",
          },
          404
        );
      }

      return c.json(
        {
          error: "Failed to like asset",
          code: "INTERNAL_ERROR",
          details: error instanceof Error ? error.message : "Unknown error",
        },
        500
      );
    }
  }
}

export class UnlikeAssetEndpoint extends OpenAPIRoute {
  schema = {
    summary: "取消点赞资产",
    description: "取消对指定资产的点赞，如果未点赞则返回当前点赞数",
    tags: ["Social"],
    security: [
      {
        BearerAuth: [],
      },
    ],
    request: {
      params: LikeAssetParamsSchema,
    },
    responses: {
      "200": {
        description: "成功取消点赞或未点赞",
        content: {
          "application/json": {
            schema: SocialActionResponseSchema,
          },
        },
      },
      "401": {
        description: "未认证或认证失效",
        content: {
          "application/json": {
            schema: ErrorResponseSchema,
          },
        },
      },
      "404": {
        description: "资产不存在",
        content: {
          "application/json": {
            schema: ErrorResponseSchema,
          },
        },
      },
      "500": {
        description: "服务器内部错误",
        content: {
          "application/json": {
            schema: ErrorResponseSchema,
          },
        },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    try {
      const container = c.get("container");
      const socialService = container.resolve<SocialService>(ISocialService);

      const firebaseToken = getFirebaseToken(c);
      if (!firebaseToken) {
        return c.json({ error: "Authentication required" }, 401);
      }
      const userId = firebaseToken.uid;

      const { params } = await this.getValidatedData<typeof this.schema>();
      const { assetId } = params;

      console.log(
        `[UnlikeAsset] User ${userId} attempting to unlike asset ${assetId}`
      );

      // Unlike the asset
      const result = await socialService.unlikeAsset(assetId, userId);

      const response = {
        success: result.success,
        likeCount: result.likeCount,
        message: result.success ? "Asset unliked successfully" : "Asset not liked",
      };

      console.log(
        `[UnlikeAsset] ${result.success ? "Successfully unliked" : "Not liked"} asset ${assetId} by user ${userId}`
      );

      return c.json(response, 200);
    } catch (error) {
      console.error("[UnlikeAsset] Error:", error);

      if (error instanceof Error && error.message.includes("not found")) {
        return c.json(
          {
            error: "Asset not found",
            code: "NOT_FOUND",
            details: "The requested asset does not exist",
          },
          404
        );
      }

      return c.json(
        {
          error: "Failed to unlike asset",
          code: "INTERNAL_ERROR",
          details: error instanceof Error ? error.message : "Unknown error",
        },
        500
      );
    }
  }
}
