import { type Context } from "hono";
import { OpenAPIRoute } from "chanfana";
import { z } from "zod";

import { AssetStatsSchema } from "../social.schema";
import { SocialService, ISocialService } from "../social.interface";
import { ErrorResponseSchema } from "../../../shared/error-response.schema";
import type { HonoEnv } from "../../../types";

// Path parameter schema
const AssetStatsParamsSchema = z.object({
  assetId: z.string().min(1, "Asset ID is required"),
});

export class GetAssetStatsEndpoint extends OpenAPIRoute {
  schema = {
    summary: "获取资产统计信息",
    description: "获取指定资产的详细统计信息，包括点赞数、收藏数等",
    tags: ["Social"],
    request: {
      params: AssetStatsParamsSchema,
    },
    responses: {
      "200": {
        description: "成功获取资产统计信息",
        content: {
          "application/json": {
            schema: AssetStatsSchema,
          },
        },
      },
      "404": {
        description: "资产不存在",
        content: {
          "application/json": {
            schema: ErrorResponseSchema,
          },
        },
      },
      "500": {
        description: "服务器内部错误",
        content: {
          "application/json": {
            schema: ErrorResponseSchema,
          },
        },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    try {
      const container = c.get("container");
      const socialService = container.resolve<SocialService>(ISocialService);

      const { params } = await this.getValidatedData<typeof this.schema>();
      const { assetId } = params;

      console.log(`[GetAssetStats] Getting stats for asset ${assetId}`);

      // Get asset statistics
      const stats = await socialService.getAssetStats(assetId);

      console.log(
        `[GetAssetStats] Successfully retrieved stats for asset ${assetId}:`,
        stats.stats
      );

      return c.json(stats, 200);
    } catch (error) {
      console.error("[GetAssetStats] Error:", error);

      if (error instanceof Error && error.message.includes("not found")) {
        return c.json(
          {
            error: "Asset not found",
            code: "NOT_FOUND",
            details: "The requested asset does not exist",
          },
          404
        );
      }

      return c.json(
        {
          error: "Failed to get asset stats",
          code: "INTERNAL_ERROR",
          details: error instanceof Error ? error.message : "Unknown error",
        },
        500
      );
    }
  }
}
