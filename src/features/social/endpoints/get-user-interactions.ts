import { type Context } from "hono";
import { OpenAPIRoute } from "chanfana";
import { z } from "zod";

import { UserInteractionQuerySchema, UserInteractionListResponseSchema } from "../social.schema";
import { SocialService, ISocialService } from "../social.interface";
import { ErrorResponseSchema } from "../../../shared/error-response.schema";
import type { HonoEnv } from "../../../types";
import { getFirebaseToken } from "@hono/firebase-auth";

export class GetUserLikesEndpoint extends OpenAPIRoute {
  schema = {
    summary: "获取用户点赞列表",
    description: "获取当前用户的点赞历史，包含资产详细信息",
    tags: ["Social"],
    security: [
      {
        BearerAuth: [],
      },
    ],
    request: {
      query: UserInteractionQuerySchema,
    },
    responses: {
      "200": {
        description: "成功获取用户点赞列表",
        content: {
          "application/json": {
            schema: UserInteractionListResponseSchema,
          },
        },
      },
      "401": {
        description: "未认证或认证失效",
        content: {
          "application/json": {
            schema: ErrorResponseSchema,
          },
        },
      },
      "500": {
        description: "服务器内部错误",
        content: {
          "application/json": {
            schema: ErrorResponseSchema,
          },
        },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    try {
      const container = c.get("container");
      const socialService = container.resolve<SocialService>(ISocialService);

      const firebaseToken = getFirebaseToken(c);
      if (!firebaseToken) {
        return c.json({ error: "Authentication required" }, 401);
      }
      const userId = firebaseToken.uid;

      const { query } = await this.getValidatedData<typeof this.schema>();

      console.log(
        `[GetUserLikes] Getting likes for user ${userId} with query:`,
        query
      );

      // Get user's likes
      const result = await socialService.getUserInteractions(userId, "like", query);

      console.log(
        `[GetUserLikes] Successfully retrieved ${result.items.length} likes for user ${userId}`
      );

      return c.json(result, 200);
    } catch (error) {
      console.error("[GetUserLikes] Error:", error);

      return c.json(
        {
          error: "Failed to get user likes",
          code: "INTERNAL_ERROR",
          details: error instanceof Error ? error.message : "Unknown error",
        },
        500
      );
    }
  }
}

export class GetUserFavoritesEndpoint extends OpenAPIRoute {
  schema = {
    summary: "获取用户收藏列表",
    description: "获取当前用户的收藏历史，包含资产详细信息",
    tags: ["Social"],
    security: [
      {
        BearerAuth: [],
      },
    ],
    request: {
      query: UserInteractionQuerySchema,
    },
    responses: {
      "200": {
        description: "成功获取用户收藏列表",
        content: {
          "application/json": {
            schema: UserInteractionListResponseSchema,
          },
        },
      },
      "401": {
        description: "未认证或认证失效",
        content: {
          "application/json": {
            schema: ErrorResponseSchema,
          },
        },
      },
      "500": {
        description: "服务器内部错误",
        content: {
          "application/json": {
            schema: ErrorResponseSchema,
          },
        },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    try {
      const container = c.get("container");
      const socialService = container.resolve<SocialService>(ISocialService);

      const firebaseToken = getFirebaseToken(c);
      if (!firebaseToken) {
        return c.json({ error: "Authentication required" }, 401);
      }
      const userId = firebaseToken.uid;

      const { query } = await this.getValidatedData<typeof this.schema>();

      console.log(
        `[GetUserFavorites] Getting favorites for user ${userId} with query:`,
        query
      );

      // Get user's favorites
      const result = await socialService.getUserInteractions(userId, "favorite", query);

      console.log(
        `[GetUserFavorites] Successfully retrieved ${result.items.length} favorites for user ${userId}`
      );

      return c.json(result, 200);
    } catch (error) {
      console.error("[GetUserFavorites] Error:", error);

      return c.json(
        {
          error: "Failed to get user favorites",
          code: "INTERNAL_ERROR",
          details: error instanceof Error ? error.message : "Unknown error",
        },
        500
      );
    }
  }
}
