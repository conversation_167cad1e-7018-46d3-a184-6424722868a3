import { type Context } from "hono";
import { OpenAPIRoute } from "chanfana";
import { z } from "zod";

import { SocialActionResponseSchema } from "../social.schema";
import { SocialService, ISocialService } from "../social.interface";
import { ErrorResponseSchema } from "../../../shared/error-response.schema";
import type { HonoEnv } from "../../../types";
import { getFirebaseToken } from "@hono/firebase-auth";

// Path parameter schema
const FavoriteAssetParamsSchema = z.object({
  assetId: z.string().min(1, "Asset ID is required"),
});

export class FavoriteAssetEndpoint extends OpenAPIRoute {
  schema = {
    summary: "收藏资产",
    description: "收藏指定资产，如果已经收藏则返回当前收藏数",
    tags: ["Social"],
    security: [
      {
        BearerAuth: [],
      },
    ],
    request: {
      params: FavoriteAssetParamsSchema,
    },
    responses: {
      "200": {
        description: "成功收藏或已经收藏",
        content: {
          "application/json": {
            schema: SocialActionResponseSchema,
          },
        },
      },
      "401": {
        description: "未认证或认证失效",
        content: {
          "application/json": {
            schema: ErrorResponseSchema,
          },
        },
      },
      "404": {
        description: "资产不存在",
        content: {
          "application/json": {
            schema: ErrorResponseSchema,
          },
        },
      },
      "500": {
        description: "服务器内部错误",
        content: {
          "application/json": {
            schema: ErrorResponseSchema,
          },
        },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    try {
      const container = c.get("container");
      const socialService = container.resolve<SocialService>(ISocialService);

      const firebaseToken = getFirebaseToken(c);
      if (!firebaseToken) {
        return c.json({ error: "Authentication required" }, 401);
      }
      const userId = firebaseToken.uid;

      const { params } = await this.getValidatedData<typeof this.schema>();
      const { assetId } = params;

      console.log(
        `[FavoriteAsset] User ${userId} attempting to favorite asset ${assetId}`
      );

      // Favorite the asset
      const result = await socialService.favoriteAsset(assetId, userId);

      const response = {
        success: result.success,
        favoriteCount: result.favoriteCount,
        message: result.success ? "Asset favorited successfully" : "Asset already favorited",
      };

      console.log(
        `[FavoriteAsset] ${result.success ? "Successfully favorited" : "Already favorited"} asset ${assetId} by user ${userId}`
      );

      return c.json(response, 200);
    } catch (error) {
      console.error("[FavoriteAsset] Error:", error);

      if (error instanceof Error && error.message.includes("not found")) {
        return c.json(
          {
            error: "Asset not found",
            code: "NOT_FOUND",
            details: "The requested asset does not exist",
          },
          404
        );
      }

      return c.json(
        {
          error: "Failed to favorite asset",
          code: "INTERNAL_ERROR",
          details: error instanceof Error ? error.message : "Unknown error",
        },
        500
      );
    }
  }
}

export class UnfavoriteAssetEndpoint extends OpenAPIRoute {
  schema = {
    summary: "取消收藏资产",
    description: "取消收藏指定资产，如果未收藏则返回当前收藏数",
    tags: ["Social"],
    security: [
      {
        BearerAuth: [],
      },
    ],
    request: {
      params: FavoriteAssetParamsSchema,
    },
    responses: {
      "200": {
        description: "成功取消收藏或未收藏",
        content: {
          "application/json": {
            schema: SocialActionResponseSchema,
          },
        },
      },
      "401": {
        description: "未认证或认证失效",
        content: {
          "application/json": {
            schema: ErrorResponseSchema,
          },
        },
      },
      "404": {
        description: "资产不存在",
        content: {
          "application/json": {
            schema: ErrorResponseSchema,
          },
        },
      },
      "500": {
        description: "服务器内部错误",
        content: {
          "application/json": {
            schema: ErrorResponseSchema,
          },
        },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    try {
      const container = c.get("container");
      const socialService = container.resolve<SocialService>(ISocialService);

      const firebaseToken = getFirebaseToken(c);
      if (!firebaseToken) {
        return c.json({ error: "Authentication required" }, 401);
      }
      const userId = firebaseToken.uid;

      const { params } = await this.getValidatedData<typeof this.schema>();
      const { assetId } = params;

      console.log(
        `[UnfavoriteAsset] User ${userId} attempting to unfavorite asset ${assetId}`
      );

      // Unfavorite the asset
      const result = await socialService.unfavoriteAsset(assetId, userId);

      const response = {
        success: result.success,
        favoriteCount: result.favoriteCount,
        message: result.success ? "Asset unfavorited successfully" : "Asset not favorited",
      };

      console.log(
        `[UnfavoriteAsset] ${result.success ? "Successfully unfavorited" : "Not favorited"} asset ${assetId} by user ${userId}`
      );

      return c.json(response, 200);
    } catch (error) {
      console.error("[UnfavoriteAsset] Error:", error);

      if (error instanceof Error && error.message.includes("not found")) {
        return c.json(
          {
            error: "Asset not found",
            code: "NOT_FOUND",
            details: "The requested asset does not exist",
          },
          404
        );
      }

      return c.json(
        {
          error: "Failed to unfavorite asset",
          code: "INTERNAL_ERROR",
          details: error instanceof Error ? error.message : "Unknown error",
        },
        500
      );
    }
  }
}
