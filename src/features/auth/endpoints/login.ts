import { OpenAPIRoute } from "chanfana";
import { type Context } from "hono";
import { z } from "zod";
import { getFirebaseToken } from "@hono/firebase-auth";
import type { DependencyContainer } from "tsyringe";
import type { HonoEnv } from "../../../types";
import { IUserService, type UserService } from "../../user/user.interface";

/**
 * User login endpoint for registration event handling
 */
export class LoginEndpoint extends OpenAPIRoute {
  schema = {
    summary: "User login",
    description: "Handle user login and detect new user registration events",
    tags: ["Authentication"],
    security: [
      {
        BearerAuth: [],
      },
    ],
    responses: {
      204: {
        description: "Login successful - No content returned",
      },
      401: {
        description: "Unauthorized - Invalid Firebase token",
        content: {
          "application/json": {
            schema: z.object({
              error: z.string(),
            }),
          },
        },
      },
      404: {
        description: "User document not found",
        content: {
          "application/json": {
            schema: z.object({
              error: z.string(),
            }),
          },
        },
      },
      500: {
        description: "Internal server error",
        content: {
          "application/json": {
            schema: z.object({
              error: z.string(),
            }),
          },
        },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    try {
      // 1. Verify Firebase token
      const firebaseToken = getFirebaseToken(c);
      if (!firebaseToken) {
        return c.json({ error: "Unauthorized" }, 401);
      }

      const uid = firebaseToken.uid;
      const container = c.get("container");
      const userService = container.resolve<UserService>(IUserService);

      // 2. Get user document from Firestore
      const userDoc = await userService.getUserById(uid);

      if (!userDoc) {
        // Edge case: User document doesn't exist
        // This shouldn't happen in normal flow
        console.warn(`User document not found for authenticated user: ${uid}`);
        return c.json({ error: "User document not found" }, 404);
      }

      // 3. Check if user is new
      if (userDoc.is_new_user === true) {
        console.log(`Processing new user registration: ${uid}`);

        // 4. Immediately mark as processed (prevent duplicate processing)
        await userService.updateUser(uid, {
          is_new_user: false,
          updatedAt: new Date(),
        });

        // 5. Process new user registration asynchronously
        c.executionCtx.waitUntil(
          this.processNewUserRegistration(uid, userDoc, container)
        );
      }

      // 6. Update last login time
      await userService.updateUser(uid, {
        lastLoginAt: new Date(),
      });

      // 7. Return minimal response
      return c.body(null, 204);
    } catch (error: any) {
      console.error("Login processing error:", error);
      return c.json({ error: "Internal server error" }, 500);
    }
  }

  /**
   * Process new user registration asynchronously
   */
  private async processNewUserRegistration(
    uid: string,
    userData: any,
    container: DependencyContainer
  ) {
    try {
      const userService = container.resolve<UserService>(IUserService);

      // 1. Grant signup credits
      const signupCredits = 30;
      const currentCredits = userData.credits || 0;

      await userService.updateUser(uid, {
        credits: currentCredits + signupCredits,
        updatedAt: new Date(),
      });

      console.log(`Granted ${signupCredits} signup credits to user: ${uid}`);

      // 2. Send welcome email (placeholder - implementation left empty as requested)
      // await emailService.sendWelcomeEmail(userData.email, userData.displayName);

      // 3. Log registration event
      console.log(`User registration completed: ${uid}`);

      // 4. Additional logic can be added here:
      // - Analytics tracking
      // - Third-party integrations
      // - Admin notifications
    } catch (error) {
      console.error(`User ${uid} registration processing failed:`, error);
      // Consider implementing retry logic or dead letter queue
    }
  }
}
