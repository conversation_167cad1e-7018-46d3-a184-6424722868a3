/**
 * Utility functions for working with MIME types and content categorization
 */

export type ContentCategory = "image" | "video" | "audio" | "document" | "archive" | "other";

/**
 * Categorizes a MIME type into a general content category
 * @param mimeType - The MIME type to categorize
 * @returns The content category
 */
export function categorizeContentType(mimeType: string): ContentCategory {
  const type = mimeType.toLowerCase();
  
  if (type.startsWith("image/")) {
    return "image";
  }
  
  if (type.startsWith("video/")) {
    return "video";
  }
  
  if (type.startsWith("audio/")) {
    return "audio";
  }
  
  if (
    type.startsWith("text/") ||
    type === "application/pdf" ||
    type === "application/msword" ||
    type.includes("officedocument") ||
    type === "application/json" ||
    type === "application/xml"
  ) {
    return "document";
  }
  
  if (
    type === "application/zip" ||
    type.includes("compressed") ||
    type.includes("tar") ||
    type === "application/gzip"
  ) {
    return "archive";
  }
  
  return "other";
}

/**
 * Checks if a MIME type is an image
 * @param mimeType - The MIME type to check
 * @returns True if the MIME type is an image
 */
export function isImageType(mimeType: string): boolean {
  return mimeType.toLowerCase().startsWith("image/");
}

/**
 * Checks if a MIME type is a video
 * @param mimeType - The MIME type to check
 * @returns True if the MIME type is a video
 */
export function isVideoType(mimeType: string): boolean {
  return mimeType.toLowerCase().startsWith("video/");
}

/**
 * Checks if a MIME type is an audio file
 * @param mimeType - The MIME type to check
 * @returns True if the MIME type is an audio file
 */
export function isAudioType(mimeType: string): boolean {
  return mimeType.toLowerCase().startsWith("audio/");
}

/**
 * Checks if a MIME type is a document
 * @param mimeType - The MIME type to check
 * @returns True if the MIME type is a document
 */
export function isDocumentType(mimeType: string): boolean {
  return categorizeContentType(mimeType) === "document";
}

/**
 * Gets a human-readable description for a MIME type
 * @param mimeType - The MIME type
 * @returns A human-readable description
 */
export function getMimeTypeDescription(mimeType: string): string {
  const descriptions: Record<string, string> = {
    // Images
    "image/jpeg": "JPEG Image",
    "image/jpg": "JPEG Image", 
    "image/png": "PNG Image",
    "image/webp": "WebP Image",
    "image/gif": "GIF Image",
    "image/svg+xml": "SVG Vector Image",
    "image/bmp": "Bitmap Image",
    "image/tiff": "TIFF Image",
    
    // Videos
    "video/mp4": "MP4 Video",
    "video/webm": "WebM Video",
    "video/avi": "AVI Video",
    "video/mov": "QuickTime Video",
    "video/wmv": "Windows Media Video",
    "video/flv": "Flash Video",
    "video/mkv": "Matroska Video",
    
    // Audio
    "audio/mp3": "MP3 Audio",
    "audio/wav": "WAV Audio",
    "audio/ogg": "OGG Audio",
    "audio/aac": "AAC Audio",
    "audio/flac": "FLAC Audio",
    "audio/m4a": "M4A Audio",
    
    // Documents
    "application/pdf": "PDF Document",
    "application/msword": "Microsoft Word Document",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document": "Microsoft Word Document",
    "application/vnd.ms-excel": "Microsoft Excel Spreadsheet",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "Microsoft Excel Spreadsheet",
    "application/vnd.ms-powerpoint": "Microsoft PowerPoint Presentation",
    "application/vnd.openxmlformats-officedocument.presentationml.presentation": "Microsoft PowerPoint Presentation",
    "text/plain": "Plain Text",
    "text/csv": "CSV File",
    "text/html": "HTML Document",
    "text/css": "CSS Stylesheet",
    "text/javascript": "JavaScript File",
    "application/json": "JSON File",
    "application/xml": "XML Document",
    
    // Archives
    "application/zip": "ZIP Archive",
    "application/x-rar-compressed": "RAR Archive",
    "application/x-7z-compressed": "7-Zip Archive",
    "application/x-tar": "TAR Archive",
    "application/gzip": "GZIP Archive",
  };
  
  return descriptions[mimeType.toLowerCase()] || mimeType;
}

/**
 * Common MIME types grouped by category
 */
export const COMMON_MIME_TYPES = {
  image: [
    "image/jpeg",
    "image/jpg",
    "image/png", 
    "image/webp",
    "image/gif",
    "image/svg+xml"
  ],
  video: [
    "video/mp4",
    "video/webm",
    "video/avi",
    "video/mov"
  ],
  audio: [
    "audio/mp3",
    "audio/wav",
    "audio/ogg",
    "audio/aac"
  ],
  document: [
    "application/pdf",
    "text/plain",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
  ]
} as const;
