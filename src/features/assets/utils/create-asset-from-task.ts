import { container } from "tsyringe";
import type { Task as ImageTask } from "../../image-generation/image-generation.schema";
import type { VideoTask } from "../../video-generation/video-generation.schema";
import type { Asset, CreateAssetData } from "../asset.schema";
import { IAssetService, type AssetService } from "../asset.interface";

/**
 * Creates an Asset record from a completed Task (image generation)
 * @param task - The completed image generation task
 * @returns Promise<Asset | null> - The created asset or null if creation failed
 */
export async function createAssetFromImageTask(
  task: ImageTask
): Promise<Asset | null> {
  try {
    // Validate that the task is completed successfully and has results
    if (
      task.status !== "succeeded" ||
      !task.resultImageUrls ||
      task.resultImageUrls.length === 0
    ) {
      console.warn(
        `[createAssetFromImageTask] Task ${task.taskId} is not in a valid state for asset creation`
      );
      return null;
    }

    const assetService = container.resolve<AssetService>(IAssetService);

    // Use the first result image URL
    const imageUrl = task.resultImageUrls[0];

    // Generate a filename based on the task
    const fileName = `ai-generated-image-${task.taskId}.jpg`;

    // Extract prompt from task
    const prompt = task.prompt;

    // Create asset data
    const assetData: CreateAssetData = {
      // 存储信息 (we'll need to determine these from the URL or set defaults)
      path: `ai-generated/images/${task.taskId}`,
      bucket: "default", // This should be determined from the actual storage configuration

      // Web 标准字段
      name: fileName,
      size: 0, // We don't have size info from the task, could be fetched later
      type: "image/jpeg", // Assuming JPEG for AI-generated images
      url: imageUrl,

      // 业务分类字段
      sourceType: "ai_generated",
      sourceTaskId: task.taskId,
      generationPrompt: prompt,

      // 社交统计字段
      likeCount: 0,
      favoriteCount: 0,

      // 元数据
      metadata: {
        generationProvider: "openai", // This could be determined from the task or service
        originalTaskData: {
          batchId: task.batchId,
          previousTaskId: task.previousTaskId,
          inputImageUrls: task.inputImageUrls,
          progress: task.progress,
        },
        taskCreatedAt: task.createdAt.toISOString(),
        taskUpdatedAt: task.updatedAt.toISOString(),
      },

      // 状态管理
      status: "active",
      isPublic: true, // AI-generated content is public by default for Explore feature
    };

    console.log(
      `[createAssetFromImageTask] Creating asset for task ${task.taskId}`
    );
    const asset = await assetService.createAsset(task.userId, assetData);

    console.log(
      `[createAssetFromImageTask] Successfully created asset ${asset.id} for task ${task.taskId}`
    );
    return asset;
  } catch (error) {
    console.error(
      `[createAssetFromImageTask] Failed to create asset for task ${task.taskId}:`,
      error
    );
    return null;
  }
}

/**
 * Creates an Asset record from a completed VideoTask (video generation)
 * @param videoTask - The completed video generation task
 * @returns Promise<Asset | null> - The created asset or null if creation failed
 */
export async function createAssetFromVideoTask(
  videoTask: VideoTask
): Promise<Asset | null> {
  try {
    // Validate that the task is completed successfully and has results
    if (videoTask.status !== "succeeded" || !videoTask.videoUrl) {
      console.warn(
        `[createAssetFromVideoTask] VideoTask ${videoTask.taskId} is not in a valid state for asset creation`
      );
      return null;
    }

    const assetService = container.resolve<AssetService>(IAssetService);

    // Generate a filename based on the task
    const fileName = `ai-generated-video-${videoTask.taskId}.mp4`;

    // Extract prompt from input data
    const prompt = videoTask.inputData?.prompt || "AI-generated video";

    // Create asset data
    const assetData: CreateAssetData = {
      // 存储信息
      thumbnailUrl: videoTask.coverImageUrl,
      path: `ai-generated/videos/${videoTask.taskId}`,
      bucket: "default", // This should be determined from the actual storage configuration

      // Web 标准字段
      name: fileName,
      size: 0, // We don't have size info from the task, could be fetched later
      type: "video/mp4", // Assuming MP4 for AI-generated videos
      url: videoTask.videoUrl,

      // 业务分类字段
      sourceType: "ai_generated",
      sourceTaskId: videoTask.taskId,
      generationPrompt: prompt,

      // 社交统计字段
      likeCount: 0,
      favoriteCount: 0,

      // 元数据
      metadata: {
        generationProvider: videoTask.provider || "unknown",
        originalTaskData: {
          batchId: videoTask.batchId,
          providerTaskId: videoTask.providerTaskId,
          inputData: videoTask.inputData,
          resultData: videoTask.resultData,
          inputImageUrl: videoTask.inputImageUrl,
          progress: videoTask.progress,
        },
        taskCreatedAt: videoTask.createdAt.toISOString(),
        taskUpdatedAt: videoTask.updatedAt.toISOString(),
      },

      // 状态管理
      status: "active",
      isPublic: true, // AI-generated content is public by default for Explore feature
    };

    console.log(
      `[createAssetFromVideoTask] Creating asset for video task ${videoTask.taskId}`
    );
    const asset = await assetService.createAsset(videoTask.userId, assetData);

    console.log(
      `[createAssetFromVideoTask] Successfully created asset ${asset.id} for video task ${videoTask.taskId}`
    );
    return asset;
  } catch (error) {
    console.error(
      `[createAssetFromVideoTask] Failed to create asset for video task ${videoTask.taskId}:`,
      error
    );
    return null;
  }
}

/**
 * Generic function to create an Asset from either Task or VideoTask
 * @param task - The completed task (either Task or VideoTask)
 * @returns Promise<Asset | null> - The created asset or null if creation failed
 */
export async function createAssetFromTask(
  task: ImageTask | VideoTask
): Promise<Asset | null> {
  // Check if it's a VideoTask by looking for video-specific fields
  if ("videoUrl" in task || "provider" in task) {
    return createAssetFromVideoTask(task as VideoTask);
  } else {
    return createAssetFromImageTask(task as ImageTask);
  }
}
