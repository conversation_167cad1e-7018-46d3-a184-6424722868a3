import { type Context } from "hono";
import { OpenAPIRoute } from "chanfana";
import { getFirebaseToken } from "@hono/firebase-auth";
import { z } from "zod";

import type { HonoEnv } from "../../../types";
import { IAssetService, type AssetService } from "../asset.interface";

const AssetStatsResponseSchema = z.object({
  totalAssets: z.number(),
  totalSize: z.number(),
  assetsByType: z.record(z.number()),
  assetsByStatus: z.record(z.number()),
});

export class GetAssetStatsEndpoint extends OpenAPIRoute {
  schema = {
    summary: "Get asset statistics",
    description:
      "Retrieves statistics about the authenticated user's assets including total count, size, and breakdowns by type and status.",
    tags: ["Assets"],
    security: [
      {
        BearerAuth: [],
      },
    ],
    responses: {
      200: {
        description: "Asset statistics retrieved successfully",
        content: {
          "application/json": {
            schema: AssetStatsResponseSchema,
          },
        },
      },
      401: {
        description: "Authentication required",
        content: {
          "application/json": {
            schema: z.object({
              error: z.string(),
            }),
          },
        },
      },
      500: {
        description: "Internal server error",
        content: {
          "application/json": {
            schema: z.object({
              error: z.string(),
            }),
          },
        },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    try {
      // Get Firebase token for user authentication
      const firebaseToken = getFirebaseToken(c);
      if (!firebaseToken) {
        return c.json({ error: "Authentication required" }, 401);
      }

      const userId = firebaseToken.uid;

      const container = c.get("container");
      const assetService = container.resolve<AssetService>(IAssetService);

      // Get asset statistics
      const stats = await assetService.getAssetStats(userId);

      return c.json(stats, 200);
    } catch (error: any) {
      console.error("Error getting asset statistics:", error);

      return c.json({ error: "Failed to retrieve asset statistics" }, 500);
    }
  }
}
