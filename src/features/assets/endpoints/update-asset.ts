import { type Context } from "hono";
import { OpenAPIRoute } from "chanfana";
import { getFirebaseToken } from "@hono/firebase-auth";
import { z } from "zod";

import {
  AssetIdParamSchema,
  UpdateAssetSchema,
  AssetResponseSchema,
} from "../asset.schema";
import type { HonoEnv } from "../../../types";
import { IAssetService, type AssetService } from "../asset.interface";

export class UpdateAssetEndpoint extends OpenAPIRoute {
  schema = {
    summary: "Update asset",
    description:
      "Updates an asset's metadata. Users can only update their own assets.",
    tags: ["Assets"],
    security: [
      {
        BearerAuth: [],
      },
    ],
    request: {
      params: AssetIdParamSchema,
      body: {
        content: {
          "application/json": {
            schema: UpdateAssetSchema,
            examples: {
              updateTags: {
                summary: "Update asset tags and description",
                description: "Example request for updating asset metadata",
                value: {
                  tags: ["vacation", "beach", "summer", "2024"],
                  description: "Updated description for beach vacation photo",
                  metadata: {
                    camera: "iPhone 14 Pro",
                    location: "Maldives",
                    edited: true,
                  },
                },
              },
              makePublic: {
                summary: "Make asset public",
                description:
                  "Example request for making an asset publicly accessible",
                value: {
                  isPublic: true,
                  description: "This photo is now available for public viewing",
                },
              },
            },
          },
        },
      },
    },
    responses: {
      200: {
        description: "Asset updated successfully",
        content: {
          "application/json": {
            schema: AssetResponseSchema,
          },
        },
      },
      400: {
        description: "Invalid request data",
        content: {
          "application/json": {
            schema: z.object({
              error: z.string(),
              details: z.any().optional(),
            }),
          },
        },
      },
      401: {
        description: "Authentication required",
        content: {
          "application/json": {
            schema: z.object({
              error: z.string(),
            }),
          },
        },
      },
      404: {
        description: "Asset not found or access denied",
        content: {
          "application/json": {
            schema: z.object({
              error: z.string(),
            }),
          },
        },
      },
      500: {
        description: "Internal server error",
        content: {
          "application/json": {
            schema: z.object({
              error: z.string(),
            }),
          },
        },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    try {
      // Get Firebase token for user authentication
      const firebaseToken = getFirebaseToken(c);
      if (!firebaseToken) {
        return c.json({ error: "Authentication required" }, 401);
      }

      const userId = firebaseToken.uid;
      const { params, body } = await this.getValidatedData<
        typeof this.schema
      >();

      const container = c.get("container");
      const assetService = container.resolve<AssetService>(IAssetService);

      // Update asset
      const updatedAsset = await assetService.updateAsset(
        params.id,
        userId,
        body
      );

      if (!updatedAsset) {
        return c.json({ error: "Asset not found or access denied" }, 404);
      }

      // Convert dates to ISO strings for response
      const response = {
        ...updatedAsset,
        createdAt: updatedAsset.createdAt.toISOString(),
        updatedAt: updatedAsset.updatedAt.toISOString(),
      };

      return c.json(response, 200);
    } catch (error: any) {
      console.error("Error updating asset:", error);

      if (error.message.includes("validation")) {
        return c.json(
          {
            error: "Invalid request data",
            details: error.message,
          },
          400
        );
      }

      return c.json({ error: "Failed to update asset" }, 500);
    }
  }
}
