import { type Context } from "hono";
import { OpenAPIRoute } from "chanfana";
import { getFirebaseToken } from "@hono/firebase-auth";
import { z } from "zod";

import { AssetListQuerySchema, AssetResponseSchema } from "../asset.schema";
import type { HonoEnv } from "../../../types";
import { IAssetService, type AssetService } from "../asset.interface";

const UserAssetsResponseSchema = z.object({
  assets: z.array(AssetResponseSchema),
  pagination: z.object({
    page: z.number(),
    limit: z.number(),
    total: z.number(),
    totalPages: z.number(),
    hasNext: z.boolean(),
    hasPrev: z.boolean(),
  }),
});

export class GetUserAssetsEndpoint extends OpenAPIRoute {
  schema = {
    summary: "Get user's assets",
    description:
      "Retrieves a paginated list of assets for the authenticated user with optional filtering",
    tags: ["Assets"],
    request: {
      query: AssetListQuerySchema,
    },
    security: [
      {
        BearerAuth: [],
      },
    ],
    responses: {
      200: {
        description: "Assets retrieved successfully",
        content: {
          "application/json": {
            schema: UserAssetsResponseSchema,
          },
        },
      },
      400: {
        description: "Invalid query parameters",
        content: {
          "application/json": {
            schema: z.object({
              error: z.string(),
              details: z.any().optional(),
            }),
          },
        },
      },
      401: {
        description: "Authentication required",
        content: {
          "application/json": {
            schema: z.object({
              error: z.string(),
            }),
          },
        },
      },
      500: {
        description: "Internal server error",
        content: {
          "application/json": {
            schema: z.object({
              error: z.string(),
            }),
          },
        },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    try {
      // Get Firebase token for user authentication
      const firebaseToken = getFirebaseToken(c);
      if (!firebaseToken) {
        return c.json({ error: "Authentication required" }, 401);
      }

      const userId = firebaseToken.uid;
      const { query } = await this.getValidatedData<typeof this.schema>();

      const container = c.get("container");
      const assetService = container.resolve<AssetService>(IAssetService);

      // Get user assets with pagination and filtering
      const result = await assetService.getUserAssets(userId, query);

      // Convert dates to ISO strings for response
      const response = {
        assets: result.assets.map((asset) => ({
          ...asset,
          createdAt: asset.createdAt.toISOString(),
          updatedAt: asset.updatedAt.toISOString(),
        })),
        pagination: result.pagination,
      };

      return c.json(response, 200);
    } catch (error: any) {
      console.error("Error getting user assets:", error);

      if (error.message.includes("validation")) {
        return c.json(
          {
            error: "Invalid query parameters",
            details: error.message,
          },
          400
        );
      }

      return c.json({ error: "Failed to retrieve assets" }, 500);
    }
  }
}
