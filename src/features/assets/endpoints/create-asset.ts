import { type Context } from "hono";
import { OpenAPIRoute } from "chanfana";
import { getFirebaseToken } from "@hono/firebase-auth";
import { z } from "zod";

import { CreateAssetSchema, AssetResponseSchema } from "../asset.schema";
import type { HonoEnv } from "../../../types";
import { IAssetService, type AssetService } from "../asset.interface";

export class CreateAssetEndpoint extends OpenAPIRoute {
  schema = {
    summary: "Create a new asset record",
    description: "Creates a new asset record for the authenticated user",
    tags: ["Assets"],
    security: [
      {
        BearerAuth: [],
      },
    ],
    request: {
      body: {
        content: {
          "application/json": {
            schema: CreateAssetSchema,
            examples: {
              imageAsset: {
                summary: "Create an image asset",
                description:
                  "Example request for creating an image asset record",
                value: {
                  storageUrl: "https://example.com/storage/image.jpg",
                  thumbnailUrl: "https://example.com/storage/thumb_image.jpg",
                  path: "uploads/images/image.jpg",
                  bucket: "my-bucket",
                  originalFilename: "vacation-photo.jpg",
                  fileSize: 1024000,
                  mimeType: "image/jpeg",
                  fileType: "image",
                  tags: ["vacation", "beach", "summer"],
                  description: "Beautiful beach vacation photo",
                  metadata: {
                    camera: "iPhone 14",
                    location: "Maldives",
                  },
                  isPublic: false,
                },
              },
            },
          },
        },
      },
    },
    responses: {
      201: {
        description: "Asset created successfully",
        content: {
          "application/json": {
            schema: AssetResponseSchema,
          },
        },
      },
      400: {
        description: "Invalid request data",
        content: {
          "application/json": {
            schema: z.object({
              error: z.string(),
              details: z.any().optional(),
            }),
          },
        },
      },
      401: {
        description: "Authentication required",
        content: {
          "application/json": {
            schema: z.object({
              error: z.string(),
            }),
          },
        },
      },
      500: {
        description: "Internal server error",
        content: {
          "application/json": {
            schema: z.object({
              error: z.string(),
            }),
          },
        },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    try {
      // Get Firebase token for user authentication
      const firebaseToken = getFirebaseToken(c);
      if (!firebaseToken) {
        return c.json({ error: "Authentication required" }, 401);
      }

      const userId = firebaseToken.uid;
      const { body } = await this.getValidatedData<typeof this.schema>();

      const container = c.get("container");
      const assetService = container.resolve<AssetService>(IAssetService);

      // Create asset record
      const asset = await assetService.createAsset(userId, body);

      // Convert dates to ISO strings for response
      const response = {
        ...asset,
        createdAt: asset.createdAt.toISOString(),
        updatedAt: asset.updatedAt.toISOString(),
      };

      return c.json(response, 201);
    } catch (error: any) {
      console.error("Error creating asset:", error);

      if (error.message.includes("validation")) {
        return c.json(
          {
            error: "Invalid request data",
            details: error.message,
          },
          400
        );
      }

      return c.json({ error: "Failed to create asset" }, 500);
    }
  }
}
