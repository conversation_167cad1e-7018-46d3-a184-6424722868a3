import { type Context } from "hono";
import { OpenAPIRoute } from "chanfana";
import { getFirebaseToken } from "@hono/firebase-auth";
import { z } from "zod";

import { AssetIdParamSchema, AssetResponseSchema } from "../asset.schema";
import type { HonoEnv } from "../../../types";
import { IAssetService, type AssetService } from "../asset.interface";

export class GetAssetByIdEndpoint extends OpenAPIRoute {
  schema = {
    summary: "Get asset by ID",
    description:
      "Retrieves a specific asset by its ID. Users can only access their own assets or public assets.",
    tags: ["Assets"],
    security: [
      {
        BearerAuth: [],
      },
    ],
    request: {
      params: AssetIdParamSchema,
    },
    responses: {
      200: {
        description: "Asset retrieved successfully",
        content: {
          "application/json": {
            schema: AssetResponseSchema,
          },
        },
      },
      401: {
        description: "Authentication required",
        content: {
          "application/json": {
            schema: z.object({
              error: z.string(),
            }),
          },
        },
      },
      404: {
        description: "Asset not found or access denied",
        content: {
          "application/json": {
            schema: z.object({
              error: z.string(),
            }),
          },
        },
      },
      500: {
        description: "Internal server error",
        content: {
          "application/json": {
            schema: z.object({
              error: z.string(),
            }),
          },
        },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    try {
      // Get Firebase token for user authentication
      const firebaseToken = getFirebaseToken(c);
      if (!firebaseToken) {
        return c.json({ error: "Authentication required" }, 401);
      }

      const userId = firebaseToken.uid;
      const { params } = await this.getValidatedData<typeof this.schema>();

      const container = c.get("container");
      const assetService = container.resolve<AssetService>(IAssetService);

      // Get asset by ID with access control
      const asset = await assetService.getAssetById(params.id, userId);

      if (!asset) {
        return c.json({ error: "Asset not found or access denied" }, 404);
      }

      // Convert dates to ISO strings for response
      const response = {
        ...asset,
        createdAt: asset.createdAt.toISOString(),
        updatedAt: asset.updatedAt.toISOString(),
      };

      return c.json(response, 200);
    } catch (error: any) {
      console.error("Error getting asset by ID:", error);

      return c.json({ error: "Failed to retrieve asset" }, 500);
    }
  }
}
