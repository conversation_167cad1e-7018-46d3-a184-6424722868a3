import { type Context } from "hono";
import { OpenAPIRoute } from "chanfana";
import { getFirebaseToken } from "@hono/firebase-auth";
import { z } from "zod";

import { AssetIdParamSchema } from "../asset.schema";
import type { HonoEnv } from "../../../types";
import { IAssetService, type AssetService } from "../asset.interface";

export class DeleteAssetEndpoint extends OpenAPIRoute {
  schema = {
    summary: "Delete asset",
    description:
      "Soft deletes an asset by setting its status to 'deleted'. Users can only delete their own assets.",
    tags: ["Assets"],
    security: [
      {
        BearerAuth: [],
      },
    ],
    request: {
      params: AssetIdParamSchema,
    },
    responses: {
      200: {
        description: "Asset deleted successfully",
        content: {
          "application/json": {
            schema: z.object({
              message: z.string(),
              id: z.string(),
            }),
          },
        },
      },
      401: {
        description: "Authentication required",
        content: {
          "application/json": {
            schema: z.object({
              error: z.string(),
            }),
          },
        },
      },
      404: {
        description: "Asset not found or access denied",
        content: {
          "application/json": {
            schema: z.object({
              error: z.string(),
            }),
          },
        },
      },
      500: {
        description: "Internal server error",
        content: {
          "application/json": {
            schema: z.object({
              error: z.string(),
            }),
          },
        },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    try {
      // Get Firebase token for user authentication
      const firebaseToken = getFirebaseToken(c);
      if (!firebaseToken) {
        return c.json({ error: "Authentication required" }, 401);
      }

      const userId = firebaseToken.uid;
      const { params } = await this.getValidatedData<typeof this.schema>();

      const container = c.get("container");
      const assetService = container.resolve<AssetService>(IAssetService);

      // Delete asset (soft delete)
      const deleted = await assetService.deleteAsset(params.id, userId);

      if (!deleted) {
        return c.json({ error: "Asset not found or access denied" }, 404);
      }

      return c.json(
        {
          message: "Asset deleted successfully",
          id: params.id,
        },
        200
      );
    } catch (error: any) {
      console.error("Error deleting asset:", error);

      return c.json({ error: "Failed to delete asset" }, 500);
    }
  }
}
