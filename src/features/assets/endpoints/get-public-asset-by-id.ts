import { type Context } from "hono";
import { OpenAPIRoute } from "chanfana";
import { z } from "zod";

import { AssetIdParamSchema, AssetResponseSchema } from "../asset.schema";
import type { HonoEnv } from "../../../types";
import { IAssetService, type AssetService } from "../asset.interface";

export class GetPublicAssetByIdEndpoint extends OpenAPIRoute {
  schema = {
    summary: "Get public asset by ID",
    description:
      "Retrieves a specific public asset by its ID. Only returns assets that are marked as public and active. No authentication required.",
    tags: ["Assets"],
    request: {
      params: AssetIdParamSchema,
    },
    responses: {
      200: {
        description: "Public asset retrieved successfully",
        content: {
          "application/json": {
            schema: AssetResponseSchema,
          },
        },
      },
      404: {
        description: "Asset not found or not public",
        content: {
          "application/json": {
            schema: z.object({
              error: z.string(),
            }),
          },
        },
      },
      500: {
        description: "Internal server error",
        content: {
          "application/json": {
            schema: z.object({
              error: z.string(),
            }),
          },
        },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    try {
      const { params } = await this.getValidatedData<typeof this.schema>();

      const container = c.get("container");
      const assetService = container.resolve<AssetService>(IAssetService);

      // Get public asset by ID (no authentication required)
      const asset = await assetService.getPublicAssetById(params.id);

      if (!asset) {
        return c.json({ error: "Asset not found or not public" }, 404);
      }

      // Convert dates to ISO strings for response
      const response = {
        ...asset,
        createdAt: asset.createdAt.toISOString(),
        updatedAt: asset.updatedAt.toISOString(),
      };

      return c.json(response, 200);
    } catch (error: any) {
      console.error("Error getting public asset by ID:", error);

      return c.json({ error: "Failed to retrieve public asset" }, 500);
    }
  }
}
