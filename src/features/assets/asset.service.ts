import { inject, singleton } from "tsyringe";
import { v4 as uuidv4 } from "uuid";
import type { CollectionReference } from "firebase-rest-firestore";

import { AssetService } from "./asset.interface";
import type {
  Asset,
  CreateAssetData,
  UpdateAssetData,
  AssetListQuery,
} from "./asset.schema";
import type { AssetListResponse } from "./asset.interface";
import {
  IDbService,
  type DbService,
} from "../../infrastructure/db/db-service.interface";

const COLLECTION_NAME = "assets";

@singleton()
export class FirestoreAssetService implements AssetService {
  private assetsCollection: CollectionReference;

  constructor(@inject(IDbService) dbService: DbService) {
    this.assetsCollection = dbService
      .getFirestoreInstance()
      .collection(COLLECTION_NAME);
  }

  async createAsset(
    userId: string,
    assetData: CreateAssetData
  ): Promise<Asset> {
    const id = uuidv4();
    const now = new Date();

    const asset: Asset = {
      id,
      userId,
      ...assetData,
      createdAt: now,
      updatedAt: now,
      status: assetData.status || "active",
      isPublic: assetData.isPublic || false,
    };

    // Use update instead of set to ensure consistent behavior with firebase-rest-firestore
    await this.assetsCollection.doc(id).update(asset);

    return asset;
  }

  async getAssetById(id: string, userId?: string): Promise<Asset | null> {
    try {
      const doc = await this.assetsCollection.doc(id).get();

      if (!doc.exists) {
        return null;
      }

      const asset = {
        id: doc.id,
        ...doc.data(),
      } as Asset;

      // Access control: only return asset if it's public or belongs to the requesting user
      if (userId && asset.userId !== userId && !asset.isPublic) {
        return null;
      }

      return asset;
    } catch (error: any) {
      console.error(`Error getting asset with ID ${id}:`, error);
      throw new Error(`Failed to retrieve asset: ${error.message}`);
    }
  }

  async getPublicAssetById(id: string): Promise<Asset | null> {
    try {
      const doc = await this.assetsCollection.doc(id).get();

      if (!doc.exists) {
        return null;
      }

      const asset = {
        id: doc.id,
        ...doc.data(),
      } as Asset;

      // Only return public and active assets
      if (!asset.isPublic || asset.status !== "active") {
        return null;
      }

      return asset;
    } catch (error: any) {
      console.error(`Error getting public asset with ID ${id}:`, error);
      throw new Error(`Failed to retrieve public asset: ${error.message}`);
    }
  }

  async getUserAssets(
    userId: string,
    query?: AssetListQuery
  ): Promise<AssetListResponse> {
    try {
      const limit = query?.limit || 20;
      const page = query?.page || 1;
      const offset = (page - 1) * limit;

      // Simple approach: get all user assets and filter client-side
      // This avoids Firestore's inequality filter limitations completely
      let baseQuery = this.assetsCollection
        .where("userId", "==", userId)
        .orderBy("createdAt", "desc");

      // Only apply equality filters to avoid Firestore limitations
      if (query?.type) {
        baseQuery = baseQuery.where("type", "==", query.type);
      }

      // If specific status is requested, use it; otherwise get all and filter client-side
      if (query?.status) {
        baseQuery = baseQuery.where("status", "==", query.status);
      }

      const snapshot = await baseQuery.get();
      let assets = snapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as Asset[];

      // Apply client-side filtering
      // Filter out deleted assets if no specific status was requested
      if (!query?.status) {
        assets = assets.filter((asset) => asset.status !== "deleted");
      }

      // Apply other client-side filters
      if (query?.tags) {
        const searchTags = query.tags
          .split(",")
          .map((tag) => tag.trim().toLowerCase());
        assets = assets.filter((asset) =>
          asset.tags?.some((tag) =>
            searchTags.some((searchTag) =>
              tag.toLowerCase().includes(searchTag)
            )
          )
        );
      }

      if (query?.search) {
        const searchTerm = query.search.toLowerCase();
        assets = assets.filter(
          (asset) =>
            asset.name.toLowerCase().includes(searchTerm) ||
            asset.description?.toLowerCase().includes(searchTerm)
        );
      }

      // Calculate pagination after filtering
      const total = assets.length;
      const totalPages = Math.ceil(total / limit);

      // Apply pagination
      const startIndex = offset;
      const endIndex = startIndex + limit;
      const paginatedAssets = assets.slice(startIndex, endIndex);

      return {
        assets: paginatedAssets,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      };
    } catch (error: any) {
      console.error(`Error getting assets for user ${userId}:`, error);
      throw new Error(`Failed to retrieve assets: ${error.message}`);
    }
  }

  async updateAsset(
    id: string,
    userId: string,
    updateData: UpdateAssetData
  ): Promise<Asset | null> {
    try {
      const assetRef = this.assetsCollection.doc(id);
      const doc = await assetRef.get();

      if (!doc.exists) {
        return null;
      }

      const asset = doc.data() as Asset;

      // Access control: only allow owner to update
      if (asset.userId !== userId) {
        return null;
      }

      const updateDataWithTimestamp = {
        ...updateData,
        updatedAt: new Date(),
      };

      await assetRef.update(updateDataWithTimestamp);

      return {
        ...asset,
        ...updateDataWithTimestamp,
      };
    } catch (error: any) {
      console.error(`Error updating asset ${id}:`, error);
      throw new Error(`Failed to update asset: ${error.message}`);
    }
  }

  async deleteAsset(id: string, userId: string): Promise<boolean> {
    try {
      const assetRef = this.assetsCollection.doc(id);
      const doc = await assetRef.get();

      if (!doc.exists) {
        return false;
      }

      const asset = doc.data() as Asset;

      // Access control: only allow owner to delete
      if (asset.userId !== userId) {
        return false;
      }

      // Soft delete by setting status to 'deleted'
      await assetRef.update({
        status: "deleted",
        updatedAt: new Date(),
      });

      return true;
    } catch (error: any) {
      console.error(`Error deleting asset ${id}:`, error);
      throw new Error(`Failed to delete asset: ${error.message}`);
    }
  }

  async searchAssets(query: AssetListQuery): Promise<AssetListResponse> {
    // This is an admin function - implement with appropriate access controls
    const limit = query.limit || 20;
    const page = query.page || 1;
    const offset = (page - 1) * limit;

    // Simple approach: get all assets and filter client-side to avoid Firestore limitations
    let dataQuery = this.assetsCollection.orderBy("createdAt", "desc");

    // Only apply equality filters
    if (query.status) {
      dataQuery = dataQuery.where("status", "==", query.status);
    }

    const snapshot = await dataQuery.get();
    let assets = snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as Asset[];

    // Client-side filtering for deleted assets if no specific status requested
    if (!query.status) {
      assets = assets.filter((asset) => asset.status !== "deleted");
    }

    // Calculate pagination after filtering
    const total = assets.length;
    const totalPages = Math.ceil(total / limit);

    // Apply pagination
    const startIndex = offset;
    const endIndex = startIndex + limit;
    const paginatedAssets = assets.slice(startIndex, endIndex);

    return {
      assets: paginatedAssets,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    };
  }

  async getPublicAssets(query?: AssetListQuery): Promise<AssetListResponse> {
    const limit = query?.limit || 20;
    const page = query?.page || 1;
    const offset = (page - 1) * limit;

    let dataQuery = this.assetsCollection
      .where("isPublic", "==", true)
      .where("status", "==", "active")
      .orderBy("createdAt", "desc");

    if (offset > 0) {
      dataQuery = dataQuery.offset(offset);
    }
    dataQuery = dataQuery.limit(limit);

    const snapshot = await dataQuery.get();
    const assets = snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as Asset[];

    return {
      assets,
      pagination: {
        page,
        limit,
        total: assets.length,
        totalPages: Math.ceil(assets.length / limit),
        hasNext: assets.length === limit,
        hasPrev: page > 1,
      },
    };
  }

  async getAssetsByTags(
    userId: string,
    tags: string[],
    query?: AssetListQuery
  ): Promise<AssetListResponse> {
    // Get user assets first, then filter by tags
    const userAssets = await this.getUserAssets(userId, query);

    const filteredAssets = userAssets.assets.filter((asset) =>
      asset.tags?.some((assetTag) =>
        tags.some((searchTag) =>
          assetTag.toLowerCase().includes(searchTag.toLowerCase())
        )
      )
    );

    return {
      assets: filteredAssets,
      pagination: userAssets.pagination,
    };
  }

  async getAssetStats(userId: string): Promise<{
    totalAssets: number;
    totalSize: number;
    assetsByType: Record<string, number>;
    assetsByStatus: Record<string, number>;
  }> {
    const snapshot = await this.assetsCollection
      .where("userId", "==", userId)
      .get();

    let assets = snapshot.docs.map((doc) => doc.data()) as Asset[];

    // Client-side filtering to exclude deleted assets
    assets = assets.filter((asset) => asset.status !== "deleted");

    const stats = {
      totalAssets: assets.length,
      totalSize: assets.reduce((sum, asset) => sum + asset.size, 0),
      assetsByType: {} as Record<string, number>,
      assetsByStatus: {} as Record<string, number>,
    };

    assets.forEach((asset) => {
      // Count by file type
      stats.assetsByType[asset.type] =
        (stats.assetsByType[asset.type] || 0) + 1;

      // Count by status
      const status = asset.status || "active";
      stats.assetsByStatus[status] = (stats.assetsByStatus[status] || 0) + 1;
    });

    return stats;
  }
}
