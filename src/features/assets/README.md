# Assets Management System

这个模块提供了完整的用户资产管理功能，包括文件上传、元数据管理、搜索和统计等功能。

## 🎯 功能特性

### 核心功能

- **资产创建** - 创建新的资产记录
- **文件上传集成** - 上传文件的同时自动创建资产记录
- **元数据管理** - 支持标签、描述和自定义元数据
- **访问控制** - 支持私有和公开资产
- **软删除** - 安全的删除机制，支持恢复

### 查询功能

- **分页查询** - 支持分页获取用户资产
- **类型筛选** - 按文件类型筛选（图片、音频、视频、文档等）
- **标签搜索** - 按标签筛选资产
- **全文搜索** - 在文件名和描述中搜索
- **状态筛选** - 按资产状态筛选

### 统计功能

- **资产统计** - 总数量、总大小
- **类型分布** - 各文件类型的数量统计
- **状态分布** - 各状态的资产数量

## 📊 数据模型

### Asset 接口

```typescript
interface Asset {
  // 基础标识
  id: string; // 主键 UUID
  userId: string; // 用户ID

  // 存储信息
  storageUrl: string; // 云存储访问URL
  thumbnailUrl?: string; // 缩略图URL
  path: string; // 存储路径
  bucket: string; // 存储桶名称

  // 文件信息
  originalFilename: string; // 原始文件名
  fileSize: number; // 文件大小(bytes)
  mimeType: string; // MIME类型
  fileType: FileType; // 文件类型枚举

  // 元数据
  tags?: string[]; // 标签数组
  description?: string; // 资产描述
  metadata?: Record<string, any>; // 扩展元数据

  // 时间戳
  createdAt: Date; // 创建时间

  // 状态管理
  status?: AssetStatus; // 资产状态
  isPublic?: boolean; // 是否公开访问
}
```

### 枚举类型

```typescript
type FileType = "image" | "audio" | "video" | "document" | "other";
type AssetStatus = "active" | "archived" | "deleted";
```

## 🚀 API 端点

### 文件上传与资产创建

```http
POST /api/v1/file-upload/with-asset
```

上传文件并自动创建资产记录，需要认证。

### 资产管理

```http
POST   /api/v1/assets           # 创建资产记录 (需认证)
GET    /api/v1/assets           # 获取用户资产列表 (需认证)
GET    /api/v1/assets/{id}      # 获取特定资产 (需认证)
GET    /api/v1/assets/{id}/public # 获取公开资产详情 (无需认证)
PUT    /api/v1/assets/{id}      # 更新资产元数据 (需认证)
DELETE /api/v1/assets/{id}      # 删除资产(软删除) (需认证)
GET    /api/v1/assets/stats     # 获取资产统计 (需认证)
```

### 查询参数

- `page` - 页码 (默认: 1)
- `limit` - 每页数量 (默认: 20, 最大: 100)
- `fileType` - 文件类型筛选
- `status` - 状态筛选
- `tags` - 标签筛选 (逗号分隔)
- `search` - 搜索关键词

## 💡 使用示例

### 1. 上传文件并创建资产

```javascript
const response = await fetch("/api/v1/file-upload/with-asset", {
  method: "POST",
  headers: {
    Authorization: "Bearer YOUR_JWT_TOKEN",
    "Content-Type": "application/json",
  },
  body: JSON.stringify({
    mimeType: "image/jpeg",
    base64Data: "iVBORw0KGgoAAAANSUhEUgAAAAEAAAAB...",
    fileName: "vacation-photo",
    prefix: "uploads/images",
    tags: ["vacation", "beach", "summer"],
    description: "Beautiful beach vacation photo",
    metadata: {
      camera: "iPhone 14",
      location: "Maldives",
    },
    isPublic: false,
  }),
});
```

### 2. 获取用户资产列表

```javascript
const response = await fetch("/api/v1/assets?page=1&limit=10&fileType=image", {
  headers: {
    Authorization: "Bearer YOUR_JWT_TOKEN",
  },
});

const data = await response.json();
console.log(`总共 ${data.pagination.total} 个资产`);
console.log(`当前页: ${data.pagination.page}/${data.pagination.totalPages}`);
```

### 3. 搜索资产

```javascript
// 按标签搜索
const tagSearch = await fetch("/api/v1/assets?tags=vacation,beach");

// 全文搜索
const textSearch = await fetch("/api/v1/assets?search=vacation");
```

### 4. 获取公开资产详情（无需认证）

```javascript
// 用于 Explore 功能的公开资产详情
const response = await fetch(`/api/v1/assets/${assetId}/public`);

if (response.ok) {
  const asset = await response.json();
  console.log("公开资产详情:", asset);
} else if (response.status === 404) {
  console.log("资产不存在或不是公开资产");
}
```

### 5. 更新资产元数据

```javascript
await fetch(`/api/v1/assets/${assetId}`, {
  method: "PUT",
  headers: {
    Authorization: "Bearer YOUR_JWT_TOKEN",
    "Content-Type": "application/json",
  },
  body: JSON.stringify({
    tags: ["vacation", "beach", "summer", "2024"],
    description: "Updated description",
    isPublic: true,
  }),
});
```

## 🔧 技术实现

### 架构组件

- **AssetService** - 业务逻辑层，处理资产的 CRUD 操作
- **FirestoreAssetService** - Firestore 数据库实现
- **StorageService** - 文件存储服务集成
- **认证中间件** - Firebase 认证集成

### 数据库设计

- 使用 Firestore 作为主数据库
- 集合名称: `assets`
- 支持复合查询和分页
- 软删除机制，通过状态字段实现

### 存储集成

- 与现有的 R2StorageService 集成
- 支持文件上传和元数据提取
- 自动生成缩略图（如果支持）

## 🛡️ 安全特性

### 访问控制

- 用户只能访问自己的资产
- 支持公开资产的访问
- 基于 Firebase JWT 的认证

### 数据验证

- 使用 Zod 进行请求验证
- 文件类型和大小限制
- 输入数据清理和验证

## 📈 性能优化

### 查询优化

- 使用 Firestore 复合索引
- 分页查询减少数据传输
- 客户端筛选复杂查询

### 缓存策略

- 可扩展缓存层支持
- 统计数据缓存
- 元数据缓存

## 🧪 测试

使用提供的 `api.http` 文件进行 API 测试：

1. 替换 `{{authToken}}` 为有效的 Firebase JWT
2. 替换 `{{assetId}}` 为实际的资产 ID
3. 运行各个测试用例

## 🔄 扩展性

### 未来功能

- 批量操作支持
- 资产版本管理
- 高级搜索功能
- 资产分享和协作
- 自动标签生成（AI）

### 集成点

- 图像处理服务
- CDN 集成
- 备份和归档
- 审计日志
