import { z } from "zod";

// Asset status enumeration
export const AssetStatusSchema = z.enum(["active", "archived", "deleted"]);

export type AssetStatus = z.infer<typeof AssetStatusSchema>;

// Source type enumeration for AI-generated vs user-uploaded content
export const SourceTypeSchema = z.enum(["ai_generated", "user_upload"]);

export type SourceType = z.infer<typeof SourceTypeSchema>;

// Common MIME types enumeration for type field
export const MimeTypeSchema = z.enum([
  // Images
  "image/jpeg",
  "image/jpg",
  "image/png",
  "image/webp",
  "image/gif",
  "image/svg+xml",
  "image/bmp",
  "image/tiff",

  // Videos
  "video/mp4",
  "video/webm",
  "video/avi",
  "video/mov",
  "video/wmv",
  "video/flv",
  "video/mkv",

  // Audio
  "audio/mp3",
  "audio/wav",
  "audio/ogg",
  "audio/aac",
  "audio/flac",
  "audio/m4a",

  // Documents
  "application/pdf",
  "application/msword",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  "application/vnd.ms-excel",
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  "application/vnd.ms-powerpoint",
  "application/vnd.openxmlformats-officedocument.presentationml.presentation",
  "text/plain",
  "text/csv",
  "text/html",
  "text/css",
  "text/javascript",
  "application/json",
  "application/xml",

  // Archives
  "application/zip",
  "application/x-rar-compressed",
  "application/x-7z-compressed",
  "application/x-tar",
  "application/gzip",
]);

export type MimeType = z.infer<typeof MimeTypeSchema>;

// Core Asset interface
export interface Asset {
  // 基础标识
  id: string; // 主键, UUID
  userId: string; // 外键, 指向 User.id

  // 存储信息
  thumbnailUrl?: string; // 缩略图 URL
  path: string; // 存储路径
  bucket: string; // 存储桶名称

  // Web 标准字段 (遵循 File API)
  name: string; // 文件名
  size: number; // 文件大小 (bytes)
  type: string; // MIME 类型
  url: string; // 访问 URL

  // 业务分类字段
  sourceType: SourceType; // 来源类型：AI生成 或 用户上传
  sourceTaskId?: string; // 关联的任务ID（仅AI生成内容）
  generationPrompt?: string; // AI生成的prompt（仅AI生成内容）

  // 社交统计（基础计数）
  likeCount: number; // 点赞数
  favoriteCount: number; // 收藏数

  // 元数据
  tags?: string[]; // 标签数组，便于分类和搜索
  description?: string; // 资产描述
  metadata?: Record<string, any>; // 扩展元数据

  // 时间戳
  createdAt: Date; // 创建时间
  updatedAt: Date; // 更新时间

  // 状态管理
  status?: AssetStatus; // 资产状态
  isPublic?: boolean; // 是否公开访问
}

// Zod schema for Asset validation
export const AssetSchema = z.object({
  id: z.string().uuid(),
  userId: z.string().min(1),

  // 存储信息
  thumbnailUrl: z.string().url().optional(),
  path: z.string().min(1),
  bucket: z.string().min(1),

  // Web 标准字段 (遵循 File API)
  name: z.string().min(1),
  size: z.number().positive(),
  type: z.union([
    MimeTypeSchema, // 常用 MIME 类型枚举
    z
      .string()
      .regex(
        /^[a-zA-Z0-9][a-zA-Z0-9!#$&\-\^_]*\/[a-zA-Z0-9][a-zA-Z0-9!#$&\-\^_.]*$/,
        {
          message: "Must be a valid MIME type",
        }
      ),
  ]), // MIME type - 支持枚举中的常用类型或其他有效 MIME type
  url: z.string().url(),

  // 业务分类字段
  sourceType: SourceTypeSchema,
  sourceTaskId: z.string().optional(),
  generationPrompt: z.string().optional(),

  // 社交统计字段
  likeCount: z.number().min(0).default(0),
  favoriteCount: z.number().min(0).default(0),

  // 元数据字段
  tags: z.array(z.string()).optional(),
  description: z.string().optional(),
  metadata: z.record(z.any()).optional(),

  // 时间戳
  createdAt: z.date(),
  updatedAt: z.date(),

  // 状态管理
  status: AssetStatusSchema.optional().default("active"),
  isPublic: z.boolean().optional().default(false),
});

// Schema for creating an asset (without id and createdAt)
export const CreateAssetSchema = z.object({
  // 存储信息
  thumbnailUrl: z.string().url().optional(),
  path: z.string().min(1),
  bucket: z.string().min(1),

  // Web 标准字段 (遵循 File API)
  name: z.string().min(1),
  size: z.number().positive(),
  type: z.union([
    MimeTypeSchema, // 常用 MIME 类型枚举
    z
      .string()
      .regex(
        /^[a-zA-Z0-9][a-zA-Z0-9!#$&\-\^_]*\/[a-zA-Z0-9][a-zA-Z0-9!#$&\-\^_.]*$/,
        {
          message: "Must be a valid MIME type",
        }
      ),
  ]), // MIME type
  url: z.string().url(),

  // 业务分类字段
  sourceType: SourceTypeSchema,
  sourceTaskId: z.string().optional(),
  generationPrompt: z.string().optional(),

  // 社交统计字段
  likeCount: z.number().min(0).default(0),
  favoriteCount: z.number().min(0).default(0),

  // 元数据字段
  tags: z.array(z.string()).optional(),
  description: z.string().optional(),
  metadata: z.record(z.any()).optional(),

  // 状态管理
  status: AssetStatusSchema.optional().default("active"),
  isPublic: z.boolean().optional().default(false),
});

export type CreateAssetData = z.infer<typeof CreateAssetSchema>;

// Schema for updating an asset
export const UpdateAssetSchema = z.object({
  // Web 标准字段更新
  name: z.string().min(1).optional(),

  // 业务字段更新
  sourceType: SourceTypeSchema.optional(),
  sourceTaskId: z.string().optional(),
  generationPrompt: z.string().optional(),

  // 社交统计字段更新 (通常由系统自动更新)
  likeCount: z.number().min(0).optional(),
  favoriteCount: z.number().min(0).optional(),

  // 元数据字段更新
  tags: z.array(z.string()).optional(),
  description: z.string().optional(),
  metadata: z.record(z.any()).optional(),

  // 状态管理更新
  status: AssetStatusSchema.optional(),
  isPublic: z.boolean().optional(),
});

export type UpdateAssetData = z.infer<typeof UpdateAssetSchema>;

// Response schema for API endpoints
export const AssetResponseSchema = z.object({
  id: z.string().uuid(),
  userId: z.string(),

  // 存储信息
  thumbnailUrl: z.string().url().optional(),
  path: z.string(),
  bucket: z.string(),

  // Web 标准字段
  name: z.string(),
  size: z.number(),
  type: z.string(), // MIME type (在响应中保持为 string 以支持所有可能的值)
  url: z.string().url(),

  // 业务分类字段
  sourceType: SourceTypeSchema,
  sourceTaskId: z.string().optional(),
  generationPrompt: z.string().optional(),

  // 社交统计字段
  likeCount: z.number().min(0),
  favoriteCount: z.number().min(0),

  // 元数据字段
  tags: z.array(z.string()).optional(),
  description: z.string().optional(),
  metadata: z.record(z.any()).optional(),

  // 时间戳
  createdAt: z.string().datetime(), // ISO string for API response
  updatedAt: z.string().datetime(), // ISO string for API response

  // 状态管理
  status: AssetStatusSchema.optional(),
  isPublic: z.boolean().optional(),
});

// Query parameters for listing assets
export const AssetListQuerySchema = z.object({
  page: z.coerce.number().min(1).optional().default(1),
  limit: z.coerce.number().min(1).max(100).optional().default(20),
  type: z.string().optional(), // MIME type filter (Web standard) - supports wildcards like "image/*"
  sourceType: SourceTypeSchema.optional(), // Filter by source type
  status: AssetStatusSchema.optional(),
  tags: z.string().optional(), // Comma-separated tags
  search: z.string().optional(), // Search in filename or description
});

export type AssetListQuery = z.infer<typeof AssetListQuerySchema>;

// Path parameters
export const AssetIdParamSchema = z.object({
  id: z.string().uuid(),
});

export type AssetIdParam = z.infer<typeof AssetIdParamSchema>;
