import type { InjectionToken } from "tsyringe";
import type {
  Asset,
  CreateAssetData,
  UpdateAssetData,
  AssetListQuery,
} from "./asset.schema";

// Define the injection token for AssetService
export const IAssetService: InjectionToken<AssetService> =
  Symbol("IAssetService");

/**
 * Asset list response interface
 */
export interface AssetListResponse {
  assets: Asset[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

/**
 * Abstract class representing the contract for an asset service.
 * It defines methods for asset-related operations.
 */
export abstract class AssetService {
  /**
   * Create a new asset record
   * @param userId - The ID of the user who owns the asset
   * @param assetData - The asset data to create
   * @returns Promise<Asset> - The created asset
   */
  abstract createAsset(
    userId: string,
    assetData: CreateAssetData
  ): Promise<Asset>;

  /**
   * Get an asset by its ID
   * @param id - The asset ID
   * @param userId - The user ID (for access control)
   * @returns Promise<Asset | null> - The asset or null if not found
   */
  abstract getAssetById(id: string, userId?: string): Promise<Asset | null>;

  /**
   * Get a public asset by its ID (only returns public and active assets)
   * @param id - The asset ID
   * @returns Promise<Asset | null> - The public asset or null if not found/not public
   */
  abstract getPublicAssetById(id: string): Promise<Asset | null>;

  /**
   * Get all assets for a specific user with pagination and filtering
   * @param userId - The user ID
   * @param query - Query parameters for filtering and pagination
   * @returns Promise<AssetListResponse> - Paginated list of assets
   */
  abstract getUserAssets(
    userId: string,
    query?: AssetListQuery
  ): Promise<AssetListResponse>;

  /**
   * Update an asset
   * @param id - The asset ID
   * @param userId - The user ID (for access control)
   * @param updateData - The data to update
   * @returns Promise<Asset | null> - The updated asset or null if not found
   */
  abstract updateAsset(
    id: string,
    userId: string,
    updateData: UpdateAssetData
  ): Promise<Asset | null>;

  /**
   * Delete an asset (soft delete by setting status to 'deleted')
   * @param id - The asset ID
   * @param userId - The user ID (for access control)
   * @returns Promise<boolean> - True if deleted successfully
   */
  abstract deleteAsset(id: string, userId: string): Promise<boolean>;

  /**
   * Search assets across all users (admin function)
   * @param query - Query parameters for searching
   * @returns Promise<AssetListResponse> - Paginated search results
   */
  abstract searchAssets(query: AssetListQuery): Promise<AssetListResponse>;

  /**
   * Get public assets (assets marked as isPublic: true)
   * @param query - Query parameters for filtering and pagination
   * @returns Promise<AssetListResponse> - Paginated list of public assets
   */
  abstract getPublicAssets(query?: AssetListQuery): Promise<AssetListResponse>;

  /**
   * Get assets by tags
   * @param userId - The user ID
   * @param tags - Array of tags to filter by
   * @param query - Additional query parameters
   * @returns Promise<AssetListResponse> - Paginated list of assets with matching tags
   */
  abstract getAssetsByTags(
    userId: string,
    tags: string[],
    query?: AssetListQuery
  ): Promise<AssetListResponse>;

  /**
   * Get asset statistics for a user
   * @param userId - The user ID
   * @returns Promise with asset statistics
   */
  abstract getAssetStats(userId: string): Promise<{
    totalAssets: number;
    totalSize: number;
    assetsByType: Record<string, number>;
    assetsByStatus: Record<string, number>;
  }>;
}
