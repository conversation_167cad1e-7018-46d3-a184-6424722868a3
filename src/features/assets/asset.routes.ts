// Import endpoints
import { <PERSON><PERSON>AssetEndpoint } from "./endpoints/create-asset";
import { GetUserAssetsEndpoint } from "./endpoints/get-user-assets";
import { GetAssetByIdEndpoint } from "./endpoints/get-asset-by-id";
import { GetPublicAssetByIdEndpoint } from "./endpoints/get-public-asset-by-id";
import { UpdateAssetEndpoint } from "./endpoints/update-asset";
import { DeleteAssetEndpoint } from "./endpoints/delete-asset";
import { GetAssetStatsEndpoint } from "./endpoints/get-asset-stats";

import { authMiddleware } from "../../middleware/auth.middleware";

/**
 * Register all asset-related routes
 * @param openapi - The OpenAPI Hono instance
 * @param basePath - The base path for asset routes (e.g., "/api/v1/assets")
 */
export function registerAssetRoutes(openapi: any, basePath: string) {
  // POST /assets - Create a new asset
  openapi.post(basePath, authMiddleware, CreateAssetEndpoint);

  // GET /assets - Get user's assets with pagination and filtering
  openapi.get(basePath, authMiddleware, GetUserAssetsEndpoint);

  // GET /assets/stats - Get user's asset statistics
  openapi.get(`${basePath}/stats`, authMiddleware, GetAssetStatsEndpoint);

  // GET /assets/:id - Get specific asset by ID (requires authentication)
  openapi.get(`${basePath}/:id`, authMiddleware, GetAssetByIdEndpoint);

  // GET /assets/:id/public - Get public asset by ID (no authentication required)
  openapi.get(`${basePath}/:id/public`, GetPublicAssetByIdEndpoint);

  // PUT /assets/:id - Update asset metadata
  openapi.put(`${basePath}/:id`, authMiddleware, UpdateAssetEndpoint);

  // DELETE /assets/:id - Delete asset (soft delete)
  openapi.delete(`${basePath}/:id`, authMiddleware, DeleteAssetEndpoint);
}
