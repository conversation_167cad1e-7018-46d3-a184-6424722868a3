import { z } from "zod";

// Schema for the POST /process-message request body
export const processMessageSchema = z.object({
  chatId: z.string().min(1, { message: "chatId is required" }),
  messageId: z.string().min(1, { message: "messageId is required" }),
  userId: z.string().optional(), // Optional, was used for rate limiting
});

// Export the inferred type
export type ProcessMessageBody = z.infer<typeof processMessageSchema>;
