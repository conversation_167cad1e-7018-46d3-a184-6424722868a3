// import { singleton, inject } from "tsyringe";
// import type { GenerateContentRequest } from "@google/generative-ai";
// import { GeminiService } from "../../infrastructure/ai/GeminiService";
// import { ChatService } from "../chat/chat.service";
// import { StorageService } from "../../infrastructure/storage/StorageService";
// import type { Message } from "../chat/message.schema";
// import type { Attachment } from "../chat/message.schema";

// // 将Blob转换为Base64字符串的辅助函数
// async function blobToBase64(blob: Blob): Promise<string> {
//   const arrayBuffer = await blob.arrayBuffer();
//   const buffer = Buffer.from(arrayBuffer);
//   return buffer.toString("base64");
// }

// @singleton()
// export class MessageProcessService {
//   constructor(
//     @inject(ChatService) private chatService: ChatService,
//     @inject(StorageService) private storageService: StorageService,
//     @inject(GeminiService) private geminiService: GeminiService
//   ) {}

//   // 处理消息内容
//   async processMessage(chatId: string, messageId: string): Promise<any> {
//     // 1. 获取消息内容
//     const message = await this.chatService.getMessageById(chatId, messageId);

//     if (!message) {
//       throw new Error(`消息不存在: ${messageId}`);
//     }

//     // 2. 准备请求参数
//     const request: GenerateContentRequest = {
//       contents: [
//         {
//           role: "user",
//           parts: [{ text: message.content.text }],
//         },
//       ],
//     };

//     // 3. 如果有附件（如图片），添加到请求中
//     if (message.content.attachments && message.content.attachments.length > 0) {
//       for (const attachment of message.content.attachments) {
//         if (attachment.type === "image" && attachment.url) {
//           try {
//             // 假设url是一个可以直接访问的图片URL或base64数据
//             const imageData = attachment.url;
//             const mimeType = attachment.mimeType || "image/jpeg";

//             // 检查是否是base64编码的图片
//             if (
//               typeof imageData === "string" &&
//               imageData.startsWith("data:image/")
//             ) {
//               request.contents[0].parts.push({
//                 inlineData: {
//                   data: imageData.split(",")[1],
//                   mimeType,
//                 },
//               });
//             } else {
//               // 对于外部URL的图片，这里需要先下载或者使用fileData
//               // 使用storageService处理图片
//               console.log("Processing image:", attachment.url);
//               console.log("Processing attachment:", attachment);
//               const fileInfo = await this.storageService.getFile(
//                 attachment.url
//               );
//               if (fileInfo) {
//                 // 如果是存储服务中的文件，直接获取其内容
//                 const response = await fetch(fileInfo.url);
//                 const blob = await response.blob();
//                 const base64 = await blobToBase64(blob);

//                 request.contents[0].parts.push({
//                   inlineData: {
//                     data: base64,
//                     mimeType: fileInfo.contentType,
//                   },
//                 });
//               } else {
//                 // 下载外部URL的图片
//                 const response = await fetch(imageData);
//                 if (response.ok) {
//                   const blob = await response.blob();
//                   const base64 = await blobToBase64(blob);

//                   request.contents[0].parts.push({
//                     inlineData: {
//                       data: base64,
//                       mimeType,
//                     },
//                   });
//                 } else {
//                   console.error(
//                     `无法获取图片: ${imageData}`,
//                     response.statusText
//                   );
//                 }
//               }
//             }
//           } catch (error) {
//             console.error(`处理附件时出错: ${attachment.id}`, error);
//           }
//         }
//       }
//     }

//     // 4. 处理请求
//     const result = await this.geminiService.processRequest(request);

//     // 5. 创建助手回复消息
//     let responseText = "";
//     let responseAttachments: Attachment[] = [];

//     // 解析处理结果
//     for (const part of result) {
//       if (part.text) {
//         responseText += part.text;
//       }

//       // 如果结果包含图片
//       if (part.inlineData) {
//         try {
//           // 上传图片到存储服务
//           const base64Image = `data:${
//             part.inlineData.mimeType || "image/jpeg"
//           };base64,${part.inlineData.data}`;
//           const uploadResult = await this.storageService.uploadFromSource(
//             base64Image,
//             `ai-generated-${Date.now()}.jpg`,
//             {
//               path: `chats/${chatId}/images`,
//               metadata: {
//                 generated: "true",
//                 messageId,
//                 generatedBy: "gemini",
//               },
//               generateThumbnail: false,
//             }
//           );

//           // 创建附件对象
//           const imageAttachment: Attachment = {
//             id: uploadResult.id,
//             type: "image",
//             url: uploadResult.url, // 存储路径作为引用
//             mimeType: uploadResult.contentType,
//             filename: uploadResult.fileName,
//             fileSize: uploadResult.size,
//             isUploaded: true,
//             uploadProgress: 100,
//           };

//           // 只有在存在缩略图 URL 时才添加该属性
//           if (uploadResult.thumbnailUrl) {
//             imageAttachment.thumbnailUrl = uploadResult.thumbnailUrl;
//           }

//           responseAttachments.push(imageAttachment);
//         } catch (error) {
//           console.error("保存生成的图片失败:", error);
//         }
//       }
//     }

//     // 创建助手回复消息
//     const now = new Date();
//     const assistantMessage = await this.chatService.createMessage(chatId, {
//       content: {
//         text: responseText,
//         styleType: "plainText",
//         attachments: responseAttachments,
//       },
//       role: "assistant",
//       sender: {
//         id: "ai-assistant",
//         type: "ai",
//       },
//       type: "ai",
//       status: "sent",
//       isRead: false,
//       timestamp: {
//         created: now,
//         updated: now,
//       },
//       metadata: {
//         generatedBy: "gemini",
//         originalMessageId: messageId,
//       },
//     });

//     return {
//       originalMessage: message,
//       response: assistantMessage,
//     };
//   }
// }
