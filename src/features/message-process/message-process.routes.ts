// import { <PERSON>o } from "hono";
// import { container } from "tsyringe";
// import { zValidator } from "@hono/zod-validator";
// import { MessageProcessService } from "./message-process.service";
// import { processMessageSchema } from "./message-process.schema";

// const messageProcessRoutes = new Hono();

// messageProcessRoutes.post(
//   "/process-message",
//   zValidator("json", processMessageSchema, (result, c) => {
//     if (!result.success) {
//       return c.json(
//         {
//           success: false,
//           message: "Validation failed",
//           errors: result.error.flatten().fieldErrors,
//         },
//         400
//       );
//     }
//   }),
//   async (c) => {
//     try {
//       const validatedData = c.req.valid("json");
//       const { chatId, messageId, userId } = validatedData;
//       const messageProcessService = container.resolve(MessageProcessService);
//       const result = await messageProcessService.processMessage(
//         chatId,
//         messageId
//       );

//       return c.json(result);
//     } catch (error: any) {
//       console.error("Failed to process message:", error);
//       if (error.message?.includes("not found")) {
//         return c.json({ error: error.message }, 404);
//       } else if (error.message?.includes("Gemini AI service unavailable")) {
//         return c.json(
//           { error: "AI service unavailable, please try again later" },
//           503
//         );
//       }
//       return c.json({ error: "Failed to process message internally" }, 500);
//     }
//   }
// );

// export default messageProcessRoutes;
