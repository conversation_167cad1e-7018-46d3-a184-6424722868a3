import {
  authMiddleware,
  optionalAuthMiddleware,
} from "../../middleware/auth.middleware";
import { FileUploadEndpoint } from "./endpoints/upload.handler";
import { UploadWithAssetEndpoint } from "./endpoints/upload-with-asset";

/**
 * Registers the file upload routes with the given Hono/Chanfana application instance.
 * @param openapi - The Hono application instance (used by Chanfana for route registration).
 * @param routePrefix - Optional prefix for the routes (e.g., "/files"). Defaults to "/upload".
 */
export function registerFileUploadRoutes(
  openapi: any,
  routePrefix: string = "/upload"
) {
  const base = routePrefix.startsWith("/") ? routePrefix : `/${routePrefix}`;

  // Basic file upload (returns URL only)
  openapi.post(base, optionalAuthMiddleware(), FileUploadEndpoint);

  // Upload with asset creation (requires authentication, returns full asset record)
  openapi.post(`${base}/with-asset`, authMiddleware, UploadWithAssetEndpoint);
}
