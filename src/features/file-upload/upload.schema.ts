import { z } from "zod";
import type { UploadResult } from "../../infrastructure/storage/storage.schema";

// Schema for the request body of the POST /upload endpoint
export const UploadRequestSchema = z.object({
  mimeType: z
    .string({
      required_error: "mimeType is required",
      invalid_type_error: "mimeType must be a string",
    })
    .min(1, { message: "mimeType cannot be empty" }),
  base64Data: z
    .string({
      required_error: "base64Data is required",
      invalid_type_error: "base64Data must be a string",
    })
    .min(1, { message: "base64Data cannot be empty" }),
  prefix: z.string().optional(),
  fileName: z.string().optional(), // Optional: if not provided, a generic name will be used by the handler
});

export type UploadRequest = z.infer<typeof UploadRequestSchema>;

// Schema for the successful response of the POST /upload endpoint
// This reuses the UploadResult type, but we define it as a Zod schema for Chanfana
export const UploadResponseSchema = z.object({
  id: z.string(),
  fileName: z.string(),
  contentType: z.string(),
  size: z.number(),
  url: z.string().url(),
  thumbnailUrl: z.string().url().optional(),
  path: z.string(),
  bucket: z.string(),
  createdAt: z.preprocess((arg) => {
    if (typeof arg === "string" || arg instanceof Date) return new Date(arg);
    return arg;
  }, z.date()),
  metadata: z.record(z.string()).optional(),
});

export type UploadResponse = z.infer<typeof UploadResponseSchema>;

// Generic error schema for 500 responses
export const GenericErrorSchema = z.object({
  error: z.string(),
});

// Schema for Zod validation errors (400 responses)
export const ZodValidationErrorSchema = z.object({
  error: z.string(),
  issues: z
    .array(
      z.object({
        code: z.string(),
        expected: z.string().optional(),
        received: z.string().optional(),
        path: z.array(z.string()),
        message: z.string(),
      })
    )
    .optional(),
});
