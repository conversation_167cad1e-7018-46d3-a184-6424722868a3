import { type Context } from "hono";
import { OpenAPIRoute } from "chanfana";

import {
  UploadRequestSchema,
  UploadResponseSchema,
  GenericErrorSchema,
  ZodValidationErrorSchema,
} from "../upload.schema";
import type { HonoEnv } from "../../../types";
import {
  IStorageService,
  StorageService,
} from "../../../infrastructure/storage/storage-service.interface";
import { buildErrorResponse, buildSuccessResponse } from "../../../lib/utils";
import { base64Image } from "../../../lib/constants";
import { getFirebaseToken } from "@hono/firebase-auth";

export class FileUploadEndpoint extends OpenAPIRoute {
  schema = {
    summary: "Upload a file using Base64 data",
    description:
      "Uploads a file encoded in Base64. The 'prefix' field can be used to specify a directory for the uploaded file.",
    tags: ["FileUpload"],
    request: {
      body: {
        content: {
          "application/json": {
            schema: UploadRequestSchema,
            examples: {
              imageUpload: {
                summary: "Upload an image file",
                description: "Example request for uploading a JPEG image",
                value: {
                  mimeType: "image/jpeg",
                  base64Data: base64Image,
                  prefix: "uploads/images",
                  fileName: "profile-picture",
                },
              },
              minimalUpload: {
                summary: "Minimal upload request",
                description: "Example with only required fields",
                value: {
                  mimeType: "image/png",
                  base64Data: base64Image,
                },
              },
              documentUpload: {
                summary: "Upload a document file",
                description: "Example request for uploading a PDF document",
                value: {
                  mimeType: "application/pdf",
                  base64Data:
                    "JVBERi0xLjQKJcOkw7zDtsO8CjIgMCBvYmoKPDwKL0xlbmd0aCAzIDAgUgovRmlsdGVyIC9GbGF0ZURlY29kZQo+PgpzdHJlYW0KeJxLy8nPS8ksqUwFAAwYAz4KZW5kc3RyZWFtCmVuZG9iagoKMyAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgNCAwIFIKPj4KZW5kb2JqCgo0IDAgb2JqCjw8Ci9UeXBlIC9QYWdlcwovS2lkcyBbNSAwIFJdCi9Db3VudCAxCj4+CmVuZG9iagoKNSAwIG9iago8PAovVHlwZSAvUGFnZQovUGFyZW50IDQgMCBSCi9NZWRpYUJveCBbMCAwIDYxMiA3OTJdCj4+CmVuZG9iagoKeHJlZgowIDYKMDAwMDAwMDAwMCA2NTUzNSBmIAowMDAwMDAwMDA5IDAwMDAwIG4gCjAwMDAwMDAwNzQgMDAwMDAgbiAKMDAwMDAwMDEyMCAwMDAwMCBuIAowMDAwMDAwMTY1IDAwMDAwIG4gCjAwMDAwMDAyMjIgMDAwMDAgbiAKdHJhaWxlcgo8PAovU2l6ZSA2Ci9Sb290IDMgMCBSCj4+CnN0YXJ0eHJlZgoyODQKJSVFT0Y=",
                  prefix: "uploads/documents",
                  fileName: "contract.pdf",
                },
              },
            },
          },
        },
        required: true,
      },
    },
    responses: {
      "201": {
        description: "File uploaded successfully",
        content: {
          "application/json": {
            schema: UploadResponseSchema,
          },
        },
      },
      "400": {
        description:
          "Validation failed or invalid input (e.g., bad Base64 data, missing fields)",
        content: { "application/json": { schema: ZodValidationErrorSchema } },
      },
      "500": {
        description: "Internal server error during file upload",
        content: { "application/json": { schema: GenericErrorSchema } },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    try {
      const { body } = await this.getValidatedData<typeof this.schema>();
      const { mimeType, base64Data, prefix, fileName } = body;
      const container = c.get("container");
      const storageService = container.resolve<StorageService>(IStorageService);

      // Potentially get userId from context if authentication is implemented
      // const userId = c.get("user")?.id; // Example: if auth middleware sets c.get("user")

      const uploadResult = await storageService.uploadImage(
        mimeType,
        base64Data,
        fileName,
        prefix
      );

      if (!uploadResult) {
        return buildErrorResponse("File upload failed due to invalid input.");
      }

      const firebaseToken = getFirebaseToken(c);
      if (firebaseToken) {
        const userId = firebaseToken.uid;
        // const user = await container.resolve<IUserRepository>(IUserRepository).getUserById(userId);
      }

      return c.json(
        {
          url: uploadResult,
        },
        201
      );
    } catch (error: any) {
      console.error("Error during file upload:", error);
      // Check if it's a Zod validation error from getValidatedData
      if (error.name === "ZodValidationError") {
        return c.json(
          {
            error: "Validation failed",
            issues: error.errors.map((e: any) => ({
              code: e.code,
              path: e.path,
              message: e.message,
              expected: e.expected, // some Zod issues have these
              received: e.received, // some Zod issues have these
            })),
          },
          400
        );
      }
      return c.json(
        { error: "File upload failed due to an internal error." },
        500
      );
    }
  }
}
