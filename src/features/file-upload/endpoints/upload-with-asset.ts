import { type Context } from "hono";
import { OpenAPIRoute } from "chanfana";
import { getFirebaseToken } from "@hono/firebase-auth";
import { z } from "zod";

import { UploadRequestSchema } from "../upload.schema";
import {
  AssetResponseSchema,
  CreateAssetData,
} from "../../assets/asset.schema";
import type { HonoEnv } from "../../../types";
import {
  IStorageService,
  StorageService,
} from "../../../infrastructure/storage/storage-service.interface";
import { IAssetService, type AssetService } from "../../assets/asset.interface";
import { base64Image } from "../../../lib/constants";

// Extended upload request schema with asset metadata
const UploadWithAssetRequestSchema = UploadRequestSchema.extend({
  tags: z.array(z.string()).optional(),
  description: z.string().optional(),
  metadata: z.record(z.any()).optional(),
  isPublic: z.boolean().optional().default(false),
});

export class UploadWithAssetEndpoint extends OpenAPIRoute {
  schema = {
    summary: "Upload a file and create asset record",
    description:
      "Uploads a file encoded in Base64 and automatically creates an asset record for the authenticated user",
    tags: ["FileUpload", "Assets"],
    security: [
      {
        BearerAuth: [],
      },
    ],
    request: {
      body: {
        content: {
          "application/json": {
            schema: UploadWithAssetRequestSchema,
            examples: {
              imageUploadWithAsset: {
                summary: "Upload an image with asset metadata",
                description:
                  "Example request for uploading a JPEG image with asset information",
                value: {
                  mimeType: "image/jpeg",
                  base64Data: base64Image,
                  prefix: "uploads/images",
                  fileName: "profile-picture",
                  tags: ["profile", "user", "avatar"],
                  description: "User profile picture",
                  metadata: {
                    camera: "iPhone 14",
                    location: "Office",
                  },
                  isPublic: false,
                },
              },
              documentUpload: {
                summary: "Upload a document",
                description: "Example request for uploading a PDF document",
                value: {
                  mimeType: "application/pdf",
                  base64Data:
                    "JVBERi0xLjQKJcOkw7zDtsO8CjIgMCBvYmoKPDwKL0xlbmd0aCAzIDAgUgo+PgpzdHJlYW0KQlQKL0YxIDEyIFRmCjEwMCA3MjAgVGQKKEhlbGxvIFdvcmxkKSBUagpFVAplbmRzdHJlYW0KZW5kb2JqCg==",
                  prefix: "uploads/documents",
                  fileName: "report",
                  tags: ["report", "business"],
                  description: "Monthly business report",
                  isPublic: false,
                },
              },
            },
          },
        },
      },
    },
    responses: {
      201: {
        description: "File uploaded and asset created successfully",
        content: {
          "application/json": {
            schema: AssetResponseSchema,
          },
        },
      },
      400: {
        description: "Invalid request data",
        content: {
          "application/json": {
            schema: z.object({
              error: z.string(),
              details: z.any().optional(),
            }),
          },
        },
      },
      401: {
        description: "Authentication required",
        content: {
          "application/json": {
            schema: z.object({
              error: z.string(),
            }),
          },
        },
      },
      500: {
        description: "Internal server error",
        content: {
          "application/json": {
            schema: z.object({
              error: z.string(),
            }),
          },
        },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    try {
      // Get Firebase token for user authentication
      const firebaseToken = getFirebaseToken(c);
      if (!firebaseToken) {
        return c.json({ error: "Authentication required" }, 401);
      }

      const userId = firebaseToken.uid;
      const { body } = await this.getValidatedData<typeof this.schema>();
      const {
        mimeType,
        base64Data,
        prefix,
        fileName,
        tags,
        description,
        metadata,
        isPublic,
      } = body;

      const container = c.get("container");
      const storageService = container.resolve<StorageService>(IStorageService);
      const assetService = container.resolve<AssetService>(IAssetService);

      // Upload file with metadata
      const uploadResult = await storageService.uploadImageWithMetadata(
        mimeType,
        base64Data,
        fileName,
        prefix
      );

      if (!uploadResult) {
        return c.json({ error: "File upload failed" }, 500);
      }

      // Create asset record
      const assetData = {
        // 存储信息
        thumbnailUrl: uploadResult.thumbnailUrl,
        path: uploadResult.path,
        bucket: uploadResult.bucket,

        // Web 标准字段
        name: uploadResult.fileName,
        size: uploadResult.size,
        type: uploadResult.contentType, // MIME type
        url: uploadResult.url,

        // 业务分类字段
        sourceType: "user_upload" as const,

        // 社交统计字段
        likeCount: 0,
        favoriteCount: 0,

        // 元数据
        tags,
        description,
        metadata: {
          ...uploadResult.metadata,
          ...metadata,
        },

        // 状态管理
        isPublic: isPublic || false,
        status: "active" as const,
      } as CreateAssetData;

      const asset = await assetService.createAsset(userId, assetData);

      // Convert dates to ISO strings for response
      const response = {
        ...asset,
        createdAt: asset.createdAt.toISOString(),
      };

      return c.json(response, 201);
    } catch (error: any) {
      console.error("Error uploading file with asset:", error);

      if (error.message.includes("validation")) {
        return c.json(
          {
            error: "Invalid request data",
            details: error.message,
          },
          400
        );
      }

      return c.json({ error: "Failed to upload file and create asset" }, 500);
    }
  }
}
