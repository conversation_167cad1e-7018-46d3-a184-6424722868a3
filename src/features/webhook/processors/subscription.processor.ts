import { inject, singleton } from "tsyringe";
import type { UserService } from "../../user/user.interface";
import { IUserService } from "../../user/user.interface";
import type { UpdateUserSubscriptionData } from "../../user/user.schema";

/**
 * Subscription processor
 * Handles subscription-related business logic for webhook events
 */
@singleton()
export class SubscriptionProcessor {
  constructor(
    @inject(IUserService) private userService: UserService
  ) {}

  /**
   * Process subscription update for a user
   */
  async processSubscriptionUpdate(
    userId: string,
    subscriptionData: UpdateUserSubscriptionData
  ): Promise<{ success: boolean; message: string }> {
    try {
      console.log(`[SubscriptionProcessor] Processing subscription update for user: ${userId}`);
      console.log(`[SubscriptionProcessor] Subscription data:`, subscriptionData);

      // Validate subscription data
      const validation = this.validateSubscriptionData(subscriptionData);
      if (!validation.isValid) {
        const errorMessage = `Invalid subscription data: ${validation.errors.join(", ")}`;
        console.error(`[SubscriptionProcessor] ${errorMessage}`);
        return { success: false, message: errorMessage };
      }

      // Update user subscription
      const updateResult = await this.userService.updateUserSubscription(
        userId,
        subscriptionData
      );

      if (updateResult) {
        const successMessage = `Successfully updated subscription for user: ${userId}`;
        console.log(`[SubscriptionProcessor] ${successMessage}`);
        return { success: true, message: successMessage };
      } else {
        const warningMessage = `User ${userId} not found, subscription update skipped`;
        console.warn(`[SubscriptionProcessor] ${warningMessage}`);
        return { success: false, message: warningMessage };
      }

    } catch (error: any) {
      const errorMessage = `Error processing subscription update for user ${userId}: ${error.message}`;
      console.error(`[SubscriptionProcessor] ${errorMessage}`, error);
      return { success: false, message: errorMessage };
    }
  }

  /**
   * Get current subscription status for a user
   */
  async getCurrentSubscriptionStatus(userId: string) {
    try {
      console.log(`[SubscriptionProcessor] Getting subscription status for user: ${userId}`);
      
      const subscription = await this.userService.getUserSubscriptionStatus(userId);
      
      if (subscription) {
        console.log(`[SubscriptionProcessor] Found subscription for user ${userId}:`, subscription);
        return { success: true, data: subscription };
      } else {
        console.log(`[SubscriptionProcessor] No subscription found for user: ${userId}`);
        return { success: false, message: "No subscription found" };
      }

    } catch (error: any) {
      const errorMessage = `Error getting subscription status for user ${userId}: ${error.message}`;
      console.error(`[SubscriptionProcessor] ${errorMessage}`, error);
      return { success: false, message: errorMessage };
    }
  }

  /**
   * Check if subscription status change is valid
   */
  validateStatusTransition(
    currentStatus: string | undefined,
    newStatus: string
  ): { isValid: boolean; message?: string } {
    // Define valid status transitions
    const validTransitions: Record<string, string[]> = {
      undefined: ["active", "canceled", "expired"], // New subscription
      active: ["canceled", "expired"],
      canceled: ["active", "expired"],
      expired: ["active"], // Can reactivate expired subscription
    };

    const allowedStatuses = validTransitions[currentStatus || "undefined"] || [];
    
    if (allowedStatuses.includes(newStatus)) {
      return { isValid: true };
    } else {
      return {
        isValid: false,
        message: `Invalid status transition from '${currentStatus || "none"}' to '${newStatus}'`
      };
    }
  }

  /**
   * Validate subscription data
   */
  private validateSubscriptionData(data: UpdateUserSubscriptionData): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!data.status) {
      errors.push("Missing subscription status");
    } else if (!["active", "canceled", "expired"].includes(data.status)) {
      errors.push(`Invalid subscription status: ${data.status}`);
    }

    if (!Array.isArray(data.entitlements)) {
      errors.push("Entitlements must be an array");
    }

    if (data.expires_date && isNaN(Date.parse(data.expires_date))) {
      errors.push("Invalid expires_date format");
    }

    if (!data.last_updated_from_webhook || isNaN(Date.parse(data.last_updated_from_webhook))) {
      errors.push("Invalid or missing last_updated_from_webhook");
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
