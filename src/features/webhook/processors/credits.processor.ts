import { inject, singleton } from "tsyringe";
import type { UserService } from "../../user/user.interface";
import { IUserService } from "../../user/user.interface";
import type { UpdateUserCreditsData } from "../../user/user.schema";

/**
 * Credits processor
 * Handles credits-related business logic for webhook events
 */
@singleton()
export class CreditsProcessor {
  constructor(
    @inject(IUserService) private userService: UserService
  ) {}

  /**
   * Process credits update for a user
   */
  async processCreditsUpdate(
    userId: string,
    creditsData: UpdateUserCreditsData
  ): Promise<{ success: boolean; message: string }> {
    try {
      console.log(`[CreditsProcessor] Processing credits update for user: ${userId}`);
      console.log(`[CreditsProcessor] Credits data:`, creditsData);

      // Validate credits data
      const validation = this.validateCreditsData(creditsData);
      if (!validation.isValid) {
        const errorMessage = `Invalid credits data: ${validation.errors.join(", ")}`;
        console.error(`[CreditsProcessor] ${errorMessage}`);
        return { success: false, message: errorMessage };
      }

      // Update user credits
      const updateResult = await this.userService.updateUserCredits(
        userId,
        creditsData
      );

      if (updateResult) {
        const successMessage = `Successfully updated credits for user: ${userId} (${creditsData.credits} credits)`;
        console.log(`[CreditsProcessor] ${successMessage}`);
        return { success: true, message: successMessage };
      } else {
        const warningMessage = `User ${userId} not found, credits update skipped`;
        console.warn(`[CreditsProcessor] ${warningMessage}`);
        return { success: false, message: warningMessage };
      }

    } catch (error: any) {
      const errorMessage = `Error processing credits update for user ${userId}: ${error.message}`;
      console.error(`[CreditsProcessor] ${errorMessage}`, error);
      return { success: false, message: errorMessage };
    }
  }

  /**
   * Add credits to a user's account
   */
  async addCredits(
    userId: string,
    creditsToAdd: number,
    reason?: string
  ): Promise<{ success: boolean; message: string; newBalance?: number }> {
    try {
      console.log(`[CreditsProcessor] Adding ${creditsToAdd} credits to user: ${userId}${reason ? ` (${reason})` : ""}`);

      if (creditsToAdd <= 0) {
        const errorMessage = "Credits to add must be positive";
        console.error(`[CreditsProcessor] ${errorMessage}`);
        return { success: false, message: errorMessage };
      }

      // Get current user to check existing credits
      const currentUser = await this.userService.getUserById(userId);
      if (!currentUser) {
        const errorMessage = `User ${userId} not found`;
        console.error(`[CreditsProcessor] ${errorMessage}`);
        return { success: false, message: errorMessage };
      }

      const currentCredits = currentUser.credits || 0;
      const newBalance = currentCredits + creditsToAdd;

      // Update credits
      const updateResult = await this.userService.updateUserCredits(
        userId,
        { credits: newBalance }
      );

      if (updateResult) {
        const successMessage = `Successfully added ${creditsToAdd} credits to user: ${userId}`;
        console.log(`[CreditsProcessor] ${successMessage} (${currentCredits} → ${newBalance})`);
        return { success: true, message: successMessage, newBalance };
      } else {
        const errorMessage = `Failed to update credits for user: ${userId}`;
        console.error(`[CreditsProcessor] ${errorMessage}`);
        return { success: false, message: errorMessage };
      }

    } catch (error: any) {
      const errorMessage = `Error adding credits for user ${userId}: ${error.message}`;
      console.error(`[CreditsProcessor] ${errorMessage}`, error);
      return { success: false, message: errorMessage };
    }
  }

  /**
   * Set credits for a user (absolute value)
   */
  async setCredits(
    userId: string,
    credits: number,
    reason?: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      console.log(`[CreditsProcessor] Setting credits to ${credits} for user: ${userId}${reason ? ` (${reason})` : ""}`);

      const validation = this.validateCreditsData({ credits });
      if (!validation.isValid) {
        const errorMessage = `Invalid credits value: ${validation.errors.join(", ")}`;
        console.error(`[CreditsProcessor] ${errorMessage}`);
        return { success: false, message: errorMessage };
      }

      const updateResult = await this.userService.updateUserCredits(
        userId,
        { credits }
      );

      if (updateResult) {
        const successMessage = `Successfully set credits to ${credits} for user: ${userId}`;
        console.log(`[CreditsProcessor] ${successMessage}`);
        return { success: true, message: successMessage };
      } else {
        const errorMessage = `User ${userId} not found, credits update skipped`;
        console.warn(`[CreditsProcessor] ${errorMessage}`);
        return { success: false, message: errorMessage };
      }

    } catch (error: any) {
      const errorMessage = `Error setting credits for user ${userId}: ${error.message}`;
      console.error(`[CreditsProcessor] ${errorMessage}`, error);
      return { success: false, message: errorMessage };
    }
  }

  /**
   * Get current credits balance for a user
   */
  async getCurrentCredits(userId: string): Promise<{ success: boolean; credits?: number; message?: string }> {
    try {
      console.log(`[CreditsProcessor] Getting credits balance for user: ${userId}`);
      
      const user = await this.userService.getUserById(userId);
      
      if (user) {
        const credits = user.credits || 0;
        console.log(`[CreditsProcessor] User ${userId} has ${credits} credits`);
        return { success: true, credits };
      } else {
        const errorMessage = `User ${userId} not found`;
        console.error(`[CreditsProcessor] ${errorMessage}`);
        return { success: false, message: errorMessage };
      }

    } catch (error: any) {
      const errorMessage = `Error getting credits for user ${userId}: ${error.message}`;
      console.error(`[CreditsProcessor] ${errorMessage}`, error);
      return { success: false, message: errorMessage };
    }
  }

  /**
   * Validate credits data
   */
  private validateCreditsData(data: UpdateUserCreditsData): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (typeof data.credits !== "number") {
      errors.push("Credits must be a number");
    } else if (data.credits < 0) {
      errors.push("Credits cannot be negative");
    } else if (!Number.isInteger(data.credits)) {
      errors.push("Credits must be an integer");
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
