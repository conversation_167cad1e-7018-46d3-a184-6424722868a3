# Webhook Events Feature

这个功能模块实现了完整的 webhook 事件管理系统，支持接收、存储、查询和处理来自 RevenueCat 等外部服务的 webhook 事件。

## 🏗️ 架构设计

### 核心组件

1. **WebhookEvent Schema** (`webhook-event.schema.ts`)
   - 定义了 WebhookEvent 数据结构和 Zod 验证 schemas
   - 包含状态管理、时间戳处理、分页查询等完整的数据模型

2. **WebhookEvent Service** (`webhook-event.interface.ts` + `firestore-webhook-event.service.ts`)
   - 抽象服务接口定义
   - Firestore 数据库实现
   - 支持 CRUD 操作、状态管理、分页查询

3. **Webhook Endpoints**
   - `RevenueCatWebhookEndpoint`: 接收 RevenueCat webhook 事件
   - `GetUserWebhookEventsEndpoint`: 查询用户的 webhook 事件
   - `GetWebhookEventByIdEndpoint`: 根据 ID 获取特定 webhook 事件

## 📊 数据模型

### WebhookEvent 接口

```typescript
interface WebhookEvent {
  // 基础标识
  id: string;                    // 数据库主键 (UUID)
  eventId: string;               // RevenueCat event.id，用于幂等性检查
  
  // 事件信息
  eventType: string;             // RevenueCat event.type
  appUserId: string;             // 关联到用户系统的 userId
  
  // 状态管理
  status: 'received' | 'processing' | 'succeeded' | 'failed';
  
  // 时间戳
  createdAt: Date;               // webhook 接收时间
  processedAt?: Date;            // 处理完成时间
  
  // 数据载荷
  payload: Record<string, any>;  // 完整的 webhook 数据
  errorMessage?: string;         // 错误信息
  
  // 处理元数据
  processingAttempts?: number;   // 处理尝试次数
  lastAttemptAt?: Date;          // 最后一次处理尝试时间
}
```

### 状态流转

```
received → processing → succeeded/failed
```

- `received`: 初始状态，webhook 已接收并存储
- `processing`: 正在处理中
- `succeeded`: 处理成功
- `failed`: 处理失败

## 🔄 工作流程

### 1. Webhook 接收流程

1. **接收请求**: RevenueCat 发送 webhook 到 `/api/v1/webhook/revenue-cat`
2. **验证数据**: 验证 webhook 载荷格式和必需字段
3. **幂等性检查**: 根据 `eventId` 检查是否已处理过
4. **创建记录**: 在 Firestore 中创建 webhook 事件记录
5. **异步处理**: 使用 `waitUntil` 异步处理业务逻辑
6. **立即响应**: 向 RevenueCat 返回成功响应

### 2. 事件处理流程

1. **标记处理中**: 更新状态为 `processing`
2. **执行业务逻辑**: 根据事件类型执行相应处理
3. **更新状态**: 根据处理结果更新为 `succeeded` 或 `failed`
4. **错误处理**: 失败时记录错误信息和重试次数

## 🔐 安全特性

### 认证授权

- **Webhook 接收**: 无需认证（来自外部服务）
- **事件查询**: 需要 Firebase JWT 认证
- **权限控制**: 用户只能查询自己的 webhook 事件

### 数据验证

- 使用 Zod 进行严格的数据验证
- 必需字段检查（eventId, eventType, appUserId）
- UUID 格式验证

### 幂等性保证

- 基于 `eventId` 的重复检测
- 防止同一事件被重复处理

## 📡 API 端点

### 1. 接收 RevenueCat Webhook

```http
POST /api/v1/webhook/revenue-cat
Content-Type: application/json

{
  "event": {
    "id": "event-123",
    "type": "INITIAL_PURCHASE",
    // ... other RevenueCat fields
  },
  "app_user_id": "user-123"
}
```

### 2. 查询用户 Webhook 事件

```http
GET /api/v1/webhook/events/user/{userId}?page=1&limit=20&status=succeeded
Authorization: Bearer {firebase-jwt}
```

### 3. 获取特定 Webhook 事件

```http
GET /api/v1/webhook/events/{eventId}
Authorization: Bearer {firebase-jwt}
```

## 🗄️ 数据库设计

### Firestore 集合

- **集合名称**: `webhook_events`
- **文档 ID**: UUID
- **索引建议**:
  - `eventId` (单字段，用于幂等性检查)
  - `appUserId + createdAt` (复合索引，用户查询)
  - `status + createdAt` (复合索引，状态查询)

## 🧪 测试

使用提供的 `webhook-events.api.http` 文件进行 API 测试：

1. 替换变量值（baseUrl, authToken, userId 等）
2. 测试各种场景（正常流程、错误处理、权限控制）
3. 验证幂等性和状态管理

## 🔧 配置

### 依赖注入

在 `container.setup.ts` 中已注册：

```typescript
container.registerSingleton(IWebhookEventService, FirestoreWebhookEventService);
```

### 路由注册

在 `webhook.routes.ts` 中已注册所有端点。

## 🚀 扩展性

### 添加新的 Webhook 提供商

1. 创建新的 endpoint 类
2. 在 `webhook.routes.ts` 中注册路由
3. 复用现有的 WebhookEventService

### 添加业务逻辑处理

在 `RevenueCatWebhookEndpoint.processWebhookEvent()` 方法中添加具体的业务逻辑：

```typescript
// 根据事件类型处理
switch (webhookEvent.eventType) {
  case 'INITIAL_PURCHASE':
    await handleInitialPurchase(webhookEvent);
    break;
  case 'RENEWAL':
    await handleRenewal(webhookEvent);
    break;
  // ... 其他事件类型
}
```

## 📈 监控和日志

- 所有关键操作都有详细的控制台日志
- 错误信息会被记录到 webhook 事件记录中
- 支持处理尝试次数统计

## 🔄 重试机制

系统支持失败重试：

- `processingAttempts`: 记录重试次数
- `getRetryableWebhookEvents()`: 获取可重试的事件
- 可以实现定时任务来处理失败的事件

这个实现完全遵循了项目的架构模式和最佳实践，提供了完整的 webhook 事件管理能力。
