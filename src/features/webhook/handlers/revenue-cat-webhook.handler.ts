import { inject, singleton } from "tsyringe";
import type { <PERSON>ho<PERSON><PERSON>and<PERSON> } from "./webhook-handler.interface";
import type { WebhookEvent } from "../webhook-event.schema";
import { RevenueCatTransformer } from "../transformers/revenue-cat.transformer";
import { SubscriptionProcessor } from "../processors/subscription.processor";
import { CreditsProcessor } from "../processors/credits.processor";

/**
 * RevenueCat webhook handler
 * Processes RevenueCat-specific webhook events and updates user subscription/credits data
 */
@singleton()
export class RevenueCatWebhookHandler implements WebhookHandler {
  constructor(
    @inject(RevenueCatTransformer) private transformer: RevenueCatTransformer,
    @inject(SubscriptionProcessor)
    private subscriptionProcessor: SubscriptionProcessor,
    @inject(CreditsProcessor) private creditsProcessor: CreditsProcessor
  ) {}

  /**
   * Check if this handler can process the given event type
   */
  canHandle(eventType: string): boolean {
    // RevenueCat event types this handler can process
    const supportedEventTypes = [
      "INITIAL_PURCHASE",
      "REN<PERSON>WA<PERSON>",
      "CA<PERSON><PERSON><PERSON><PERSON><PERSON>",
      "UNCA<PERSON><PERSON><PERSON><PERSON><PERSON>",
      "EX<PERSON>RA<PERSON><PERSON>",
      "NON_RENEWING_PURCHASE",
      "SUBSCRIPTION_PAUSED",
      "BILLING_ISSUE",
      "PRODUCT_CHANGE",
      "TRANSFER",
      "SUBSCRIPTION_EXTENDED",
      "TEMPORARY_ENTITLEMENT_GRANT",
      "REFUND_REVERSED",
      "INVOICE_ISSUANCE",
      "TEST",
    ];

    return supportedEventTypes.includes(eventType);
  }

  /**
   * Process the RevenueCat webhook event
   */
  async process(webhookEvent: WebhookEvent): Promise<void> {
    const { eventType, appUserId, payload } = webhookEvent;

    console.log(
      `[RevenueCatWebhookHandler] Processing event: ${eventType} for user: ${appUserId}`
    );

    try {
      // Validate payload structure
      const validation = this.transformer.validatePayload(payload);
      if (!validation.isValid) {
        throw new Error(
          `Invalid RevenueCat payload: ${validation.errors.join(", ")}`
        );
      }

      // Process subscription updates
      await this.processSubscriptionUpdates(appUserId, payload, eventType);

      // Process credits updates
      await this.processCreditsUpdates(appUserId, payload, eventType);

      console.log(
        `[RevenueCatWebhookHandler] Successfully processed event: ${eventType} for user: ${appUserId}`
      );
    } catch (error: any) {
      console.error(
        `[RevenueCatWebhookHandler] Error processing event ${eventType} for user ${appUserId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Get handler name for identification
   */
  getHandlerName(): string {
    return "RevenueCatWebhookHandler";
  }

  /**
   * Process subscription-related updates
   */
  private async processSubscriptionUpdates(
    userId: string,
    payload: any,
    eventType: string
  ): Promise<void> {
    try {
      // Extract subscription data
      const subscriptionData = this.transformer.extractSubscriptionData(
        payload,
        eventType
      );

      if (subscriptionData) {
        console.log(
          `[RevenueCatWebhookHandler] Processing subscription update for user: ${userId}`
        );

        const result =
          await this.subscriptionProcessor.processSubscriptionUpdate(
            userId,
            subscriptionData
          );

        if (!result.success) {
          console.warn(
            `[RevenueCatWebhookHandler] Subscription update warning: ${result.message}`
          );
        }
      } else {
        console.log(
          `[RevenueCatWebhookHandler] No subscription update needed for event type: ${eventType}`
        );
      }
    } catch (error: any) {
      console.error(
        `[RevenueCatWebhookHandler] Error processing subscription updates:`,
        error
      );
      // Don't throw here - we want to continue with credits processing
    }
  }

  /**
   * Process credits-related updates
   */
  private async processCreditsUpdates(
    userId: string,
    payload: any,
    eventType: string
  ): Promise<void> {
    try {
      // Extract credits data
      const creditsData = this.transformer.extractCreditsData(
        payload,
        eventType
      );

      if (creditsData) {
        console.log(
          `[RevenueCatWebhookHandler] Processing credits update for user: ${userId}`
        );

        const result = await this.creditsProcessor.processCreditsUpdate(
          userId,
          creditsData
        );

        if (!result.success) {
          console.warn(
            `[RevenueCatWebhookHandler] Credits update warning: ${result.message}`
          );
        }
      } else {
        console.log(
          `[RevenueCatWebhookHandler] No credits update needed for event type: ${eventType}`
        );
      }
    } catch (error: any) {
      console.error(
        `[RevenueCatWebhookHandler] Error processing credits updates:`,
        error
      );
      // Don't throw here - subscription updates might have succeeded
    }
  }

  /**
   * Handle specific event types with custom logic
   */
  private async handleSpecialEventTypes(
    userId: string,
    payload: any,
    eventType: string
  ): Promise<void> {
    switch (eventType) {
      case "TEST":
        console.log(
          `[RevenueCatWebhookHandler] Processing test event for user: ${userId}`
        );
        // Test events don't require any data updates
        break;

      case "TRANSFER":
        console.log(`[RevenueCatWebhookHandler] Processing transfer event`);
        // Transfer events might need special handling for multiple users
        await this.handleTransferEvent(payload);
        break;

      case "BILLING_ISSUE":
        console.log(
          `[RevenueCatWebhookHandler] Processing billing issue for user: ${userId}`
        );
        // Billing issues might need special notification logic
        await this.handleBillingIssue(userId, payload);
        break;

      default:
        // Standard processing handled by subscription/credits processors
        break;
    }
  }

  /**
   * Handle transfer events (multiple users involved)
   */
  private async handleTransferEvent(payload: any): Promise<void> {
    // Transfer events involve moving subscriptions between users
    // This would require more complex logic to handle multiple user updates
    console.log(
      `[RevenueCatWebhookHandler] Transfer event handling not yet implemented`
    );
  }

  /**
   * Handle billing issues
   */
  private async handleBillingIssue(
    userId: string,
    payload: any
  ): Promise<void> {
    // Billing issues might require notifications or grace period handling
    console.log(
      `[RevenueCatWebhookHandler] Billing issue handling for user ${userId} not yet implemented`
    );
  }
}
