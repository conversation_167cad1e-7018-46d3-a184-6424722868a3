import type { InjectionToken } from "tsyringe";
import type { WebhookEvent } from "../webhook-event.schema";

/**
 * Generic webhook handler interface
 * Defines the contract for processing different types of webhook events
 */
export interface WebhookHandler {
  /**
   * Check if this handler can process the given event type
   * @param eventType - The type of webhook event
   * @returns true if this handler can process the event
   */
  canHandle(eventType: string): boolean;

  /**
   * Process the webhook event
   * @param webhookEvent - The webhook event to process
   * @returns Promise that resolves when processing is complete
   */
  process(webhookEvent: WebhookEvent): Promise<void>;

  /**
   * Get the name/identifier of this handler
   * @returns Handler name for logging and debugging
   */
  getHandlerName(): string;
}

/**
 * Base webhook processing result
 */
export interface WebhookProcessingResult {
  success: boolean;
  message?: string;
  data?: any;
}

/**
 * Webhook processing context
 */
export interface WebhookProcessingContext {
  eventId: string;
  eventType: string;
  appUserId: string;
  timestamp: Date;
  payload: Record<string, any>;
}

// Define injection tokens for different webhook handlers
export const IRevenueCatWebhookHandler: InjectionToken<WebhookHandler> = Symbol(
  "IRevenueCatWebhookHandler"
);

/**
 * Webhook handler registry interface
 * Manages multiple webhook handlers and routes events to appropriate handlers
 */
export interface WebhookHandlerRegistry {
  /**
   * Register a webhook handler
   * @param handler - The handler to register
   */
  registerHandler(handler: WebhookHandler): void;

  /**
   * Get the appropriate handler for an event type
   * @param eventType - The type of webhook event
   * @returns The handler that can process this event type, or null if none found
   */
  getHandler(eventType: string): WebhookHandler | null;

  /**
   * Get all registered handlers
   * @returns Array of all registered handlers
   */
  getAllHandlers(): WebhookHandler[];
}

export const IWebhookHandlerRegistry: InjectionToken<WebhookHandlerRegistry> =
  Symbol("IWebhookHandlerRegistry");
