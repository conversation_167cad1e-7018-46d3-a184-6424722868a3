import { singleton } from "tsyringe";
import type { WebhookHandler, WebhookHandlerRegistry } from "./webhook-handler.interface";

/**
 * Default implementation of WebhookHandlerRegistry
 * Manages webhook handlers and routes events to appropriate handlers
 */
@singleton()
export class DefaultWebhookHandlerRegistry implements WebhookHandlerRegistry {
  private handlers: WebhookHandler[] = [];

  /**
   * Register a webhook handler
   */
  registerHandler(handler: WebhookHandler): void {
    console.log(`[WebhookRegistry] Registering handler: ${handler.getHandlerName()}`);
    this.handlers.push(handler);
  }

  /**
   * Get the appropriate handler for an event type
   */
  getHandler(eventType: string): WebhookHandler | null {
    const handler = this.handlers.find(h => h.canHandle(eventType));
    
    if (handler) {
      console.log(`[WebhookRegistry] Found handler ${handler.getHandlerName()} for event type: ${eventType}`);
    } else {
      console.warn(`[WebhookRegistry] No handler found for event type: ${eventType}`);
    }
    
    return handler || null;
  }

  /**
   * Get all registered handlers
   */
  getAllHandlers(): WebhookHandler[] {
    return [...this.handlers];
  }
}
