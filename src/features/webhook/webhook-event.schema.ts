import { z } from "zod";

/**
 * Webhook Event Status Enumeration
 * Follows the same pattern as VideoTask and ImageTask status
 */
export const WebhookEventStatusSchema = z.enum([
  "received", // Initial state when webhook is received
  "processing", // Currently being processed
  "succeeded", // Successfully processed
  "failed", // Processing failed
]);

export type WebhookEventStatus = z.infer<typeof WebhookEventStatusSchema>;

/**
 * Core WebhookEvent interface
 * Follows project naming conventions and patterns
 */
export interface WebhookEvent {
  // 基础标识
  id: string; // 数据库主键 (UUID)
  eventId: string; // RevenueCat event.id，用于幂等性检查

  // 事件信息
  eventType: string; // RevenueCat event.type
  appUserId: string; // 关联到用户系统的 userId

  // 状态管理
  status: WebhookEventStatus;

  // 时间戳（遵循项目约定）
  createdAt: Date; // webhook 接收时间
  processedAt?: Date; // 处理完成时间（仅在 succeeded/failed 时设置）

  // 数据载荷
  payload: Record<string, any>; // 完整的 webhook 数据
  errorMessage?: string; // 仅在 failed 状态时填充

  // 处理元数据
  processingAttempts?: number; // 处理尝试次数
  lastAttemptAt?: Date; // 最后一次处理尝试时间
}

/**
 * Zod schema for WebhookEvent validation
 */
export const WebhookEventSchema = z.object({
  id: z.string().uuid(),
  eventId: z.string().min(1),
  eventType: z.string().min(1),
  appUserId: z.string().min(1),
  status: WebhookEventStatusSchema,
  createdAt: z.date(),
  processedAt: z.date().optional(),
  payload: z.record(z.any()),
  errorMessage: z.string().optional(),
  processingAttempts: z.number().min(0).optional().default(0),
  lastAttemptAt: z.date().optional(),
});

/**
 * Schema for creating a webhook event (without id and timestamps)
 */
export const CreateWebhookEventSchema = z.object({
  eventId: z.string().min(1),
  eventType: z.string().min(1),
  appUserId: z.string().min(1),
  payload: z.record(z.any()),
  status: WebhookEventStatusSchema.optional(),
});

export type CreateWebhookEventData = z.infer<typeof CreateWebhookEventSchema>;

/**
 * Schema for updating webhook event status
 */
export const UpdateWebhookEventStatusSchema = z.object({
  status: WebhookEventStatusSchema,
  errorMessage: z.string().optional(),
  processingAttempts: z.number().min(0).optional(),
});

export type UpdateWebhookEventStatusData = z.infer<
  typeof UpdateWebhookEventStatusSchema
>;

/**
 * Query parameters for listing webhook events
 */
export const WebhookEventQuerySchema = z.object({
  page: z.coerce.number().min(1).optional().default(1),
  limit: z.coerce.number().min(1).max(100).optional().default(20),
  status: WebhookEventStatusSchema.optional(),
  eventType: z.string().optional(),
  startDate: z.string().datetime().optional(), // ISO string for date filtering
  endDate: z.string().datetime().optional(), // ISO string for date filtering
});

export type WebhookEventQuery = z.infer<typeof WebhookEventQuerySchema>;

/**
 * Response schema for API endpoints
 */
export const WebhookEventResponseSchema = z.object({
  id: z.string().uuid(),
  eventId: z.string(),
  eventType: z.string(),
  appUserId: z.string(),
  status: WebhookEventStatusSchema,
  createdAt: z.string().datetime(), // ISO string for API response
  processedAt: z.string().datetime().optional(),
  payload: z.record(z.any()),
  errorMessage: z.string().optional(),
  processingAttempts: z.number().optional(),
  lastAttemptAt: z.string().datetime().optional(),
});

/**
 * Pagination metadata schema (reusing from existing patterns)
 */
export const PaginationMetaSchema = z.object({
  currentPage: z.number(),
  totalPages: z.number(),
  totalItems: z.number(),
  itemsPerPage: z.number(),
  hasNextPage: z.boolean(),
  hasPreviousPage: z.boolean(),
});

export type PaginationMeta = z.infer<typeof PaginationMetaSchema>;

/**
 * Response schema for listing webhook events
 */
export const WebhookEventListResponseSchema = z.object({
  events: z.array(WebhookEventResponseSchema),
  pagination: PaginationMetaSchema,
});

export type WebhookEventListResponse = z.infer<
  typeof WebhookEventListResponseSchema
>;

/**
 * Path parameters for webhook event endpoints
 */
export const WebhookEventIdParamSchema = z.object({
  id: z.string().uuid(),
});

export type WebhookEventIdParam = z.infer<typeof WebhookEventIdParamSchema>;

/**
 * Request schema for getting webhook events by user
 */
export const GetUserWebhookEventsRequestSchema = z.object({
  userId: z.string().min(1, { message: "User ID cannot be empty." }).openapi({
    example: "bGl3YXcxe3YsZeSSDvlG86kA4wj1",
  }),
});

export type GetUserWebhookEventsRequest = z.infer<
  typeof GetUserWebhookEventsRequestSchema
>;

/**
 * Generic error schemas (reusing from existing patterns)
 */
export const GenericErrorSchema = z.object({ error: z.string() });
export const NotFoundErrorSchema = z.object({
  error: z.literal("Webhook event not found"),
});

/**
 * Success response schema for webhook processing
 */
export const WebhookProcessingResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  eventId: z.string().optional(),
  timestamp: z.string().datetime(),
});

export type WebhookProcessingResponse = z.infer<
  typeof WebhookProcessingResponseSchema
>;
