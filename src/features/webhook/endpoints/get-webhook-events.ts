import { type Context } from "hono";
import { OpenAPIRoute } from "chanfana";

// import { getFirebaseToken } from "@hono/firebase-auth";

import type { HonoEnv } from "../../../types";
import {
  WebhookEventQuerySchema,
  WebhookEventListResponseSchema,
  GetUserWebhookEventsRequestSchema,
  WebhookEventResponseSchema,
  GenericErrorSchema,
  NotFoundErrorSchema,
} from "../webhook-event.schema";
import { z } from "zod";
import { IWebhookEventService } from "../webhook-event.interface";
import type { WebhookEventService } from "../webhook-event.interface";

/**
 * Get User Webhook Events Endpoint
 *
 * This endpoint retrieves webhook events for a specific user with pagination and filtering support.
 * Requires Firebase authentication.
 */
export class GetUserWebhookEventsEndpoint extends OpenAPIRoute {
  schema = {
    tags: ["Webhooks"],
    summary: "Get User Webhook Events",
    description:
      "Retrieve webhook events for a specific user with pagination and filtering",
    security: [{ BearerAuth: [] }],
    request: {
      params: GetUserWebhookEventsRequestSchema,
      query: WebhookEventQuerySchema,
    },
    responses: {
      200: {
        description: "Webhook events retrieved successfully",
        content: {
          "application/json": {
            schema: WebhookEventListResponseSchema,
          },
        },
      },
      400: {
        description: "Invalid request parameters",
        content: {
          "application/json": {
            schema: GenericErrorSchema,
          },
        },
      },
      401: {
        description: "Unauthorized - Invalid or missing authentication token",
        content: {
          "application/json": {
            schema: GenericErrorSchema,
          },
        },
      },
      403: {
        description:
          "Forbidden - User can only access their own webhook events",
        content: {
          "application/json": {
            schema: GenericErrorSchema,
          },
        },
      },
      404: {
        description: "User not found",
        content: {
          "application/json": {
            schema: NotFoundErrorSchema,
          },
        },
      },
      500: {
        description: "Internal server error",
        content: {
          "application/json": {
            schema: GenericErrorSchema,
          },
        },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    try {
      // Get Firebase token for authentication
      // const firebaseToken = getFirebaseToken(c);
      // if (!firebaseToken) {
      //   return c.json({ error: "Unauthorized" }, 401);
      // }

      // const authenticatedUserId = firebaseToken.uid;

      // Parse and validate request parameters
      const { userId } = c.req.param();
      const query = c.req.query();
      const container = c.get("container");

      // Validate that user can only access their own webhook events
      // if (authenticatedUserId !== userId) {
      //   return c.json(
      //     { error: "Forbidden - You can only access your own webhook events" },
      //     403
      //   );
      // }

      // Parse query parameters
      const validatedQuery = WebhookEventQuerySchema.parse(query);

      // Get webhook event service
      const webhookEventService =
        container.resolve<WebhookEventService>(IWebhookEventService);

      // Retrieve user webhook events
      const result = await webhookEventService.getUserWebhookEvents(
        userId,
        validatedQuery
      );

      return c.json(result, 200);
    } catch (error: any) {
      console.error("Error retrieving user webhook events:", error);

      // Handle validation errors
      if (error.name === "ZodError") {
        return c.json(
          {
            error: "Invalid request parameters",
            details: error.errors,
          },
          400
        );
      }

      return c.json({ error: "Failed to retrieve webhook events" }, 500);
    }
  }
}

/**
 * Get Webhook Event by ID Endpoint
 *
 * This endpoint retrieves a specific webhook event by its ID.
 * Requires Firebase authentication and user can only access their own events.
 */
export class GetWebhookEventByIdEndpoint extends OpenAPIRoute {
  schema = {
    tags: ["Webhooks"],
    summary: "Get Webhook Event by ID",
    description: "Retrieve a specific webhook event by its ID",
    security: [{ BearerAuth: [] }],
    request: {
      params: z.object({
        id: z.string().uuid({ message: "Invalid webhook event ID format" }),
      }),
    },
    responses: {
      200: {
        description: "Webhook event retrieved successfully",
        content: {
          "application/json": {
            schema: WebhookEventResponseSchema,
          },
        },
      },
      401: {
        description: "Unauthorized - Invalid or missing authentication token",
        content: {
          "application/json": {
            schema: GenericErrorSchema,
          },
        },
      },
      403: {
        description:
          "Forbidden - User can only access their own webhook events",
        content: {
          "application/json": {
            schema: GenericErrorSchema,
          },
        },
      },
      404: {
        description: "Webhook event not found",
        content: {
          "application/json": {
            schema: NotFoundErrorSchema,
          },
        },
      },
      500: {
        description: "Internal server error",
        content: {
          "application/json": {
            schema: GenericErrorSchema,
          },
        },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    try {
      // Get Firebase token for authentication
      // const firebaseToken = getFirebaseToken(c);
      // if (!firebaseToken) {
      //   return c.json({ error: "Unauthorized" }, 401);
      // }

      // const authenticatedUserId = firebaseToken.uid;

      // Parse and validate request parameters
      const { id } = c.req.param();
      const container = c.get("container");

      // Get webhook event service
      const webhookEventService =
        container.resolve<WebhookEventService>(IWebhookEventService);

      // Retrieve webhook event
      const webhookEvent = await webhookEventService.getWebhookEventById(id);

      if (!webhookEvent) {
        return c.json({ error: "Webhook event not found" }, 404);
      }

      // Validate that user can only access their own webhook events
      // if (authenticatedUserId !== webhookEvent.appUserId) {
      //   return c.json(
      //     { error: "Forbidden - You can only access your own webhook events" },
      //     403
      //   );
      // }

      // Convert to response format
      const response = {
        ...webhookEvent,
        createdAt: webhookEvent.createdAt.toISOString(),
        processedAt: webhookEvent.processedAt?.toISOString(),
        lastAttemptAt: webhookEvent.lastAttemptAt?.toISOString(),
      };

      return c.json(response, 200);
    } catch (error: any) {
      console.error("Error retrieving webhook event:", error);

      return c.json({ error: "Failed to retrieve webhook event" }, 500);
    }
  }
}
