import { type Context } from "hono";
import { OpenAPIRoute } from "chanfana";

import type { HonoEnv } from "../../../types";
import {
  RevenueCatWebhookBodySchema,
  WebhookErrorSchema,
} from "../webhook.schema";
import { WebhookProcessingResponseSchema } from "../webhook-event.schema";
import { IWebhookEventService } from "../webhook-event.interface";
import type { WebhookEventService } from "../webhook-event.interface";
import { IWebhookHandlerRegistry } from "../handlers/webhook-handler.interface";
import type { WebhookHandlerRegistry } from "../handlers/webhook-handler.interface";

/**
 * RevenueCat Webhook Endpoint
 *
 * This endpoint receives webhook notifications from RevenueCat
 * and currently logs the webhook body for debugging purposes.
 *
 * RevenueCat webhooks are sent when subscription events occur,
 * such as purchases, renewals, cancellations, etc.
 */
export class RevenueCatWebhookEndpoint extends OpenAPIRoute {
  schema = {
    tags: ["Webhooks"],
    summary: "RevenueCat Webhook Handler",
    description: "Receives and processes webhook notifications from RevenueCat",
    request: {
      body: {
        content: {
          "application/json": {
            schema: RevenueCatWebhookBodySchema,
          },
        },
        required: true,
      },
    },
    responses: {
      200: {
        description: "Webhook processed successfully",
        content: {
          "application/json": {
            schema: WebhookProcessingResponseSchema,
          },
        },
      },
      400: {
        description: "Invalid webhook payload",
        content: {
          "application/json": {
            schema: WebhookErrorSchema,
          },
        },
      },
      500: {
        description: "Internal server error",
        content: {
          "application/json": {
            schema: WebhookErrorSchema,
          },
        },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    const timestamp = new Date().toISOString();

    try {
      // Get the raw request body
      const body = await c.req.json();

      // Log the webhook details for debugging
      // console.log("=== RevenueCat Webhook Received ===");
      // console.log("Timestamp:", timestamp);
      // console.log("Body:", JSON.stringify(body, null, 2));
      // console.log("=== End RevenueCat Webhook ===");

      // Validate the body structure (basic validation)
      const validatedBody = RevenueCatWebhookBodySchema.parse(body);

      // Extract RevenueCat webhook fields
      const eventId = validatedBody.event?.id;
      const eventType = validatedBody.event?.type;
      // app_user_id is inside the event object
      const appUserId = validatedBody.event?.app_user_id;

      if (!eventId || !eventType || !appUserId) {
        console.error("Missing required webhook fields:", {
          eventId,
          eventType,
          appUserId,
          eventObject: validatedBody.event,
        });
        return c.json(
          {
            success: false,
            message: "Missing required webhook fields",
            timestamp,
          },
          400
        );
      }

      console.log("RevenueCat Event Type:", eventType);
      console.log("RevenueCat Event ID:", eventId);
      console.log("App User ID:", appUserId);

      // Get container and webhook event service
      const container = c.get("container");
      const webhookEventService =
        container.resolve<WebhookEventService>(IWebhookEventService);

      // Check for idempotency - if event already exists, return success
      const existingEvent = await webhookEventService.getWebhookEventByEventId(
        eventId
      );
      if (existingEvent) {
        console.log(
          `[Webhook] Event ${eventId} already processed, returning success`
        );
        return c.json(
          {
            success: true,
            message: "Event already processed",
            eventId,
            timestamp,
          },
          200
        );
      }

      // Create webhook event record
      const webhookEvent = await webhookEventService.createWebhookEvent({
        eventId,
        eventType,
        appUserId,
        payload: validatedBody,
      });

      console.log(`[Webhook] Created webhook event record: ${webhookEvent.id}`);

      // Process webhook asynchronously using waitUntil
      c.executionCtx.waitUntil(
        this.processWebhookEvent(
          webhookEvent.id,
          webhookEventService,
          container
        )
      );

      // Return immediate success response
      return c.json(
        {
          success: true,
          message: "RevenueCat webhook received and queued for processing",
          eventId,
          timestamp,
        },
        200
      );
    } catch (error: any) {
      console.error("Error processing RevenueCat webhook:", error);
      console.error("Error timestamp:", timestamp);

      // Return error response
      return c.json(
        {
          success: false,
          message: "Failed to process RevenueCat webhook",
          timestamp,
        },
        500
      );
    }
  }

  /**
   * Process webhook event asynchronously
   * This method handles the actual business logic for the webhook
   */
  private async processWebhookEvent(
    webhookEventId: string,
    webhookEventService: WebhookEventService,
    container: any
  ): Promise<void> {
    try {
      console.log(`[Webhook] Starting processing for event: ${webhookEventId}`);

      // Mark as processing
      await webhookEventService.markAsProcessing(webhookEventId);

      // Get the webhook event details
      const webhookEvent = await webhookEventService.getWebhookEventById(
        webhookEventId
      );
      if (!webhookEvent) {
        throw new Error(`Webhook event ${webhookEventId} not found`);
      }

      // Process RevenueCat webhook event
      console.log(`[Webhook] Processing event type: ${webhookEvent.eventType}`);
      console.log(`[Webhook] Processing for user: ${webhookEvent.appUserId}`);

      // Get webhook handler registry
      const webhookHandlerRegistry = container.resolve(
        IWebhookHandlerRegistry
      ) as WebhookHandlerRegistry;

      // Get the appropriate handler for this event type
      const webhookHandler = webhookHandlerRegistry.getHandler(
        webhookEvent.eventType
      );

      if (!webhookHandler) {
        throw new Error(
          `No handler found for event type: ${webhookEvent.eventType}`
        );
      }

      // Process the webhook using the handler
      await webhookHandler.process(webhookEvent);

      // Mark as succeeded
      await webhookEventService.markAsSucceeded(webhookEventId);
      console.log(`[Webhook] Successfully processed event: ${webhookEventId}`);
    } catch (error: any) {
      console.error(
        `[Webhook] Failed to process event ${webhookEventId}:`,
        error
      );

      // Mark as failed with error message
      await webhookEventService.markAsFailed(
        webhookEventId,
        error.message || "Unknown error"
      );
    }
  }
}
