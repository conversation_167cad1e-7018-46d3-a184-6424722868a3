import { z } from "zod";

/**
 * Schema for RevenueCat webhook request body
 * This is a flexible schema that accepts any JSON structure
 * since RevenueCat webhook payloads can vary
 */
export const RevenueCatWebhookBodySchema = z.record(z.any()).describe("RevenueCat webhook payload");

/**
 * Schema for webhook response
 */
export const WebhookResponseSchema = z.object({
  success: z.boolean().describe("Whether the webhook was processed successfully"),
  message: z.string().describe("Response message"),
  timestamp: z.string().describe("Processing timestamp"),
});

/**
 * Schema for webhook error response
 */
export const WebhookErrorSchema = z.object({
  error: z.string().describe("Error message"),
  timestamp: z.string().describe("Error timestamp"),
});

export type RevenueCatWebhookBody = z.infer<typeof RevenueCatWebhookBodySchema>;
export type WebhookResponse = z.infer<typeof WebhookResponseSchema>;
export type WebhookError = z.infer<typeof WebhookErrorSchema>;
