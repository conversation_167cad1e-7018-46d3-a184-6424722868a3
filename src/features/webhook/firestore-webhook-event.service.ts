import { inject, singleton } from "tsyringe";
import { v4 as uuidv4 } from "uuid";
import type { CollectionReference, Query } from "firebase-rest-firestore";

import {
  IDbService,
  type DbService,
} from "../../infrastructure/db/db-service.interface";
import { WebhookEventService } from "./webhook-event.interface";
import type {
  WebhookEvent,
  WebhookEventStatus,
  CreateWebhookEventData,
  WebhookEventQuery,
  WebhookEventListResponse,
  PaginationMeta,
} from "./webhook-event.schema";

const COLLECTION_NAME = "webhook_events";

/**
 * Firestore implementation of WebhookEventService
 * Handles webhook event storage and retrieval using Firestore
 */
@singleton()
export class FirestoreWebhookEventService implements WebhookEventService {
  private webhookEventsCollection: CollectionReference;

  constructor(@inject(IDbService) dbService: DbService) {
    this.webhookEventsCollection = dbService
      .getFirestoreInstance()
      .collection(COLLECTION_NAME);
  }

  /**
   * Convert Firestore document data to WebhookEvent object
   * @param data - The raw data from Firestore document
   * @returns WebhookEvent object
   */
  private mapFirestoreDataToWebhookEvent(data: any): WebhookEvent {
    return {
      id: data?.id,
      eventId: data?.eventId,
      eventType: data?.eventType,
      appUserId: data?.appUserId,
      status: data?.status,
      createdAt: data?.createdAt,
      processedAt: data?.processedAt,
      payload: data?.payload,
      errorMessage: data?.errorMessage,
      processingAttempts: data?.processingAttempts || 0,
      lastAttemptAt: data?.lastAttemptAt,
    } as WebhookEvent;
  }

  async createWebhookEvent(
    eventData: CreateWebhookEventData
  ): Promise<WebhookEvent> {
    const id = uuidv4();
    const now = new Date();

    const webhookEvent: WebhookEvent = {
      id,
      ...eventData,
      status: eventData.status || "received",
      createdAt: now,
      processingAttempts: 0,
    };

    console.log(
      `[WebhookEvent] Creating webhook event ${id} for eventId ${eventData.eventId}`
    );

    try {
      await this.webhookEventsCollection.doc(id).update(webhookEvent);
      return webhookEvent;
    } catch (error) {
      console.error(`Failed to create webhook event ${id}:`, error);
      throw new Error("Failed to create webhook event.");
    }
  }

  async getWebhookEventByEventId(
    eventId: string
  ): Promise<WebhookEvent | null> {
    try {
      const snapshot = await this.webhookEventsCollection
        .where("eventId", "==", eventId)
        .limit(1)
        .get();

      if (snapshot.docs.length === 0) {
        return null;
      }

      const data = snapshot.docs[0].data();
      return this.mapFirestoreDataToWebhookEvent(data);
    } catch (error) {
      console.error(
        `Failed to retrieve webhook event by eventId ${eventId}:`,
        error
      );
      throw new Error("Failed to retrieve webhook event.");
    }
  }

  async getWebhookEventById(id: string): Promise<WebhookEvent | null> {
    try {
      const doc = await this.webhookEventsCollection.doc(id).get();

      if (!doc.exists) {
        return null;
      }

      const data = doc.data();
      return this.mapFirestoreDataToWebhookEvent(data);
    } catch (error) {
      console.error(`Failed to retrieve webhook event ${id}:`, error);
      throw new Error("Failed to retrieve webhook event.");
    }
  }

  async updateWebhookEventStatus(
    id: string,
    status: WebhookEventStatus,
    errorMessage?: string
  ): Promise<void> {
    try {
      const updateData: Partial<WebhookEvent> = {
        status,
        lastAttemptAt: new Date(),
      };

      // Set processedAt timestamp for final states
      if (status === "succeeded" || status === "failed") {
        updateData.processedAt = new Date();
      }

      // Add error message for failed status
      if (status === "failed" && errorMessage) {
        updateData.errorMessage = errorMessage;
      }

      await this.webhookEventsCollection.doc(id).update(updateData);
      console.log(
        `[WebhookEvent] Updated webhook event ${id} status to ${status}`
      );
    } catch (error) {
      console.error(`Failed to update webhook event ${id} status:`, error);
      throw new Error("Failed to update webhook event status.");
    }
  }

  async incrementProcessingAttempts(id: string): Promise<void> {
    try {
      const doc = await this.webhookEventsCollection.doc(id).get();
      if (!doc.exists) {
        throw new Error(`Webhook event ${id} not found`);
      }

      const currentAttempts = doc.data()?.processingAttempts || 0;
      await this.webhookEventsCollection.doc(id).update({
        processingAttempts: currentAttempts + 1,
        lastAttemptAt: new Date(),
      });

      console.log(
        `[WebhookEvent] Incremented processing attempts for ${id} to ${
          currentAttempts + 1
        }`
      );
    } catch (error) {
      console.error(
        `Failed to increment processing attempts for ${id}:`,
        error
      );
      throw new Error("Failed to increment processing attempts.");
    }
  }

  async getUserWebhookEvents(
    userId: string,
    query?: WebhookEventQuery
  ): Promise<WebhookEventListResponse> {
    try {
      const limit = query?.limit || 20;
      const page = query?.page || 1;
      const offset = (page - 1) * limit;

      // Build base query for counting
      let countQuery = this.webhookEventsCollection.where(
        "appUserId",
        "==",
        userId
      );
      let dataQuery = this.webhookEventsCollection.where(
        "appUserId",
        "==",
        userId
      );

      // Apply filters
      if (query?.status) {
        countQuery = countQuery.where("status", "==", query.status);
        dataQuery = dataQuery.where("status", "==", query.status);
      }

      if (query?.eventType) {
        countQuery = countQuery.where("eventType", "==", query.eventType);
        dataQuery = dataQuery.where("eventType", "==", query.eventType);
      }

      // Get total count
      const countSnapshot = await countQuery.get();
      const totalItems = countSnapshot.docs.length;

      // Get paginated data
      const dataSnapshot = await dataQuery
        .orderBy("createdAt", "desc")
        .offset(offset)
        .limit(limit)
        .get();

      const events = dataSnapshot.docs.map((doc) => {
        const data = doc.data();
        const webhookEvent = this.mapFirestoreDataToWebhookEvent(data);

        // Convert to response format with ISO strings
        return {
          ...webhookEvent,
          createdAt: webhookEvent.createdAt.toISOString(),
          processedAt: webhookEvent.processedAt?.toISOString(),
          lastAttemptAt: webhookEvent.lastAttemptAt?.toISOString(),
        };
      });

      // Calculate pagination metadata
      const totalPages = Math.ceil(totalItems / limit);
      const pagination: PaginationMeta = {
        currentPage: page,
        totalPages,
        totalItems,
        itemsPerPage: limit,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      };

      return { events, pagination };
    } catch (error) {
      console.error(
        `Failed to retrieve webhook events for user ${userId}:`,
        error
      );
      throw new Error("Failed to retrieve user webhook events.");
    }
  }

  async getWebhookEventsByStatus(
    status: WebhookEventStatus,
    query?: WebhookEventQuery
  ): Promise<WebhookEventListResponse> {
    // Implementation similar to getUserWebhookEvents but filtering by status
    // This is a simplified version - full implementation would be similar
    try {
      const limit = query?.limit || 20;
      const page = query?.page || 1;
      const offset = (page - 1) * limit;

      const countSnapshot = await this.webhookEventsCollection
        .where("status", "==", status)
        .get();
      const totalItems = countSnapshot.docs.length;

      const dataSnapshot = await this.webhookEventsCollection
        .where("status", "==", status)
        .orderBy("createdAt", "desc")
        .offset(offset)
        .limit(limit)
        .get();

      const events = dataSnapshot.docs.map((doc) => {
        const data = doc.data();
        const webhookEvent = this.mapFirestoreDataToWebhookEvent(data);

        return {
          ...webhookEvent,
          createdAt: webhookEvent.createdAt.toISOString(),
          processedAt: webhookEvent.processedAt?.toISOString(),
          lastAttemptAt: webhookEvent.lastAttemptAt?.toISOString(),
        };
      });

      const totalPages = Math.ceil(totalItems / limit);
      const pagination: PaginationMeta = {
        currentPage: page,
        totalPages,
        totalItems,
        itemsPerPage: limit,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      };

      return { events, pagination };
    } catch (error) {
      console.error(
        `Failed to retrieve webhook events by status ${status}:`,
        error
      );
      throw new Error("Failed to retrieve webhook events by status.");
    }
  }

  async getWebhookEventsByEventType(
    eventType: string,
    query?: WebhookEventQuery
  ): Promise<WebhookEventListResponse> {
    try {
      const limit = query?.limit || 20;
      const page = query?.page || 1;
      const offset = (page - 1) * limit;

      const countSnapshot = await this.webhookEventsCollection
        .where("eventType", "==", eventType)
        .get();
      const totalItems = countSnapshot.docs.length;

      const dataSnapshot = await this.webhookEventsCollection
        .where("eventType", "==", eventType)
        .orderBy("createdAt", "desc")
        .offset(offset)
        .limit(limit)
        .get();

      const events = dataSnapshot.docs.map((doc) => {
        const data = doc.data();
        const webhookEvent = this.mapFirestoreDataToWebhookEvent(data);

        return {
          ...webhookEvent,
          createdAt: webhookEvent.createdAt.toISOString(),
          processedAt: webhookEvent.processedAt?.toISOString(),
          lastAttemptAt: webhookEvent.lastAttemptAt?.toISOString(),
        };
      });

      const totalPages = Math.ceil(totalItems / limit);
      const pagination: PaginationMeta = {
        currentPage: page,
        totalPages,
        totalItems,
        itemsPerPage: limit,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      };

      return { events, pagination };
    } catch (error) {
      console.error(
        `Failed to retrieve webhook events by eventType ${eventType}:`,
        error
      );
      throw new Error("Failed to retrieve webhook events by event type.");
    }
  }

  async getAllWebhookEvents(
    query?: WebhookEventQuery
  ): Promise<WebhookEventListResponse> {
    try {
      const limit = query?.limit || 20;
      const page = query?.page || 1;
      const offset = (page - 1) * limit;

      // Build base query
      let countQuery: Query | CollectionReference =
        this.webhookEventsCollection;
      let dataQuery: Query | CollectionReference = this.webhookEventsCollection;

      // Apply filters
      if (query?.status) {
        countQuery = countQuery.where("status", "==", query.status);
        dataQuery = dataQuery.where("status", "==", query.status);
      }

      if (query?.eventType) {
        countQuery = countQuery.where("eventType", "==", query.eventType);
        dataQuery = dataQuery.where("eventType", "==", query.eventType);
      }

      // Get total count
      const countSnapshot = await countQuery.get();
      const totalItems = countSnapshot.docs.length;

      // Get paginated data
      const dataSnapshot = await dataQuery
        .orderBy("createdAt", "desc")
        .offset(offset)
        .limit(limit)
        .get();

      const events = dataSnapshot.docs.map((doc) => {
        const data = doc.data();
        const webhookEvent = this.mapFirestoreDataToWebhookEvent(data);

        return {
          ...webhookEvent,
          createdAt: webhookEvent.createdAt.toISOString(),
          processedAt: webhookEvent.processedAt?.toISOString(),
          lastAttemptAt: webhookEvent.lastAttemptAt?.toISOString(),
        };
      });

      const totalPages = Math.ceil(totalItems / limit);
      const pagination: PaginationMeta = {
        currentPage: page,
        totalPages,
        totalItems,
        itemsPerPage: limit,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      };

      return { events, pagination };
    } catch (error) {
      console.error(`Failed to retrieve all webhook events:`, error);
      throw new Error("Failed to retrieve webhook events.");
    }
  }

  async markAsProcessing(id: string): Promise<void> {
    await this.updateWebhookEventStatus(id, "processing");
  }

  async markAsSucceeded(id: string): Promise<void> {
    await this.updateWebhookEventStatus(id, "succeeded");
  }

  async markAsFailed(id: string, errorMessage: string): Promise<void> {
    await this.updateWebhookEventStatus(id, "failed", errorMessage);
  }

  async getRetryableWebhookEvents(
    maxAttempts: number,
    query?: WebhookEventQuery
  ): Promise<WebhookEvent[]> {
    try {
      const snapshot = await this.webhookEventsCollection
        .where("status", "==", "failed")
        .where("processingAttempts", "<", maxAttempts)
        .orderBy("lastAttemptAt", "asc")
        .limit(query?.limit || 50)
        .get();

      return snapshot.docs.map((doc) => {
        const data = doc.data();
        return this.mapFirestoreDataToWebhookEvent(data);
      });
    } catch (error) {
      console.error(`Failed to retrieve retryable webhook events:`, error);
      throw new Error("Failed to retrieve retryable webhook events.");
    }
  }
}
