import { singleton } from "tsyringe";
import type { UpdateUserSubscriptionData, UpdateUserCreditsData } from "../../user/user.schema";

/**
 * RevenueCat webhook payload transformer
 * Converts RevenueCat webhook data into standardized formats
 */
@singleton()
export class RevenueCatTransformer {
  
  /**
   * Extract subscription data from RevenueCat webhook payload
   */
  extractSubscriptionData(payload: any, eventType: string): UpdateUserSubscriptionData | null {
    const event = payload.event;
    if (!event) {
      console.warn("[RevenueCatTransformer] No event object found in payload");
      return null;
    }

    const now = new Date().toISOString();
    
    // Determine subscription status based on event type
    const status = this.mapEventTypeToSubscriptionStatus(eventType);
    if (!status) {
      console.log(`[RevenueCatTransformer] Event type ${eventType} does not affect subscription status`);
      return null;
    }

    // Extract entitlements
    const entitlements = event.entitlement_ids || [];
    
    // Extract expiration date (convert from milliseconds to ISO string)
    let expires_date: string | undefined;
    if (event.expiration_at_ms) {
      expires_date = new Date(event.expiration_at_ms).toISOString();
    }

    const subscriptionData: UpdateUserSubscriptionData = {
      status,
      entitlements,
      expires_date,
      last_updated_from_webhook: now,
    };

    console.log(`[RevenueCatTransformer] Extracted subscription data:`, subscriptionData);
    return subscriptionData;
  }

  /**
   * Extract credits data from RevenueCat webhook payload
   */
  extractCreditsData(payload: any, eventType: string): UpdateUserCreditsData | null {
    const event = payload.event;
    if (!event || !event.entitlement_ids) {
      console.warn("[RevenueCatTransformer] No event or entitlements found in payload");
      return null;
    }

    // Only update credits for purchase/renewal events
    if (!this.shouldUpdateCreditsForEventType(eventType)) {
      console.log(`[RevenueCatTransformer] Event type ${eventType} does not trigger credits update`);
      return null;
    }

    const entitlements = event.entitlement_ids;
    const credits = this.calculateCreditsFromEntitlements(entitlements);

    const creditsData: UpdateUserCreditsData = { credits };
    
    console.log(`[RevenueCatTransformer] Extracted credits data:`, creditsData);
    return creditsData;
  }

  /**
   * Map RevenueCat event type to subscription status
   */
  private mapEventTypeToSubscriptionStatus(eventType: string): "active" | "canceled" | "expired" | null {
    switch (eventType) {
      case "INITIAL_PURCHASE":
      case "RENEWAL":
      case "UNCANCELLATION":
        return "active";
      case "CANCELLATION":
        return "canceled";
      case "EXPIRATION":
        return "expired";
      default:
        return null;
    }
  }

  /**
   * Check if event type should trigger credits update
   */
  private shouldUpdateCreditsForEventType(eventType: string): boolean {
    return ["INITIAL_PURCHASE", "RENEWAL"].includes(eventType);
  }

  /**
   * Calculate credits based on entitlements
   */
  private calculateCreditsFromEntitlements(entitlements: string[]): number {
    // Define credit amounts based on subscription tiers
    if (entitlements.includes("gold_tier")) {
      return 100; // Gold tier gets 100 credits
    } else if (entitlements.includes("silver_tier")) {
      return 50;  // Silver tier gets 50 credits
    } else if (entitlements.includes("premium")) {
      return 75;  // Premium tier gets 75 credits
    } else {
      return 20;  // Default credits for basic subscriptions
    }
  }

  /**
   * Validate RevenueCat webhook payload structure
   */
  validatePayload(payload: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!payload) {
      errors.push("Payload is null or undefined");
      return { isValid: false, errors };
    }

    if (!payload.event) {
      errors.push("Missing 'event' object in payload");
    } else {
      const event = payload.event;
      
      if (!event.id) {
        errors.push("Missing 'event.id' field");
      }
      
      if (!event.type) {
        errors.push("Missing 'event.type' field");
      }
      
      if (!event.app_user_id) {
        errors.push("Missing 'event.app_user_id' field");
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Extract basic event information from payload
   */
  extractEventInfo(payload: any): { eventId: string; eventType: string; appUserId: string } | null {
    const validation = this.validatePayload(payload);
    if (!validation.isValid) {
      console.error("[RevenueCatTransformer] Invalid payload:", validation.errors);
      return null;
    }

    const event = payload.event;
    return {
      eventId: event.id,
      eventType: event.type,
      appUserId: event.app_user_id,
    };
  }
}
