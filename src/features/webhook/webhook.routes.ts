import { RevenueCatWebhookEndpoint } from "./endpoints/revenue-cat-webhook";
import {
  GetUserWebhookEventsEndpoint,
  GetWebhookEventByIdEndpoint,
} from "./endpoints/get-webhook-events";

/**
 * Register webhook-related routes
 *
 * This function registers all webhook endpoints with the OpenAPI instance.
 * Webhook endpoints typically don't require authentication as they come from
 * external services, but they may require signature verification for security.
 *
 * @param openapi - The OpenAPI Hono instance
 * @param routePrefix - The base path for webhook routes (e.g., "/api/v1/webhook")
 */
export function registerWebhookRoutes(
  openapi: any,
  routePrefix: string = "/webhook"
) {
  const base = routePrefix.startsWith("/") ? routePrefix : `/${routePrefix}`;

  // RevenueCat webhook endpoint (no authentication required)
  // POST /api/v1/webhook/revenue-cat
  openapi.post(`${base}/revenue-cat`, RevenueCatWebhookEndpoint);

  // Webhook event query endpoints (authentication required)
  // GET /api/v1/webhook/events/user/:userId
  // openapi.get(`${base}/events/user/:userId`, GetUserWebhookEventsEndpoint);

  // GET /api/v1/webhook/events/:id
  // openapi.get(`${base}/events/:id`, GetWebhookEventByIdEndpoint);

  // Future webhook endpoints can be added here:
  // openapi.post(`${base}/stripe`, StripeWebhookEndpoint);
  // openapi.post(`${base}/paypal`, PayPalWebhookEndpoint);
}
