import type { InjectionToken } from "tsyringe";
import type {
  <PERSON>hookEvent,
  WebhookEventStatus,
  CreateWebhookEventData,
  WebhookEventQuery,
  WebhookEventListResponse,
  PaginationMeta,
} from "./webhook-event.schema";

// Define the injection token for WebhookEventService
export const IWebhookEventService: InjectionToken<WebhookEventService> =
  Symbol("IWebhookEventService");

/**
 * Abstract class representing the contract for a webhook event service.
 * Handles webhook event storage, retrieval, and status management.
 */
export abstract class WebhookEventService {
  /**
   * Create a new webhook event record
   * @param eventData - The webhook event data to create
   * @returns Promise that resolves to the created WebhookEvent
   */
  abstract createWebhookEvent(eventData: CreateWebhookEventData): Promise<WebhookEvent>;

  /**
   * Get a webhook event by its RevenueCat event ID (for idempotency checks)
   * @param eventId - The RevenueCat event ID
   * @returns Promise that resolves to <PERSON>hookEvent or null if not found
   */
  abstract getWebhookEventByEventId(eventId: string): Promise<WebhookEvent | null>;

  /**
   * Get a webhook event by its database ID
   * @param id - The database ID of the webhook event
   * @returns Promise that resolves to WebhookEvent or null if not found
   */
  abstract getWebhookEventById(id: string): Promise<WebhookEvent | null>;

  /**
   * Update the status of a webhook event
   * @param id - The database ID of the webhook event
   * @param status - The new status
   * @param errorMessage - Optional error message (for failed status)
   * @returns Promise that resolves when update is complete
   */
  abstract updateWebhookEventStatus(
    id: string,
    status: WebhookEventStatus,
    errorMessage?: string
  ): Promise<void>;

  /**
   * Increment the processing attempts counter for a webhook event
   * @param id - The database ID of the webhook event
   * @returns Promise that resolves when update is complete
   */
  abstract incrementProcessingAttempts(id: string): Promise<void>;

  /**
   * Get webhook events for a specific user with pagination and filtering
   * @param userId - The user ID to filter by
   * @param query - Optional query parameters for filtering and pagination
   * @returns Promise that resolves to paginated webhook events
   */
  abstract getUserWebhookEvents(
    userId: string,
    query?: WebhookEventQuery
  ): Promise<WebhookEventListResponse>;

  /**
   * Get webhook events by status with pagination
   * @param status - The status to filter by
   * @param query - Optional query parameters for pagination
   * @returns Promise that resolves to paginated webhook events
   */
  abstract getWebhookEventsByStatus(
    status: WebhookEventStatus,
    query?: WebhookEventQuery
  ): Promise<WebhookEventListResponse>;

  /**
   * Get webhook events by event type with pagination
   * @param eventType - The event type to filter by
   * @param query - Optional query parameters for pagination
   * @returns Promise that resolves to paginated webhook events
   */
  abstract getWebhookEventsByEventType(
    eventType: string,
    query?: WebhookEventQuery
  ): Promise<WebhookEventListResponse>;

  /**
   * Get all webhook events with pagination and filtering
   * @param query - Optional query parameters for filtering and pagination
   * @returns Promise that resolves to paginated webhook events
   */
  abstract getAllWebhookEvents(
    query?: WebhookEventQuery
  ): Promise<WebhookEventListResponse>;

  /**
   * Mark a webhook event as processing
   * @param id - The database ID of the webhook event
   * @returns Promise that resolves when update is complete
   */
  abstract markAsProcessing(id: string): Promise<void>;

  /**
   * Mark a webhook event as succeeded and set processed timestamp
   * @param id - The database ID of the webhook event
   * @returns Promise that resolves when update is complete
   */
  abstract markAsSucceeded(id: string): Promise<void>;

  /**
   * Mark a webhook event as failed with error message and set processed timestamp
   * @param id - The database ID of the webhook event
   * @param errorMessage - The error message describing the failure
   * @returns Promise that resolves when update is complete
   */
  abstract markAsFailed(id: string, errorMessage: string): Promise<void>;

  /**
   * Get webhook events that need retry (failed events with attempts < max)
   * @param maxAttempts - Maximum number of retry attempts allowed
   * @param query - Optional query parameters for pagination
   * @returns Promise that resolves to webhook events that can be retried
   */
  abstract getRetryableWebhookEvents(
    maxAttempts: number,
    query?: WebhookEventQuery
  ): Promise<WebhookEvent[]>;
}
