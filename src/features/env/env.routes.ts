import { GetAllEnvEndpoint } from "./endpoints/get-all-env.handler";

/**
 * Registers the environment routes with the given Hono/Chanfana application instance.
 * @param openapi - The Hono application instance (used by Chanfana for route registration).
 * @param routePrefix - Optional prefix for the routes. Defaults to "/env".
 */
export function registerEnvRoutes(openapi: any, routePrefix: string = "/env") {
  const base = routePrefix.startsWith("/") ? routePrefix : `/${routePrefix}`;

  openapi.get(base, GetAllEnvEndpoint);
}
