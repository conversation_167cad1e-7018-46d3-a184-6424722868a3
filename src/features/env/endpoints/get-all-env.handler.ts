import type { Context } from "hono";
import { OpenAPIRoute } from "chanfana";
import { z } from "zod";
import type { CloudflareBindings, HonoEnv } from "../../../types";

import {
  IEnvService,
  EnvService,
} from "../../../infrastructure/env/env-service.interface";

const EnvResponseSchema = z.record(z.any()).openapi("EnvResponse");

const GenericErrorSchema = z
  .object({
    error: z.string(),
  })
  .openapi("GenericError");

export class GetAllEnvEndpoint extends OpenAPIRoute {
  schema = {
    summary: "Get all environment variables",
    description: "Retrieves all environment variables (for testing/debugging).",
    tags: ["Environment"],
    responses: {
      "200": {
        description: "A list of all environment variables.",
        content: {
          "application/json": {
            schema: EnvResponseSchema,
          },
        },
      },
      "500": {
        description: "Failed to retrieve environment variables.",
        content: { "application/json": { schema: GenericErrorSchema } },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    try {
      const requestContainer = c.get("container");
      const envService = requestContainer.resolve<EnvService>(IEnvService);
      const envVariables = envService.getAllVariables();
      return c.json(envVariables as CloudflareBindings);
    } catch (error: any) {
      console.error("Error retrieving environment variables:", error);
      return c.json({ error: "Failed to retrieve environment variables" }, 500);
    }
  }
}
