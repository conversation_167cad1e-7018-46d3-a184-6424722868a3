import { z } from "zod";

// Define the usecase type enum
export const UsecaseTypeSchema = z.enum([
  "image_only",
  "prompt_with_image",
  "multi_image",
  "prompt_only",
]);

export type UsecaseType = z.infer<typeof UsecaseTypeSchema>;

// Schema for a single Notion item (prompt entry)
export const NotionPromptItemSchema = z.object({
  id: z.string(),
  name: z.string().optional(),
  prompt: z.string().optional(),
  category: z.string().optional(),
  // category_zh: z.string().optional(),
  outputUrl: z.string().url().optional(),
  isDisabled: z.boolean().optional(),
  groupRank: z.number().optional(),
  inGroupRank: z.number().optional(),
  usecaseType: UsecaseTypeSchema.optional(),
  // cover_img_url: z.string().url().optional(),
});

export type NotionPromptItem = z.infer<typeof NotionPromptItemSchema>;

// Schema for the API response containing a list of prompt items
export const NotionPromptListResponseSchema = z.array(NotionPromptItemSchema);

export type NotionPromptListResponse = z.infer<
  typeof NotionPromptListResponseSchema
>;

// Schema for a single Video Usecase item
export const VideoUsecaseItemSchema = z.object({
  id: z.string(),
  name: z.string().optional(),
  prompt: z.string().optional(),
  template: z.string().optional(),
  detail: z.string().optional(),
  inputInstruction: z.string().optional(),
  videoUrl: z.string().url().optional(),
  scene: z.string().optional(),
  coverImgUrl: z.string().url().optional(),
  isDisabled: z.boolean().optional(),
  groupRank: z.number().optional(),
  inGroupRank: z.number().optional(),
  provider: z.string().optional(),
  category: z.string().optional(),
  isHot: z.boolean().optional(),
  isNew: z.boolean().optional(),
  imageCount: z.number().optional(),
});

export type VideoUsecaseItem = z.infer<typeof VideoUsecaseItemSchema>;

// Schema for the API response containing a list of video usecase items
export const VideoUsecaseListResponseSchema = z.array(VideoUsecaseItemSchema);

export type VideoUsecaseListResponse = z.infer<
  typeof VideoUsecaseListResponseSchema
>;

export const GenericErrorSchema = z.object({
  error: z.string(),
});
