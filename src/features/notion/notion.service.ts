import { Client } from "@notionhq/client";

import type {
  DatabaseObjectResponse,
  PageObjectResponse,
  PartialDatabaseObjectResponse,
  PartialPageObjectResponse,
} from "@notionhq/client";
import { singleton, inject } from "tsyringe";
import type {
  NotionPromptItem,
  UsecaseType,
  VideoUsecaseItem,
} from "./notion.schema";
import {
  ICacheService,
  CacheService,
} from "../../infrastructure/cache/cache-service.interface";
import {
  NotionService as NotionServiceInterface,
  type UsecaseLocale,
} from "./notion.interface";
import {
  EnvService,
  IEnvService,
} from "../../infrastructure/env/env-service.interface";

const NOTION_CACHE_KEY_PREFIX = "notion";
const NOTION_CACHE_TTL_MS = 10 * 60 * 1000; // 10 minutes

@singleton()
export class NotionService implements NotionServiceInterface {
  private notion: Client;
  private databaseId: string;
  private videoUsecaseDatabaseId: string;

  constructor(
    @inject(ICacheService) private cacheService: CacheService,
    @inject(IEnvService) envService: EnvService
  ) {
    const env = envService.getBindings();

    if (!env.NOTION_AUTH_TOKEN) {
      throw new Error(
        "NOTION_AUTH_TOKEN is not defined in environment variables"
      );
    }
    if (!env.NOTION_DATABASE_ID) {
      throw new Error(
        "NOTION_DATABASE_ID is not defined in environment variables"
      );
    }
    if (!env.NOTION_VIDEO_USECASE_DATABASE_ID) {
      throw new Error(
        "NOTION_VIDEO_USECASE_DATABASE_ID is not defined in environment variables"
      );
    }

    this.notion = new Client({
      auth: env.NOTION_AUTH_TOKEN,
      fetch: (url, init) => fetch(url, init), // Explicitly use an arrow function wrapper
    });
    this.databaseId = env.NOTION_DATABASE_ID;
    this.videoUsecaseDatabaseId = env.NOTION_VIDEO_USECASE_DATABASE_ID;

    console.log("NotionService initialized"); // For debugging, can be removed later
  }

  // Use the more specific type from QueryDatabaseResponse for results with pagination support
  async queryDatabase(
    useCache = true
  ): Promise<
    (
      | PageObjectResponse
      | PartialPageObjectResponse
      | PartialDatabaseObjectResponse
      | DatabaseObjectResponse
    )[]
  > {
    const cacheKey = `${NOTION_CACHE_KEY_PREFIX}:database:${this.databaseId}`;
    const cachedData = await this.cacheService.get<
      (
        | PageObjectResponse
        | PartialPageObjectResponse
        | PartialDatabaseObjectResponse
        | DatabaseObjectResponse
      )[]
    >(cacheKey);

    if (cachedData && useCache) {
      console.log(`Cache hit for Notion database query: ${cacheKey}`);
      return cachedData;
    }

    console.log(
      `Cache miss for Notion database query: ${cacheKey}. Fetching from API with pagination support.`
    );

    try {
      const allResults = [];
      let cursor: string | undefined;
      let pageCount = 0;

      do {
        console.log(
          `Fetching database page ${Math.floor(pageCount / 100) + 1}...`
        );

        const response = await this.notion.databases.query({
          database_id: this.databaseId,
          start_cursor: cursor,
          page_size: 100, // Maximum page size for Notion API
        });

        allResults.push(...response.results);
        pageCount += response.results.length;
        cursor = response.next_cursor || undefined;

        console.log(
          `Retrieved ${response.results.length} database items, total: ${pageCount}`
        );

        // Add delay to avoid API rate limits
        if (cursor) {
          await new Promise((resolve) => setTimeout(resolve, 200));
        }
      } while (cursor);

      console.log(`Total database items retrieved: ${allResults.length}`);

      await this.cacheService.set(cacheKey, allResults, NOTION_CACHE_TTL_MS);
      return allResults;
    } catch (error) {
      console.error("Error querying Notion database:", error);
      throw error;
    }
  }

  // Query video usecase database with pagination support
  async queryVideoUsecaseDatabase(
    useCache = true
  ): Promise<
    (
      | PageObjectResponse
      | PartialPageObjectResponse
      | PartialDatabaseObjectResponse
      | DatabaseObjectResponse
    )[]
  > {
    const cacheKey = `${NOTION_CACHE_KEY_PREFIX}:video-usecase:${this.videoUsecaseDatabaseId}`;
    const cachedData = await this.cacheService.get<
      (
        | PageObjectResponse
        | PartialPageObjectResponse
        | PartialDatabaseObjectResponse
        | DatabaseObjectResponse
      )[]
    >(cacheKey);

    if (cachedData && useCache) {
      console.log(`Cache hit for Notion video usecase query: ${cacheKey}`);
      return cachedData;
    }

    console.log(
      `Cache miss for Notion video usecase query: ${cacheKey}. Fetching from API with pagination support.`
    );

    try {
      const allResults = [];
      let cursor: string | undefined;
      let pageCount = 0;

      do {
        console.log(
          `Fetching video usecase page ${Math.floor(pageCount / 100) + 1}...`
        );

        const response = await this.notion.databases.query({
          database_id: this.videoUsecaseDatabaseId,
          start_cursor: cursor,
          page_size: 100, 
        });

        allResults.push(...response.results);
        pageCount += response.results.length;
        cursor = response.next_cursor || undefined;

        console.log(
          `Retrieved ${response.results.length} video usecase items, total: ${pageCount}`
        );

        // Add delay to avoid API rate limits
        if (cursor) {
          await new Promise((resolve) => setTimeout(resolve, 200));
        }
      } while (cursor);

      console.log(`Total video usecase items retrieved: ${allResults.length}`);

      await this.cacheService.set(cacheKey, allResults, NOTION_CACHE_TTL_MS);
      return allResults;
    } catch (error) {
      console.error("Error querying Notion video usecase database:", error);
      throw error;
    }
  }

  private _transformPageToPromptItem(
    page: PageObjectResponse,
    locale: UsecaseLocale
  ): NotionPromptItem | null {
    const props = page.properties;
    const name = NotionService._extractTitle(props["name"]);
    if (!name) {
      // If no name, item is considered invalid for prompt list
      return null;
    }

    const coverImgUrl = NotionService._extractFileUrl(props["cover_img_url"]);
    const groupRank = NotionService._extractRollupNumber(props["group_rank"]);
    const inGroupRank = NotionService._extractNumber(props["in_group_rank"]);

    let newCategory = NotionService._extractRollupString(props["new_category"]);
    const categoryZh = NotionService._extractRollupString(props["category_zh"]);

    const prompt = NotionService._extractRichText(props["prompt"]);
    const isDisabledString = NotionService._extractSelectName(
      props["is_disabled"]
    );
    const isDisabled = isDisabledString === "true";

    const usecaseType = NotionService._extractUsecaseType(
      props["usecase_type"]
    );

    if (locale === "zh-Hans" && categoryZh !== undefined) {
      newCategory = categoryZh;
    } else if (locale === "zh-Hans" && categoryZh === undefined) {
      // If locale is zh-Hans but no specific Chinese category, newCategory could be undefined or the default English one.
      // Depending on desired behavior, newCategory might remain as is (potentially English) or be explicitly nulled if a Chinese version is strictly required.
      // Current logic: if categoryZh is undefined, newCategory (potentially English or undefined) is used.
    }

    // if (page.id === "1f662e26-c437-804f-ae55-e3740f199a0d") {
    //   console.log(props);
    // }

    return {
      id: page.id,
      name,
      outputUrl: coverImgUrl,
      groupRank: groupRank,
      inGroupRank: inGroupRank,
      category: newCategory,
      isDisabled: isDisabled,
      prompt,
      usecaseType: usecaseType,
    };
  }

  async getPromptList(
    locale: UsecaseLocale = "en"
  ): Promise<NotionPromptItem[]> {
    const results = await this.queryDatabase();

    const pageObjects = results.filter(
      (item): item is PageObjectResponse =>
        item.object === "page" &&
        "properties" in item &&
        item.properties !== null
    );

    return pageObjects
      .map((page) => this._transformPageToPromptItem(page, locale))
      .filter(
        (item): item is NotionPromptItem => item !== null && !item.isDisabled
      )
      .sort(NotionService._sortByRank);
  }

  async getGhibliNotionTableEntryById(
    id: string
  ): Promise<NotionPromptItem | undefined> {
    // This method will benefit from the refactored getPromptList
    const results = await this.getPromptList(); // Assuming default locale or pass locale if needed
    return results.find((item) => item.id === id);
  }

  private _transformPageToVideoUsecaseItem(
    page: PageObjectResponse
  ): VideoUsecaseItem | null {
    const props = page.properties;
    const name = NotionService._extractTitle(props["name"]);

    if (!name) {
      // If no name, item is considered invalid
      return null;
    }

    const prompt = NotionService._extractRichText(props["prompt"]) || "";
    const template = NotionService._extractSelectName(props["urlType"]); // urlType field contains template info
    const detail = NotionService._extractMultiSelectNames(props["detail_info"]);
    const inputInstruction = NotionService._extractRichText(
      props["input_instruction"]
    );

    const videoUrl = NotionService._extractUrl(props["videoUrl"]); // videoUrl field
    const scene = NotionService._extractRichText(props["scene"]);
    const coverImgUrl = NotionService._extractFileUrl(props["posterUrl"]); // posterUrl field
    const groupRank = NotionService._extractRollupNumber(props["group_rank"]);
    const inGroupRank = NotionService._extractNumber(props["in_group_rank"]);

    // Additional fields from the log
    const provider = NotionService._extractSelectName(props["provider"]);
    const category = NotionService._extractRollupString(props["category"]);
    const isHot = NotionService._extractCheckbox(props["isHot"]);
    const isNew = NotionService._extractCheckbox(props["isNew"]);
    const name_en = NotionService._extractRichText(props["name_en"]);
    const input_instruction_en = NotionService._extractRichText(
      props["input_instruction_en"]
    );
    const detail_info_en = NotionService._extractRichText(
      props["detail_info_en"]
    );
    const imageCount = NotionService._extractNumber(props["image_count"]);
    // const id_number = NotionService._extractNumber(props["id"]);

    const isDisabled = NotionService._extractSelectName(props["is_disabled"]);


    return {
      id: page.id,
      name: name_en,
      prompt,
      template: scene,
      inputInstruction: input_instruction_en,
      detail: detail_info_en,
      videoUrl,
      coverImgUrl,
      groupRank,
      inGroupRank,
      isDisabled: isDisabled === "true",
      provider,
      category,
      isHot,
      isNew,
      imageCount,
    };
  }

  async getVideoUsecaseList(): Promise<VideoUsecaseItem[]> {
    const results = await this.queryVideoUsecaseDatabase();

    const pageObjects = results.filter(
      (item): item is PageObjectResponse =>
        item.object === "page" &&
        "properties" in item &&
        item.properties !== null
    );


    return pageObjects
      .map((page) => this._transformPageToVideoUsecaseItem(page))
      .filter(
        (item): item is VideoUsecaseItem => item !== null && !item.isDisabled
      )
      .sort(NotionService._sortByRank);
  }

  async getVideoUsecaseById(id: string): Promise<VideoUsecaseItem | undefined> {
    const results = await this.getVideoUsecaseList();
    return results.find((item) => item.id === id);
  }

  // --- Static Helper Methods for Property Extraction ---

  private static _sortByRank<
    T extends { groupRank?: number; inGroupRank?: number }
  >(a: T, b: T): number {
    const aGroupRank = a.groupRank;
    const bGroupRank = b.groupRank;
    const aInGroupRank = a.inGroupRank;
    const bInGroupRank = b.inGroupRank;

    // Compare groupRank
    if (aGroupRank === bGroupRank) {
      // groupRanks are equal (or both undefined), compare inGroupRank
      if (aInGroupRank === bInGroupRank) return 0;
      if (aInGroupRank === undefined) return 1;
      if (bInGroupRank === undefined) return -1;
      return aInGroupRank - bInGroupRank;
    }
    // groupRanks are different
    if (aGroupRank === undefined) return 1;
    if (bGroupRank === undefined) return -1;
    return aGroupRank - bGroupRank;
  }

  private static _extractTitle(prop: any): string | undefined {
    if (prop?.type === "title" && prop.title[0]?.plain_text) {
      return prop.title[0].plain_text;
    }
    return undefined;
  }

  private static _extractRichText(prop: any): string | undefined {
    if (prop?.type === "rich_text" && prop.rich_text[0]?.plain_text) {
      return prop.rich_text[0].plain_text;
    }
    return undefined;
  }

  private static _extractFileUrl(prop: any): string | undefined {
    if (prop?.type === "files" && prop.files[0]) {
      const file = prop.files[0];
      if (file.type === "file") return file.file.url;
      if (file.type === "external") return file.external.url;
    }
    return undefined;
  }

  private static _extractNumber(prop: any): number | undefined {
    if (prop?.type === "number" && typeof prop.number === "number") {
      return prop.number;
    }
    return undefined;
  }

  private static _extractSelectName(prop: any): string | undefined {
    if (prop?.type === "select" && prop.select?.name) {
      return prop.select.name;
    }
    return undefined;
  }

  private static _extractUsecaseType(prop: any): UsecaseType | undefined {
    const selectValue = NotionService._extractSelectName(prop);
    if (
      selectValue &&
      (selectValue === "image_only" ||
        selectValue === "prompt_with_image" ||
        selectValue === "multi_image" ||
        selectValue === "prompt_only")
    ) {
      return selectValue as UsecaseType;
    }
    return undefined;
  }

  private static _extractRollupNumber(prop: any): number | undefined {
    if (
      prop?.type === "rollup" &&
      prop.rollup.type === "array" &&
      prop.rollup.array[0]
    ) {
      const item = prop.rollup.array[0];
      if (item?.type === "number" && typeof item.number === "number") {
        return item.number;
      }
    }
    return undefined;
  }

  private static _extractRollupString(prop: any): string | undefined {
    if (
      prop?.type === "rollup" &&
      prop.rollup.type === "array" &&
      prop.rollup.array[0]
    ) {
      const item = prop.rollup.array[0];
      if (item?.type === "title" && item.title[0]?.plain_text) {
        return item.title[0].plain_text;
      }
      if (item?.type === "rich_text" && item.rich_text[0]?.plain_text) {
        return item.rich_text[0].plain_text;
      }
    }
    return undefined;
  }

  private static _extractUrl(prop: any): string | undefined {
    if (prop?.type === "url" && prop.url) {
      return prop.url;
    }
    return undefined;
  }

  private static _extractCheckbox(prop: any): boolean | undefined {
    if (prop?.type === "checkbox") {
      return prop.checkbox;
    }
    return undefined;
  }

  private static _extractMultiSelectNames(prop: any): string | undefined {
    if (prop?.type === "multi_select" && prop.multi_select?.length > 0) {
      return prop.multi_select.map((item: any) => item.name).join(", ");
    }
    return undefined;
  }
}
