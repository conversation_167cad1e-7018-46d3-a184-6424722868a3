import type { InjectionToken } from "tsyringe";
import type {
  DatabaseObjectResponse,
  PageObjectResponse,
  PartialDatabaseObjectResponse,
  PartialPageObjectResponse,
} from "@notionhq/client";
import type { NotionPromptItem, VideoUsecaseItem } from "./notion.schema";

export type UsecaseLocale = "en" | "zh-Hans";

// Define the injection token for NotionService
export const INotionService: InjectionToken<NotionService> =
  Symbol("INotionService");

/**
 * Abstract class representing the contract for a Notion service.
 * It should define methods for interacting with the Notion API.
 */
export abstract class NotionService {
  abstract queryDatabase(
    useCache?: boolean
  ): Promise<
    (
      | PageObjectResponse
      | PartialPageObjectResponse
      | PartialDatabaseObjectResponse
      | DatabaseObjectResponse
    )[]
  >;
  abstract queryVideoUsecaseDatabase(
    useCache?: boolean
  ): Promise<
    (
      | PageObjectResponse
      | PartialPageObjectResponse
      | PartialDatabaseObjectResponse
      | DatabaseObjectResponse
    )[]
  >;
  abstract getPromptList(locale?: UsecaseLocale): Promise<NotionPromptItem[]>;
  abstract getGhibliNotionTableEntryById(
    id: string
  ): Promise<NotionPromptItem | undefined>;
  abstract getVideoUsecaseList(): Promise<VideoUsecaseItem[]>;
  abstract getVideoUsecaseById(
    id: string
  ): Promise<VideoUsecaseItem | undefined>;
}
