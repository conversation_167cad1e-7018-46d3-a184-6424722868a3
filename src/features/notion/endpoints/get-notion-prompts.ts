import type { Context } from "hono";

import { OpenAPIRoute } from "chanfana";
import { z } from "zod";

import {
  GenericErrorSchema,
  NotionPromptListResponseSchema,
} from "../notion.schema";

import {
  INotionService,
  NotionService,
  type UsecaseLocale,
} from "../notion.interface";
import { HonoEnv } from "../../../types";

export class GetNotionPromptsEndpoint extends OpenAPIRoute {
  schema = {
    summary: "Get all Ghibli Notion table entries",
    description:
      "Retrieves a list of all prompts from the configured Notion database.",
    tags: ["CMS"],
    request: {
      headers: z.object({
        locale: z.string().optional().openapi({
          description:
            "The locale to retrieve prompts for (e.g., 'en', 'zh-Hans'). Optional.",
          example: "en-US",
        }),
        "X-Custom-Header": z.string().optional().openapi({
          description: "An example custom header for demonstration. Optional.",
          example: "custom-value-123",
        }),
      }),
    },

    responses: {
      "200": {
        description:
          "A list of Ghibli Notion table entries retrieved successfully",
        content: {
          "application/json": {
            schema: NotionPromptListResponseSchema,
          },
        },
      },
      "500": {
        description: "Failed to retrieve Ghibli Notion table entries",
        content: { "application/json": { schema: GenericErrorSchema } }, // Use the placeholder or a proper one
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    const requestContainer = c.get("container");
    const notionService =
      requestContainer.resolve<NotionService>(INotionService);
    const locale = c.req.header("locale") as UsecaseLocale;
    try {
      const prompts = await notionService.getPromptList(locale);
      // Data is already shaped by the service, directly return
      return c.json(prompts);
    } catch (error: any) {
      console.error("Error in GetNotionPromptsEndpoint:", error);
      // Ensure the error response matches the schema
      return c.json(
        {
          error: "Failed to retrieve Ghibli Notion prompts",
          message: error.message,
        },
        500
      );
    }
  }
}
