import type { Context } from "hono";

import { OpenAPIRoute } from "chanfana";
import { z } from "zod";

import {
  GenericErrorSchema,
  VideoUsecaseListResponseSchema,
} from "../notion.schema";

import { INotionService, NotionService } from "../notion.interface";
import { HonoEnv } from "../../../types";

export class GetVideoUsecaseEndpoint extends OpenAPIRoute {
  schema = {
    summary: "Get all Video Usecase CMS entries",
    description:
      "Retrieves a list of all video usecase entries from the configured Notion database.",
    tags: ["CMS"],
    request: {
      headers: z.object({
        locale: z.string().optional().openapi({
          description:
            "The locale to retrieve entries for (e.g., 'en', 'zh-Hans'). Optional.",
          example: "en-US",
        }),
        "X-Custom-Header": z.string().optional().openapi({
          description: "An example custom header for demonstration. Optional.",
          example: "custom-value-123",
        }),
      }),
    },

    responses: {
      "200": {
        description:
          "A list of Video Usecase CMS entries retrieved successfully",
        content: {
          "application/json": {
            schema: VideoUsecaseListResponseSchema,
          },
        },
      },
      "500": {
        description: "Failed to retrieve Video Usecase CMS entries",
        content: { "application/json": { schema: GenericErrorSchema } },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    const requestContainer = c.get("container");
    const notionService =
      requestContainer.resolve<NotionService>(INotionService);
    try {
      const videoUsecases = await notionService.getVideoUsecaseList();
      return c.json(videoUsecases);
    } catch (error: any) {
      console.error("Error in GetVideoUsecaseEndpoint:", error);
      // Ensure the error response matches the schema
      return c.json(
        {
          error: "Failed to retrieve Video Usecase entries",
          message: error.message,
        },
        500
      );
    }
  }
}
