// import { Hono } from "hono";
// import { container } from "tsyringe";
// import { zValidator } from "@hono/zod-validator";
// import { ChatService } from "./chat.service";
// import {
//   chatIdParamSchema,
//   userIdParamSchema,
//   createChatSchema,
//   updateChatSchema,
//   getChatsQuerySchema,
// } from "./chat.schema";
// import {
//   messageIdParamSchema,
//   createMessageSchema,
//   updateMessageSchema,
//   getMessagesQuerySchema,
// } from "./message.schema";

// const chatRoutes = new Hono();

// // --- Chat Routes ---

// // GET /users/:userId/chats - Get all chats for a user
// chatRoutes.get(
//   "/users/:userId/chats",
//   zValidator("param", userIdParamSchema, (result, c) => {
//     if (!result.success) {
//       return c.json({ success: false, message: "Invalid User ID format" }, 400);
//     }
//   }),
//   zValidator("query", getChatsQuerySchema, (result, c) => {
//     if (!result.success) {
//       return c.json(
//         {
//           success: false,
//           message: "Invalid query parameters",
//           errors: result.error.flatten().fieldErrors,
//         },
//         400
//       );
//     }
//   }),
//   async (c) => {
//     try {
//       const { userId } = c.req.valid("param");
//       const queryParams = c.req.valid("query");
//       const chatService = container.resolve(ChatService);
//       const chats = await chatService.getAllChats(userId /*, queryParams */);
//       return c.json(chats);
//     } catch (error: any) {
//       const { userId } = c.req.valid("param");
//       console.error(`Error getting chats for user ${userId}:`, error);
//       return c.json({ error: "Failed to get chats" }, 500);
//     }
//   }
// );

// // POST /chats - Create new chat
// chatRoutes.post(
//   "/chats",
//   zValidator("json", createChatSchema, (result, c) => {
//     if (!result.success) {
//       return c.json(
//         {
//           success: false,
//           message: "Validation failed",
//           errors: result.error.flatten().fieldErrors,
//         },
//         400
//       );
//     }
//   }),
//   async (c) => {
//     try {
//       const validatedData = c.req.valid("json");
//       const chatService = container.resolve(ChatService);
//       const chat = await chatService.createChat(validatedData);
//       return c.json(chat, 201);
//     } catch (error: any) {
//       console.error("Error creating chat:", error);
//       return c.json({ error: "Failed to create chat" }, 500);
//     }
//   }
// );

// // GET /chats/:chatId - Get single chat by ID
// chatRoutes.get(
//   "/chats/:chatId",
//   zValidator("param", chatIdParamSchema, (result, c) => {
//     if (!result.success) {
//       return c.json({ success: false, message: "Invalid Chat ID format" }, 400);
//     }
//   }),
//   async (c) => {
//     try {
//       const { chatId } = c.req.valid("param");
//       const chatService = container.resolve(ChatService);
//       const chat = await chatService.getChatById(chatId);
//       if (!chat) {
//         return c.json({ error: "Chat not found" }, 404);
//       }
//       return c.json(chat);
//     } catch (error: any) {
//       const { chatId } = c.req.valid("param");
//       console.error(`Error getting chat ${chatId}:`, error);
//       return c.json({ error: "Failed to get chat" }, 500);
//     }
//   }
// );

// // PUT /chats/:chatId - Update chat
// chatRoutes.put(
//   "/chats/:chatId",
//   zValidator("param", chatIdParamSchema, (result, c) => {
//     if (!result.success) {
//       return c.json({ success: false, message: "Invalid Chat ID format" }, 400);
//     }
//   }),
//   zValidator("json", updateChatSchema, (result, c) => {
//     if (!result.success) {
//       return c.json(
//         {
//           success: false,
//           message: "Validation failed",
//           errors: result.error.flatten().fieldErrors,
//         },
//         400
//       );
//     }
//   }),
//   async (c) => {
//     try {
//       const { chatId } = c.req.valid("param");
//       const validatedData = c.req.valid("json");
//       const chatService = container.resolve(ChatService);
//       const success = await chatService.updateChat(chatId, validatedData);
//       if (!success) {
//         return c.json({ error: "Chat not found" }, 404);
//       }
//       return c.json({ message: "Chat updated successfully" });
//     } catch (error: any) {
//       const { chatId } = c.req.valid("param");
//       console.error(`Error updating chat ${chatId}:`, error);
//       return c.json({ error: "Failed to update chat" }, 500);
//     }
//   }
// );

// // DELETE /chats/:chatId - Delete chat
// chatRoutes.delete(
//   "/chats/:chatId",
//   zValidator("param", chatIdParamSchema, (result, c) => {
//     if (!result.success) {
//       return c.json({ success: false, message: "Invalid Chat ID format" }, 400);
//     }
//   }),
//   async (c) => {
//     try {
//       const { chatId } = c.req.valid("param");
//       const chatService = container.resolve(ChatService);
//       const success = await chatService.deleteChat(chatId);
//       if (!success) {
//         return c.json({ error: "Chat not found" }, 404);
//       }
//       return c.json({ message: "Chat deleted successfully" });
//     } catch (error: any) {
//       const { chatId } = c.req.valid("param");
//       console.error(`Error deleting chat ${chatId}:`, error);
//       return c.json({ error: "Failed to delete chat" }, 500);
//     }
//   }
// );

// // --- Message Routes ---

// // GET /chats/:chatId/messages - Get all messages for a chat
// chatRoutes.get(
//   "/chats/:chatId/messages",
//   zValidator("param", chatIdParamSchema, (result, c) => {
//     if (!result.success) {
//       return c.json({ success: false, message: "Invalid Chat ID format" }, 400);
//     }
//   }),
//   zValidator("query", getMessagesQuerySchema, (result, c) => {
//     if (!result.success) {
//       return c.json(
//         {
//           success: false,
//           message: "Invalid query parameters",
//           errors: result.error.flatten().fieldErrors,
//         },
//         400
//       );
//     }
//   }),
//   async (c) => {
//     try {
//       const { chatId } = c.req.valid("param");
//       const queryParams = c.req.valid("query");
//       const chatService = container.resolve(ChatService);
//       const messages = await chatService.getAllMessages(
//         chatId /*, queryParams */
//       );
//       return c.json(messages);
//     } catch (error: any) {
//       const { chatId } = c.req.valid("param");
//       console.error(`Error getting messages for chat ${chatId}:`, error);
//       return c.json({ error: "Failed to get messages" }, 500);
//     }
//   }
// );

// // POST /chats/:chatId/messages - Create new message
// chatRoutes.post(
//   "/chats/:chatId/messages",
//   zValidator("param", chatIdParamSchema, (result, c) => {
//     if (!result.success) {
//       return c.json({ success: false, message: "Invalid Chat ID format" }, 400);
//     }
//   }),
//   zValidator("json", createMessageSchema, (result, c) => {
//     if (!result.success) {
//       return c.json(
//         {
//           success: false,
//           message: "Validation failed",
//           errors: result.error.flatten().fieldErrors,
//         },
//         400
//       );
//     }
//   }),
//   async (c) => {
//     try {
//       const { chatId } = c.req.valid("param");
//       const validatedData = c.req.valid("json");
//       const chatService = container.resolve(ChatService);
//       const message = await chatService.createMessage(chatId, validatedData);
//       return c.json(message, 201);
//     } catch (error: any) {
//       const { chatId } = c.req.valid("param");
//       console.error(`Error creating message for chat ${chatId}:`, error);
//       if (error.message === "Chat not found") {
//         return c.json({ error: "Chat not found" }, 404);
//       }
//       return c.json({ error: "Failed to create message" }, 500);
//     }
//   }
// );

// // GET /chats/:chatId/messages/:messageId - Get single message by ID
// chatRoutes.get(
//   "/chats/:chatId/messages/:messageId",
//   zValidator(
//     "param",
//     chatIdParamSchema.merge(messageIdParamSchema),
//     (result, c) => {
//       if (!result.success) {
//         return c.json(
//           { success: false, message: "Invalid Chat or Message ID format" },
//           400
//         );
//       }
//     }
//   ),
//   async (c) => {
//     try {
//       const { chatId, messageId } = c.req.valid("param");
//       const chatService = container.resolve(ChatService);
//       const message = await chatService.getMessageById(chatId, messageId);
//       if (!message) {
//         return c.json({ error: "Message not found" }, 404);
//       }
//       return c.json(message);
//     } catch (error: any) {
//       const { chatId, messageId } = c.req.valid("param");
//       console.error(
//         `Error getting message ${messageId} for chat ${chatId}:`,
//         error
//       );
//       return c.json({ error: "Failed to get message" }, 500);
//     }
//   }
// );

// // PUT /chats/:chatId/messages/:messageId - Update message
// chatRoutes.put(
//   "/chats/:chatId/messages/:messageId",
//   zValidator(
//     "param",
//     chatIdParamSchema.merge(messageIdParamSchema),
//     (result, c) => {
//       if (!result.success) {
//         return c.json(
//           { success: false, message: "Invalid Chat or Message ID format" },
//           400
//         );
//       }
//     }
//   ),
//   zValidator("json", updateMessageSchema, (result, c) => {
//     if (!result.success) {
//       return c.json(
//         {
//           success: false,
//           message: "Validation failed",
//           errors: result.error.flatten().fieldErrors,
//         },
//         400
//       );
//     }
//   }),
//   async (c) => {
//     try {
//       const { chatId, messageId } = c.req.valid("param");
//       const validatedData = c.req.valid("json");
//       const chatService = container.resolve(ChatService);
//       const success = await chatService.updateMessage(
//         chatId,
//         messageId,
//         validatedData
//       );
//       if (!success) {
//         return c.json({ error: "Message not found" }, 404);
//       }
//       return c.json({ message: "Message updated successfully" });
//     } catch (error: any) {
//       const { chatId, messageId } = c.req.valid("param");
//       console.error(
//         `Error updating message ${messageId} for chat ${chatId}:`,
//         error
//       );
//       return c.json({ error: "Failed to update message" }, 500);
//     }
//   }
// );

// // DELETE /chats/:chatId/messages/:messageId - Delete message
// chatRoutes.delete(
//   "/chats/:chatId/messages/:messageId",
//   zValidator(
//     "param",
//     chatIdParamSchema.merge(messageIdParamSchema),
//     (result, c) => {
//       if (!result.success) {
//         return c.json(
//           { success: false, message: "Invalid Chat or Message ID format" },
//           400
//         );
//       }
//     }
//   ),
//   async (c) => {
//     try {
//       const { chatId, messageId } = c.req.valid("param");
//       const chatService = container.resolve(ChatService);
//       const success = await chatService.deleteMessage(chatId, messageId);
//       if (!success) {
//         return c.json({ error: "Message or Chat not found" }, 404);
//       }
//       return c.json({ message: "Message deleted successfully" });
//     } catch (error: any) {
//       const { chatId, messageId } = c.req.valid("param");
//       console.error(
//         `Error deleting message ${messageId} for chat ${chatId}:`,
//         error
//       );
//       return c.json({ error: "Failed to delete message" }, 500);
//     }
//   }
// );

// export default chatRoutes;
