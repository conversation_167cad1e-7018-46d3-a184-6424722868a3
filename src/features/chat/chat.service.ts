// import { singleton } from "tsyringe";
// import { db } from "../../firebase";
// import type { Chat } from "./chat.schema";
// import type { Message } from "./message.schema";

// @singleton()
// export class ChatService {
//   private firestore = db;

//   async getAllChats(userId: string): Promise<Chat[]> {
//     const snapshot = await this.firestore
//       .collection("chats")
//       .where("userId", "==", userId)
//       .orderBy("updatedAt", "desc")
//       .get();

//     return snapshot.docs.map(
//       (doc) =>
//         ({
//           id: doc.id,
//           ...doc.data(),
//           createdAt: doc.data().createdAt?.toDate(),
//           updatedAt: doc.data().updatedAt?.toDate(),
//         } as Chat)
//     );
//   }

//   async getChatById(chatId: string): Promise<Chat | null> {
//     const doc = await this.firestore.collection("chats").doc(chatId).get();

//     if (!doc.exists) {
//       return null;
//     }

//     const data = doc.data();

//     return {
//       id: doc.id,
//       ...data,
//       createdAt: data?.createdAt?.toDate(),
//       updatedAt: data?.updatedAt?.toDate(),
//     } as Chat;
//   }

//   async createChat(
//     chatData: Omit<Chat, "id" | "createdAt" | "updatedAt">
//   ): Promise<Chat> {
//     const now = new Date();
//     const docData = {
//       ...chatData,
//       createdAt: now,
//       updatedAt: now,
//       messageCount: 0,
//       forkCount: 0,
//     };

//     const docRef = await this.firestore.collection("chats").add(docData);

//     return {
//       id: docRef.id,
//       ...docData,
//     };
//   }

//   async updateChat(
//     chatId: string,
//     chatData: Partial<Omit<Chat, "id" | "createdAt">>
//   ): Promise<boolean> {
//     const chatRef = this.firestore.collection("chats").doc(chatId);
//     const doc = await chatRef.get();

//     if (!doc.exists) {
//       return false;
//     }

//     await chatRef.update({
//       ...chatData,
//       updatedAt: new Date(),
//     });

//     return true;
//   }

//   async deleteChat(chatId: string): Promise<boolean> {
//     const chatRef = this.firestore.collection("chats").doc(chatId);
//     const doc = await chatRef.get();

//     if (!doc.exists) {
//       return false;
//     }

//     const messagesSnapshot = await this.firestore
//       .collection("chats")
//       .doc(chatId)
//       .collection("messages")
//       .get();

//     const batch = this.firestore.batch();
//     messagesSnapshot.docs.forEach((doc) => {
//       batch.delete(doc.ref);
//     });

//     batch.delete(chatRef);

//     await batch.commit();
//     return true;
//   }

//   async getAllMessages(chatId: string): Promise<Message[]> {
//     const snapshot = await this.firestore
//       .collection("chats")
//       .doc(chatId)
//       .collection("messages")
//       .orderBy("createdAt", "asc")
//       .get();

//     return snapshot.docs.map((doc) => {
//       const data = doc.data();
//       return {
//         id: doc.id,
//         chatId,
//         ...data,
//         createdAt: data.createdAt?.toDate(),
//         updatedAt: data.updatedAt?.toDate(),
//         timestamp: data.timestamp
//           ? {
//               created:
//                 data.timestamp.created?.toDate() || data.createdAt?.toDate(),
//               updated:
//                 data.timestamp.updated?.toDate() || data.updatedAt?.toDate(),
//             }
//           : undefined,
//         content: {
//           text: data.content?.text || data.text || "",
//           styleType: data.content?.styleType || "plainText",
//           attachments: data.content?.attachments || data.attachments || [],
//           attributes: data.content?.attributes || data.attributes,
//         },
//       } as Message;
//     });
//   }

//   async getMessageById(
//     chatId: string,
//     messageId: string
//   ): Promise<Message | null> {
//     const doc = await this.firestore
//       .collection("chats")
//       .doc(chatId)
//       .collection("messages")
//       .doc(messageId)
//       .get();

//     if (!doc.exists) {
//       return null;
//     }

//     const data = doc.data();
//     if (!data) return null;

//     return {
//       id: doc.id,
//       chatId,
//       ...data,
//       createdAt: data.createdAt?.toDate(),
//       updatedAt: data.updatedAt?.toDate(),
//       timestamp: data.timestamp
//         ? {
//             created:
//               data.timestamp.created?.toDate() || data.createdAt?.toDate(),
//             updated:
//               data.timestamp.updated?.toDate() || data.updatedAt?.toDate(),
//           }
//         : undefined,
//       content: {
//         text: data.content?.text || data.text || "",
//         styleType: data.content?.styleType || "plainText",
//         attachments: data.content?.attachments || data.attachments || [],
//         attributes: data.content?.attributes || data.attributes,
//       },
//     } as Message;
//   }

//   async createMessage(
//     chatId: string,
//     messageData: Omit<Message, "id" | "chatId" | "createdAt" | "updatedAt">
//   ): Promise<Message> {
//     const chatRef = this.firestore.collection("chats").doc(chatId);
//     const chatDoc = await chatRef.get();

//     if (!chatDoc.exists) {
//       throw new Error("Chat not found");
//     }

//     const now = new Date();
//     const docData = {
//       ...messageData,
//       chatId,
//       createdAt: now,
//       updatedAt: now,
//       timestamp: messageData.timestamp || {
//         created: now,
//         updated: now,
//       },
//     };

//     // 使用事务来确保消息计数的一致性
//     await this.firestore.runTransaction(async (transaction) => {
//       // 添加新消息
//       const messageRef = chatRef.collection("messages").doc();
//       transaction.set(messageRef, docData);

//       // 更新聊天记录
//       transaction.update(chatRef, {
//         updatedAt: now,
//         messageCount: (chatDoc.data()?.messageCount || 0) + 1,
//       });
//     });

//     const messageDoc = await chatRef
//       .collection("messages")
//       .orderBy("createdAt", "desc")
//       .limit(1)
//       .get();

//     const message = messageDoc.docs[0];
//     const messageData2 = message.data();

//     return {
//       id: message.id,
//       chatId,
//       ...messageData2,
//       createdAt: messageData2.createdAt?.toDate(),
//       updatedAt: messageData2.updatedAt?.toDate(),
//       timestamp: messageData2.timestamp
//         ? {
//             created:
//               messageData2.timestamp.created?.toDate() ||
//               messageData2.createdAt?.toDate(),
//             updated:
//               messageData2.timestamp.updated?.toDate() ||
//               messageData2.updatedAt?.toDate(),
//           }
//         : undefined,
//       content: {
//         text: messageData2.content?.text || messageData2.text || "",
//         styleType: messageData2.content?.styleType || "plainText",
//         attachments:
//           messageData2.content?.attachments || messageData2.attachments || [],
//         attributes: messageData2.content?.attributes || messageData2.attributes,
//       },
//     } as Message;
//   }

//   async updateMessage(
//     chatId: string,
//     messageId: string,
//     messageData: Partial<Omit<Message, "id" | "chatId" | "createdAt">>
//   ): Promise<boolean> {
//     const messageRef = this.firestore
//       .collection("chats")
//       .doc(chatId)
//       .collection("messages")
//       .doc(messageId);

//     const doc = await messageRef.get();

//     if (!doc.exists) {
//       return false;
//     }

//     await messageRef.update({
//       ...messageData,
//       updatedAt: new Date(),
//     });

//     return true;
//   }

//   async deleteMessage(chatId: string, messageId: string): Promise<boolean> {
//     const chatRef = this.firestore.collection("chats").doc(chatId);
//     const messageRef = chatRef.collection("messages").doc(messageId);

//     const messageDoc = await messageRef.get();
//     const chatDoc = await chatRef.get();

//     if (!messageDoc.exists || !chatDoc.exists) {
//       return false;
//     }

//     // 使用事务来确保消息计数的一致性
//     await this.firestore.runTransaction(async (transaction) => {
//       // 删除消息
//       transaction.delete(messageRef);

//       // 更新聊天记录
//       transaction.update(chatRef, {
//         messageCount: Math.max((chatDoc.data()?.messageCount || 0) - 1, 0),
//       });
//     });

//     return true;
//   }
// }
