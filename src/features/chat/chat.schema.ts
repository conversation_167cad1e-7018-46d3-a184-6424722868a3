import { z } from "zod";

// --- Zod <PERSON>hemas for Chat Validation ---

// Schema for path parameters containing chatId
export const chatIdParamSchema = z.object({
  chatId: z.string().min(1, { message: "Chat ID is required" }),
});

// Schema for path parameters containing userId
export const userIdParamSchema = z.object({
  userId: z.string().min(1, { message: "User ID is required" }),
});

// Schema for creating a chat (POST /chats)
export const createChatSchema = z.object({
  userId: z.string().min(1, { message: "User ID is required" }),
  title: z.string().min(1, { message: "Title is required" }),
  description: z.string().optional(),
  // Add other necessary fields based on Chat interface/requirements
});

// Schema for updating a chat (PUT /chats/:chatId)
export const updateChatSchema = z
  .object({
    title: z.string().min(1).optional(),
    description: z.string().optional(),
    // Add other updatable fields
  })
  .partial(); // Make all fields optional for update

export const getChatsQuerySchema = z.object({
  limit: z.coerce.number().int().positive().optional().default(20),
  cursor: z.string().optional(), // Example cursor pagination
});

// --- Exported Types from Schemas ---
export type ChatIdParam = z.infer<typeof chatIdParamSchema>;
export type UserIdParam = z.infer<typeof userIdParamSchema>;
export type CreateChatBody = z.infer<typeof createChatSchema>;
export type UpdateChatBody = z.infer<typeof updateChatSchema>;
export type GetChatsQuery = z.infer<typeof getChatsQuerySchema>;

// --- Original TypeScript Interface (Reference Only) ---
// Note: This interface is for type definition purposes, not for validation.
// Validation should use the Zod schemas defined above.
export interface Chat {
  id?: string;
  userId: string;
  title: string;
  description?: string;
  createdAt?: Date;
  updatedAt?: Date;
  status?: string;
  model?: string;
  modelType?: string;
  messageCount?: number;
  parentChatId?: string;
  visibility?: string;
  sharedWithUserIds?: string[];
  forkCount?: number;
  tags?: string[];
  parameters?: {
    systemPrompt?: string;
    temperature?: number;
    maxResponseLength?: number;
    [key: string]: any;
  };
}
