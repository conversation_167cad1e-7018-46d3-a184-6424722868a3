import { z } from "zod";

// --- <PERSON><PERSON>as for Message Validation ---

// Reusable schema for attachment (adjust based on actual usage)
const attachmentSchema = z.object({
  id: z.string(),
  type: z.enum(["image", "audio", "video", "other"]),
  url: z.string().url(),
  thumbnailUrl: z.string().url().optional(),
  filename: z.string().optional(),
  fileSize: z.number().optional(),
  mimeType: z.string().optional(),
  isUploaded: z.boolean().optional(),
  uploadProgress: z.number().optional(),
});

// Schema for message content
const messageContentSchema = z.object({
  text: z.string(), // Assuming text is always required
  styleType: z.enum(["plainText", "markdown"]).optional().default("plainText"),
  attachments: z.array(attachmentSchema).optional(),
  attributes: z.record(z.string()).optional(),
});

// Schema for path parameters containing messageId
export const messageIdParamSchema = z.object({
  messageId: z.string().min(1, { message: "Message ID is required" }),
});

// Schema for creating a message (POST /chats/:chatId/messages)
export const createMessageSchema = z.object({
  content: messageContentSchema,
  // Add other fields like sender info if they come from the request body
  role: z.enum(["user", "assistant", "system"]).optional().default("user"),
  // maybe sender { id: string, type: SenderType }?
});

// Schema for updating a message (PUT /chats/:chatId/messages/:messageId)
export const updateMessageSchema = z
  .object({
    content: messageContentSchema.optional(),
    // Add other updatable fields like isRead, metadata etc.
    isRead: z.boolean().optional(),
  })
  .partial();

// Schema for GET /chats/:chatId/messages query parameters (Example)
export const getMessagesQuerySchema = z.object({
  limit: z.coerce.number().int().positive().optional().default(50),
  cursor: z.string().optional(), // Example cursor pagination
  // Add other filters like before/after timestamp if needed
});

// --- Exported Types from Schemas ---
export type MessageIdParam = z.infer<typeof messageIdParamSchema>;
export type CreateMessageBody = z.infer<typeof createMessageSchema>;
export type UpdateMessageBody = z.infer<typeof updateMessageSchema>;
export type GetMessagesQuery = z.infer<typeof getMessagesQuerySchema>;
export type MessageContent = z.infer<typeof messageContentSchema>;
export type Attachment = z.infer<typeof attachmentSchema>;

export type SenderType = "user" | "ai" | "system";

export type MessageType = "user" | "ai" | "system" | "error";

export type MessageStatus =
  | "sending"
  | "sent"
  | "receiving"
  | "received"
  | "error"
  | "deleted";

export type StyleType = "plainText" | "markdown";

export interface Timestamp {
  created: Date;
  updated: Date;
}

export interface Message {
  id?: string;
  chatId: string;
  sender?: {
    id: string;
    type: SenderType;
  };
  type?: MessageType;
  status?: MessageStatus;
  content: MessageContent;
  metadata?: Record<string, string>;
  timestamp?: Timestamp;
  createdAt?: Date;
  updatedAt?: Date;
  isRead?: boolean;
  role?: "user" | "assistant" | "system";
}
