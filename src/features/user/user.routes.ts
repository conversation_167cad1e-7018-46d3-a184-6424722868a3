import { CreateUserEndpoint } from "./endpoints/create-user";
import { GetAllUsersEndpoint } from "./endpoints/get-all-users";
import { GetUserByIdEndpoint } from "./endpoints/get-user-by-id";
import { UpdateUserEndpoint } from "./endpoints/update-user";
import { DeleteUserEndpoint } from "./endpoints/delete-user";

export function registerUserRoutes(
  openapi: any,
  routePrefix: string = "/users"
) {
  const base = routePrefix.startsWith("/") ? routePrefix : `/${routePrefix}`;
  openapi.post(base, CreateUserEndpoint);
  openapi.get(base, GetAllUsersEndpoint);
  openapi.get(`${base}/:id`, GetUserByIdEndpoint);
  openapi.put(`${base}/:id`, UpdateUserEndpoint);
  openapi.delete(`${base}/:id`, DeleteUserEndpoint);
}
