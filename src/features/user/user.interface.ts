import type { InjectionToken } from "tsyringe";
import type {
  User,
  UpdateUserSubscriptionData,
  UpdateUserCreditsData,
  Subscription,
} from "./user.schema";

// Define the injection token for UserService
export const IUserService: InjectionToken<UserService> = Symbol("IUserService");

/**
 * Abstract class representing the contract for a user service.
 * It should define methods for user-related operations.
 */
export abstract class UserService {
  abstract createUser(userData: Omit<User, "id" | "createdAt">): Promise<User>;
  abstract getAllUsers(): Promise<User[]>;
  abstract getUserById(id: string): Promise<User | null>;
  abstract updateUser(
    id: string,
    userData: Partial<Omit<User, "id">>
  ): Promise<boolean>;
  abstract deleteUser(id: string): Promise<boolean>;

  // Subscription-related methods
  abstract updateUserSubscription(
    userId: string,
    subscriptionData: UpdateUserSubscriptionData
  ): Promise<boolean>;

  abstract updateUserCredits(
    userId: string,
    creditsData: UpdateUserCreditsData
  ): Promise<boolean>;

  abstract getUserSubscriptionStatus(
    userId: string
  ): Promise<Subscription | null>;
}
