import { z } from "zod";

// Subscription status enum
export const SubscriptionStatusSchema = z.enum([
  "active",
  "canceled",
  "expired",
]);

export type SubscriptionStatus = z.infer<typeof SubscriptionStatusSchema>;

// Subscription interface
export interface Subscription {
  status: SubscriptionStatus;
  entitlements: string[];
  expires_date?: string; // ISO 8601 format
  last_updated_from_webhook: string; // ISO 8601 format
}

export interface User {
  id: string;
  email: string;
  displayName?: string;
  photoURL?: string;
  createdAt?: Date;
  updatedAt?: Date;
  lastLoginAt?: Date;
  status?: string;
  role?: string;
  settings?: {
    theme?: string;
    language?: string;
    notifications?: boolean;
    [key: string]: any;
  };
  // New subscription-related fields
  credits?: number;
  subscription?: Subscription;
  // New user registration fields
  is_new_user?: boolean;
}

export const userIdParamsSchema = z.object({
  id: z.string().min(1, { message: "User ID cannot be empty" }),
});

// Subscription Zod schema
export const SubscriptionSchema = z.object({
  status: SubscriptionStatusSchema,
  entitlements: z.array(z.string()),
  expires_date: z.string().datetime().optional(),
  last_updated_from_webhook: z.string().datetime(),
});

export const createUserBodySchema = z.object({
  email: z.string().email({ message: "Invalid email address" }),
  displayName: z.string().optional(),
  photoURL: z.string().url({ message: "Invalid URL format" }).optional(),
  credits: z.number().min(0).optional(),
  subscription: SubscriptionSchema.optional(),
  is_new_user: z.boolean().optional(),
});

export const updateUserBodySchema = z
  .object({
    displayName: z.string().optional(),
    photoURL: z.string().url({ message: "Invalid URL format" }).optional(),
    credits: z.number().min(0).optional(),
    subscription: SubscriptionSchema.optional(),
    is_new_user: z.boolean().optional(),
  })
  .partial();

export const UserResponseSchema = z.object({
  id: z.string(),
  email: z.string().email(),
  displayName: z.string().optional().nullable(),
  photoURL: z.string().url().optional().nullable(),
  createdAt: z
    .string()
    .datetime({ message: "Invalid datetime format for createdAt" })
    .optional()
    .nullable(),
  updatedAt: z
    .string()
    .datetime({ message: "Invalid datetime format for updatedAt" })
    .optional()
    .nullable(),
  lastLoginAt: z
    .string()
    .datetime({ message: "Invalid datetime format for lastLoginAt" })
    .optional()
    .nullable(),
  status: z.string().optional().nullable(),
  role: z.string().optional().nullable(),
  settings: z
    .object({
      theme: z.string().optional().nullable(),
      language: z.string().optional().nullable(),
      notifications: z.boolean().optional().nullable(),
    })
    .optional()
    .nullable(),
  credits: z.number().min(0).optional().nullable(),
  subscription: SubscriptionSchema.optional().nullable(),
  is_new_user: z.boolean().optional().nullable(),
});

export const GenericErrorSchema = z.object({ error: z.string() });
export const MessageResponseSchema = z.object({ message: z.string() });
export const NotFoundErrorSchema = z.object({
  error: z.literal("User not found"),
});

export const ZodValidationErrorSchema = z.object({
  error: z.string(),
  issues: z.record(z.array(z.string())).optional(),
});

// Schema for updating user subscription
export const UpdateUserSubscriptionSchema = z.object({
  status: SubscriptionStatusSchema,
  entitlements: z.array(z.string()),
  expires_date: z.string().datetime().optional(),
  last_updated_from_webhook: z.string().datetime(),
});

// Schema for updating user credits
export const UpdateUserCreditsSchema = z.object({
  credits: z.number().min(0),
});

export type UpdateUserSubscriptionData = z.infer<
  typeof UpdateUserSubscriptionSchema
>;
export type UpdateUserCreditsData = z.infer<typeof UpdateUserCreditsSchema>;
