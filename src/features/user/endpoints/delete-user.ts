import { type Context } from "hono";

import { OpenAPIRoute } from "chanfana";

import {
  userIdParamsSchema,
  MessageResponseSchema,
  NotFoundErrorSchema,
  GenericErrorSchema,
  ZodValidationErrorSchema,
} from "../user.schema";
import { IUserService, UserService } from "../user.interface";
import type { HonoEnv } from "../../../types";

export class DeleteUserEndpoint extends OpenAPIRoute {
  schema = {
    summary: "Delete a user",
    description: "Deletes a user by their ID.",
    tags: ["Users"],
    request: {
      params: userIdParamsSchema,
    },
    responses: {
      "200": {
        description: "User deleted successfully",
        content: { "application/json": { schema: MessageResponseSchema } },
      },
      "400": {
        description: "Invalid User ID format", // Or other param validation errors
        content: { "application/json": { schema: ZodValidationErrorSchema } },
      },
      "404": {
        description: "User not found",
        content: { "application/json": { schema: NotFoundErrorSchema } },
      },
      "500": {
        description: "Failed to delete user",
        content: { "application/json": { schema: GenericErrorSchema } },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    try {
      const { params } = await this.getValidatedData<typeof this.schema>();
      const container = c.get("container");
      const userService = container.resolve<UserService>(IUserService);
      const success = await userService.deleteUser(params.id);
      if (!success) {
        return c.json({ error: "User not found" }, 404);
      }
      return c.json({ message: "User deleted successfully" });
    } catch (error: any) {
      const idFromPath = c.req.param("id");
      console.error(
        `Error deleting user ${idFromPath || "with invalid params"}:`,
        error
      );
      return c.json({ error: "Failed to delete user" }, 500);
    }
  }
}
