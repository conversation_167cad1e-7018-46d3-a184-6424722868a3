import { type Context } from "hono";

import { OpenAPIRoute } from "chanfana";
import { z } from "zod";

import {
  userIdParamsSchema,
  updateUserBodySchema,
  MessageResponseSchema,
  NotFoundErrorSchema,
  GenericErrorSchema,
  ZodValidationErrorSchema,
} from "../user.schema";
import { IUserService, UserService } from "../user.interface";
import type { HonoEnv } from "../../../types";
export class UpdateUserEndpoint extends OpenAPIRoute {
  schema = {
    summary: "Update a user",
    description: "Updates an existing user's details by their ID.",
    tags: ["Users"],
    request: {
      params: userIdParamsSchema,
      body: {
        content: {
          "application/json": {
            schema: updateUserBodySchema,
          },
        },
        required: true, // body is required for update
      },
    },
    responses: {
      "200": {
        description: "User updated successfully",
        content: { "application/json": { schema: MessageResponseSchema } },
      },
      "400": {
        description: "Validation failed or invalid input",
        content: { "application/json": { schema: ZodValidationErrorSchema } },
      },
      "404": {
        description: "User not found",
        content: { "application/json": { schema: NotFoundErrorSchema } },
      },
      "500": {
        description: "Failed to update user",
        content: { "application/json": { schema: GenericErrorSchema } },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    try {
      const { params, body } = await this.getValidatedData<
        typeof this.schema
      >();
      const container = c.get("container");
      const userService = container.resolve<UserService>(IUserService);
      const success = await userService.updateUser(params.id, body);
      if (!success) {
        return c.json({ error: "User not found" }, 404);
      }
      return c.json({ message: "User updated successfully" });
    } catch (error: any) {
      const idFromPath = c.req.param("id");
      console.error(
        `Error updating user ${idFromPath || "with invalid params"}:`,
        error
      );
      return c.json({ error: "Failed to update user" }, 500);
    }
  }
}
