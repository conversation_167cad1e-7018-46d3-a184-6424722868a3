import { type Context } from "hono";

import { OpenAPIRoute } from "chanfana";

import {
  createUserBodySchema,
  UserResponseSchema,
  GenericErrorSchema,
  ZodValidationErrorSchema,
} from "../user.schema";
import { IUserService, UserService } from "../user.interface";
import type { HonoEnv } from "../../../types";

export class CreateUserEndpoint extends OpenAPIRoute {
  schema = {
    summary: "Create a new user",
    description:
      "Creates a new user with the provided email and optional details.",
    tags: ["Users"],
    request: {
      body: {
        content: {
          "application/json": {
            schema: createUserBodySchema,
          },
        },
        required: true,
      },
    },
    responses: {
      "201": {
        description: "User created successfully",
        content: {
          "application/json": {
            schema: UserResponseSchema,
          },
        },
      },
      "400": {
        description: "Validation failed or invalid input",
        content: { "application/json": { schema: ZodValidationErrorSchema } },
      },
      "500": {
        description: "Failed to create user",
        content: { "application/json": { schema: GenericErrorSchema } },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    try {
      const { body } = await this.getValidatedData<typeof this.schema>();
      const container = c.get("container");
      const userService = container.resolve<UserService>(IUserService);
      const user = await userService.createUser(body);
      return c.json(user, 201);
    } catch (error: any) {
      console.error("Error creating user:", error);
      return c.json({ error: "Failed to create user" }, 500);
    }
  }
}
