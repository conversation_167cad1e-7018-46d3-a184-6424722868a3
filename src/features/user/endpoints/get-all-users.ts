import { type Context } from "hono";

import { OpenAPIRoute } from "chanfana";
import { z } from "zod";
import { UserResponseSchema, GenericErrorSchema } from "../user.schema";
import { IUserService, UserService } from "../user.interface";
import type { HonoEnv } from "../../../types";

export class GetAllUsersEndpoint extends OpenAPIRoute {
  schema = {
    summary: "Get all users",
    description: "Retrieves a list of all users.",
    tags: ["Users"],
    responses: {
      "200": {
        description: "A list of users",
        content: {
          "application/json": {
            schema: z.array(UserResponseSchema),
          },
        },
      },
      "500": {
        description: "Failed to get users",
        content: { "application/json": { schema: GenericErrorSchema } },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    try {
      const container = c.get("container");
      const userService = container.resolve<UserService>(IUserService);
      const users = await userService.getAllUsers();
      return c.json(users);
    } catch (error: any) {
      console.error("Error getting all users:", error);
      return c.json({ error: "Failed to get users" }, 500);
    }
  }
}
