import { type Context } from "hono";

import { OpenAPIRoute } from "chanfana";
import {
  userIdParamsSchema,
  UserResponseSchema,
  NotFoundErrorSchema,
  GenericErrorSchema,
  ZodValidationErrorSchema,
} from "../user.schema";
import { IUserService, UserService } from "../user.interface";
import type { HonoEnv } from "../../../types";

export class GetUserByIdEndpoint extends OpenAPIRoute {
  schema = {
    summary: "Get a user by ID",
    description: "Retrieves a single user by their unique ID.",
    tags: ["Users"],
    request: {
      params: userIdParamsSchema,
    },
    responses: {
      "200": {
        description: "Successfully retrieved user",
        content: { "application/json": { schema: UserResponseSchema } },
      },
      "400": {
        description: "Invalid User ID format",
        content: { "application/json": { schema: ZodValidationErrorSchema } },
      },
      "404": {
        description: "User not found",
        content: { "application/json": { schema: NotFoundErrorSchema } },
      },
      "500": {
        description: "Failed to get user",
        content: { "application/json": { schema: GenericErrorSchema } },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    try {
      const { params } = await this.getValidatedData<typeof this.schema>();
      const container = c.get("container");
      const userService = container.resolve<UserService>(IUserService);
      const user = await userService.getUserById(params.id);
      if (!user) {
        return c.json({ error: "User not found" }, 404);
      }
      return c.json(user);
    } catch (error: any) {
      const idFromPath = c.req.param("id");
      console.error(
        `Error getting user ${idFromPath || "with invalid params"}:`,
        error
      );
      return c.json({ error: "Failed to get user" }, 500);
    }
  }
}
