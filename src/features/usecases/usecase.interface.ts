// // src/features/usecases/usecase.interface
// export interface UsecaseItem {
//   id: number;
//   status: string;
//   sort: number | null;
//   date_created: string;
//   date_updated: string | null;
//   name: string; // This will be used as 'scene'
//   prompt: string;
//   cover_image: string | null; // Assuming it could be a string ID or path
//   cover_url: string | null;
//   category: string;
// }

// export interface UsecasesResponse {
//   data: UsecaseItem[];
// }
