// // src/features/usecases/usecase.service
// import { singleton, inject } from "tsyringe";
// import type { UsecaseItem, UsecasesResponse } from "./usecase.interface";
// import { MemoryCacheService } from "../../infrastructure/cache/memory-cache.service";

// const USECASES_API_URL = "https://cms.a1d.ai/items/";
// const CACHE_TTL_MS = 24 * 60 * 60 * 1000; // 24 hours

// @singleton()
// export class UsecaseService {
//   constructor(
//     @inject(MemoryCacheService) private cacheService: MemoryCacheService
//   ) {}

//   private async fetchAllUsecases(scene: string): Promise<UsecaseItem[]> {
//     const cacheKey = `usecases:${scene}`;
//     const cachedUsecases = this.cacheService.get<UsecaseItem[]>(cacheKey);

//     if (cachedUsecases) {
//       console.log(`Cache hit for key: ${cacheKey}`);
//       return cachedUsecases;
//     }

//     console.log(`Cache miss for key: ${cacheKey}. Fetching from API.`);
//     try {
//       const finalUrl = USECASES_API_URL + scene;
//       console.log("finalUrl", finalUrl, scene);
//       const response = await fetch(finalUrl);
//       console.log(response);
//       if (!response.ok) {
//         // Log the error status and text for debugging
//         console.error(
//           `Error fetching usecases: ${response.status} ${response.statusText}`
//         );
//         // Consider throwing a custom error or returning a default/empty state
//         throw new Error("Failed to fetch usecases from external API");
//       }
//       const jsonData = (await response.json()) as UsecasesResponse;
//       const usecases = jsonData.data || [];
//       this.cacheService.set(cacheKey, usecases, CACHE_TTL_MS);
//       return usecases;
//     } catch (error) {
//       console.error("Error in fetchAllUsecases:", error);
//       // Re-throw or handle as appropriate for your application's error strategy
//       throw error;
//     }
//   }

//   public async getGhibliUsecaseByName(
//     name: string
//   ): Promise<UsecaseItem | undefined> {
//     const allUsecases = await this.fetchAllUsecases("ghibli_usecases");
//     // Case-insensitive search for the scene name
//     const foundUsecase = allUsecases.find(
//       (usecase) => usecase.name.toLowerCase() === name.toLowerCase()
//     );
//     return foundUsecase;
//   }

//   public async getAllUsecases(scene: string): Promise<UsecaseItem[]> {
//     console.log("getAllUsecases", scene);
//     return this.fetchAllUsecases(scene);
//   }
// }
