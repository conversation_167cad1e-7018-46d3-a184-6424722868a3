// import { type Context } from "hono";
// import { container } from "tsyringe";
// import { OpenAPIRoute } from "chanfana";
// import { z } from "zod";
// import { UsecaseService } from "../usecase.service";
// import type { UsecaseItem } from "../usecase.interface"; // Import UsecaseItem for type safety

// // Define Zod schema based on UsecaseItem interface
// const GhibliUsecaseSchema = z.object({
//   id: z.number(),
//   status: z.string(),
//   sort: z.number().nullable(),
//   date_created: z.string().datetime(),
//   date_updated: z.string().datetime().nullable(),
//   name: z.string(),
//   prompt: z.string(),
//   cover_image: z.string().nullable(),
//   cover_url: z.string().nullable(),
//   category: z.string(),
// });

// const GenericErrorSchema = z.object({
//   error: z.string(),
// });

// export class GetAllGhibliUsecasesEndpoint extends OpenAPIRoute {
//   schema = {
//     summary: "Get all Ghibli usecases",
//     description: "Retrieves a list of all Ghibli usecases.",
//     tags: ["Usecases"],
//     responses: {
//       "200": {
//         description: "A list of Ghibli usecases",
//         content: {
//           "application/json": {
//             // The response is { data: UsecaseItem[] }
//             schema: z.object({ data: z.array(GhibliUsecaseSchema) }),
//           },
//         },
//       },
//       "500": {
//         description: "Failed to get Ghibli usecases",
//         content: { "application/json": { schema: GenericErrorSchema } },
//       },
//     },
//   };

//   async handle(c: Context) {
//     try {
//       const usecaseService = container.resolve(UsecaseService);
//       const allUsecases = await usecaseService.getAllUsecases(
//         "ghibli_usecases"
//       );
//       // The service already returns { data: UsecaseItem[] }
//       return c.json(allUsecases);
//     } catch (error: any) {
//       console.error("Error fetching all Ghibli usecases:", error);
//       // Consistent error response with user module
//       return c.json({ error: "Failed to get Ghibli usecases" }, 500);
//     }
//   }
// }
