import { inject, singleton } from "tsyringe";
import type { CollectionReference } from "firebase-rest-firestore";

import { ExploreService } from "./explore.interface";
import type {
  ExploreQuery,
  ExploreResponse,
  ExploreItem,
  UserInteractionState,
  UserInfo,
} from "./explore.schema";
import { AssetService, IAssetService } from "../assets/asset.interface";
import type { AssetListQuery } from "../assets/asset.schema";
import {
  IDbService,
  type DbService,
} from "../../infrastructure/db/db-service.interface";
import { SocialService, ISocialService } from "../social/social.interface";

@singleton()
export class FirestoreExploreService implements ExploreService {
  private usersCollection: CollectionReference;

  constructor(
    @inject(IAssetService) private assetService: AssetService,
    @inject(IDbService) dbService: DbService,
    @inject(ISocialService) private socialService: SocialService
  ) {
    this.usersCollection = dbService.getFirestoreInstance().collection("users");
  }

  async getExploreContent(query: ExploreQuery): Promise<ExploreResponse> {
    try {
      // 1. 构建查询条件 - 基于现有的 AssetService.getPublicAssets
      const assetQuery: AssetListQuery = {
        page: query.page || 1,
        limit: query.limit || 20,
        type: query.type,
        // 只查询公开的活跃内容
        status: "active",
      };

      // 2. 查询公开资产
      const assetResponse = await this.assetService.getPublicAssets(assetQuery);

      // 3. 过滤 sourceType
      let filteredAssets = assetResponse.assets;
      if (query.sourceType && query.sourceType !== "all") {
        filteredAssets = filteredAssets.filter(
          (asset) => asset.sourceType === query.sourceType
        );
      }

      // 4. 应用排序
      if (query.sortBy === "popular") {
        filteredAssets.sort(
          (a, b) =>
            b.likeCount + b.favoriteCount - (a.likeCount + a.favoriteCount)
        );
      } else {
        // 默认按创建时间排序（最新的在前）
        filteredAssets.sort(
          (a, b) => b.createdAt.getTime() - a.createdAt.getTime()
        );
      }

      // 5. 批量获取用户信息
      const userIds = [...new Set(filteredAssets.map((asset) => asset.userId))];
      const userInfoMap = await this.getUserInfoBatch(userIds);

      // 6. 转换为 ExploreItem 格式
      const items: ExploreItem[] = filteredAssets.map((asset) => ({
        id: asset.id,
        type: asset.type,
        sourceType: asset.sourceType,
        userId: asset.userId,
        userDisplayName: userInfoMap.get(asset.userId)?.displayName,
        userPhotoURL: userInfoMap.get(asset.userId)?.photoURL,
        url: asset.url,
        thumbnailUrl: asset.thumbnailUrl,
        name: asset.name,
        size: asset.size,
        generationPrompt: asset.generationPrompt,
        description: asset.description,
        tags: asset.tags,
        likeCount: asset.likeCount,
        favoriteCount: asset.favoriteCount,
        createdAt: asset.createdAt.toISOString(),
      }));

      // 7. 计算统计信息
      const totalCounts = {
        image: filteredAssets.filter((a) => a.type.startsWith("image/")).length,
        video: filteredAssets.filter((a) => a.type.startsWith("video/")).length,
        total: filteredAssets.length,
      };

      return {
        items,
        pagination: {
          currentPage: assetResponse.pagination.page,
          totalPages: assetResponse.pagination.totalPages,
          totalItems: assetResponse.pagination.total,
          itemsPerPage: assetResponse.pagination.limit,
          hasNextPage: assetResponse.pagination.hasNext,
          hasPreviousPage: assetResponse.pagination.hasPrev,
        },
        filters: {
          totalCounts,
        },
      };
    } catch (error) {
      console.error("[ExploreService] Error getting explore content:", error);
      throw new Error(
        `Failed to get explore content: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  async getUserInteractionStatus(
    userId: string,
    assetIds: string[]
  ): Promise<Map<string, UserInteractionState>> {
    try {
      // Use SocialService to get real user interaction states
      const interactionStates =
        await this.socialService.getUserInteractionStates(userId, assetIds);

      const result = new Map<string, UserInteractionState>();

      // Convert the response format to match the expected interface
      for (const assetId of assetIds) {
        const state = interactionStates[assetId];
        result.set(assetId, {
          assetId,
          isLikedByCurrentUser: state?.isLikedByCurrentUser || false,
          isFavoritedByCurrentUser: state?.isFavoritedByCurrentUser || false,
        });
      }

      return result;
    } catch (error) {
      console.error(
        "[ExploreService] Error getting user interaction status:",
        error
      );

      // Fallback to empty states if there's an error
      const result = new Map<string, UserInteractionState>();
      for (const assetId of assetIds) {
        result.set(assetId, {
          assetId,
          isLikedByCurrentUser: false,
          isFavoritedByCurrentUser: false,
        });
      }
      return result;
    }
  }

  /**
   * 批量获取用户信息
   * @param userIds - 用户ID数组
   * @returns Promise<Map<string, UserInfo>> - 用户ID到用户信息的映射
   */
  private async getUserInfoBatch(
    userIds: string[]
  ): Promise<Map<string, UserInfo>> {
    const userInfoMap = new Map<string, UserInfo>();

    if (userIds.length === 0) {
      return userInfoMap;
    }

    try {
      // 批量查询用户信息
      // 注意：Firestore 的 'in' 查询最多支持 10 个值，如果超过需要分批查询
      const batchSize = 10;
      const batches = [];

      for (let i = 0; i < userIds.length; i += batchSize) {
        const batch = userIds.slice(i, i + batchSize);
        batches.push(batch);
      }

      for (const batch of batches) {
        // 使用文档 ID 进行批量查询，而不是查询文档数据中的 id 字段
        const promises = batch.map((userId) =>
          this.usersCollection.doc(userId).get()
        );

        const docs = await Promise.all(promises);

        docs.forEach((doc, index) => {
          const userId = batch[index];

          if (doc.exists) {
            const userData = doc.data();

            userInfoMap.set(userId, {
              id: userId, // 使用文档 ID 作为用户 ID
              displayName:
                userData?.displayName || userData?.name || "Unknown User",
              photoURL: userData?.photoURL,
            });
          } else {
            console.warn(
              `[ExploreService] getUserInfoBatch: User document ${userId} not found`
            );
          }
        });
      }

      // 为没有找到的用户添加默认信息
      userIds.forEach((userId) => {
        if (!userInfoMap.has(userId)) {
          userInfoMap.set(userId, {
            id: userId,
            displayName: "Unknown User",
            photoURL: undefined,
          });
        }
      });

      return userInfoMap;
    } catch (error) {
      console.error("[ExploreService] Error getting user info batch:", error);

      // 如果批量查询失败，为所有用户返回默认信息
      userIds.forEach((userId) => {
        userInfoMap.set(userId, {
          id: userId,
          displayName: "Unknown User",
          photoURL: undefined,
        });
      });

      return userInfoMap;
    }
  }
}
