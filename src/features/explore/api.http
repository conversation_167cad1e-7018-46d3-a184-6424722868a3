# Explore API 测试文件
# 使用 REST Client 扩展或类似工具运行这些请求

### 变量定义
@baseUrl = http://localhost:8787/api/v1
@authToken = YOUR_FIREBASE_JWT_TOKEN_HERE
@assetId = YOUR_ASSET_ID_HERE

### 1. 获取 Explore 内容 - 基础请求（无需认证）
GET {{baseUrl}}/explore
Content-Type: application/json

### 2. 获取 Explore 内容 - 带分页参数
GET {{baseUrl}}/explore?page=1&limit=10
Content-Type: application/json

### 3. 获取 Explore 内容 - 过滤图片类型
GET {{baseUrl}}/explore?type=image/jpeg&page=1&limit=20
Content-Type: application/json

### 4. 获取 Explore 内容 - 过滤视频类型
GET {{baseUrl}}/explore?type=video/mp4&page=1&limit=20
Content-Type: application/json

### 5. 获取 Explore 内容 - 只显示AI生成内容
GET {{baseUrl}}/explore?sourceType=ai_generated&page=1&limit=20
Content-Type: application/json

### 6. 获取 Explore 内容 - 按热门排序
GET {{baseUrl}}/explore?sortBy=popular&page=1&limit=20
Content-Type: application/json

### 7. 获取 Explore 内容 - 按最新排序（默认）
GET {{baseUrl}}/explore?sortBy=latest&page=1&limit=20
Content-Type: application/json

### 8. 获取用户交互状态（需要认证）
GET {{baseUrl}}/explore/{{assetId}}/interaction-status
Authorization: Bearer {{authToken}}
Content-Type: application/json

### 9. 测试无效参数
GET {{baseUrl}}/explore?page=0&limit=100
Content-Type: application/json

### 10. 测试无效的 sourceType
GET {{baseUrl}}/explore?sourceType=invalid_type
Content-Type: application/json

### 11. 测试无效的 sortBy
GET {{baseUrl}}/explore?sortBy=invalid_sort
Content-Type: application/json

### 12. 测试未认证的交互状态请求
GET {{baseUrl}}/explore/{{assetId}}/interaction-status
Content-Type: application/json

### 13. 测试不存在的资产交互状态
GET {{baseUrl}}/explore/non-existent-asset-id/interaction-status
Authorization: Bearer {{authToken}}
Content-Type: application/json
