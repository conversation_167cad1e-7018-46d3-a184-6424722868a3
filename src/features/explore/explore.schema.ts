import { z } from "zod";

// Query parameters for explore content
export const ExploreQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1).optional(),
  limit: z.coerce.number().min(1).max(50).default(20).optional(),
  type: z.string().optional(), // MIME type filter like "image/*", "video/*", "image/jpeg"
  sourceType: z.enum(["ai_generated", "user_upload", "all"]).default("all").optional(),
  sortBy: z.enum(["latest", "popular"]).default("latest").optional(),
});

export type ExploreQuery = z.infer<typeof ExploreQuerySchema>;

// Individual explore item
export const ExploreItemSchema = z.object({
  id: z.string(),
  type: z.string(), // MIME type
  sourceType: z.enum(["ai_generated", "user_upload"]),
  userId: z.string(),
  userDisplayName: z.string().optional(),
  userPhotoURL: z.string().url().optional(),

  // Content information (Web standards)
  url: z.string().url(),
  thumbnailUrl: z.string().url().optional(),
  name: z.string().optional(),
  size: z.number().optional(),
  generationPrompt: z.string().optional(), // Only for AI-generated content
  description: z.string().optional(),
  tags: z.array(z.string()).optional(),

  // Social statistics
  likeCount: z.number().min(0),
  favoriteCount: z.number().min(0),

  // Timestamps
  createdAt: z.string().datetime(), // ISO string
});

export type ExploreItem = z.infer<typeof ExploreItemSchema>;

// Pagination metadata
export const PaginationMetaSchema = z.object({
  currentPage: z.number(),
  totalPages: z.number(),
  totalItems: z.number(),
  itemsPerPage: z.number(),
  hasNextPage: z.boolean(),
  hasPreviousPage: z.boolean(),
});

export type PaginationMeta = z.infer<typeof PaginationMetaSchema>;

// Filter metadata
export const FilterMetaSchema = z.object({
  totalCounts: z.object({
    image: z.number(),
    video: z.number(),
    total: z.number(),
  }),
});

export type FilterMeta = z.infer<typeof FilterMetaSchema>;

// Complete explore response
export const ExploreResponseSchema = z.object({
  items: z.array(ExploreItemSchema),
  pagination: PaginationMetaSchema,
  filters: FilterMetaSchema,
});

export type ExploreResponse = z.infer<typeof ExploreResponseSchema>;

// User interaction state for a single asset
export const UserInteractionStateSchema = z.object({
  assetId: z.string(),
  isLikedByCurrentUser: z.boolean(),
  isFavoritedByCurrentUser: z.boolean(),
});

export type UserInteractionState = z.infer<typeof UserInteractionStateSchema>;

// User interaction status response
export const InteractionStatusResponseSchema = z.object({
  assetId: z.string(),
  isLikedByCurrentUser: z.boolean(),
  isFavoritedByCurrentUser: z.boolean(),
});

export type InteractionStatusResponse = z.infer<typeof InteractionStatusResponseSchema>;

// User info for batch queries
export interface UserInfo {
  id: string;
  displayName?: string;
  photoURL?: string;
}
