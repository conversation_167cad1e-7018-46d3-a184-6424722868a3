import { type Context } from "hono";
import { OpenAPIRoute } from "chanfana";
import { z } from "zod";

import { InteractionStatusResponseSchema } from "../explore.schema";
import { ExploreService, IExploreService } from "../explore.interface";
import { ErrorResponseSchema } from "../../../shared/error-response.schema";
import type { HonoEnv } from "../../../types";
import { getFirebaseToken } from "@hono/firebase-auth";

// Path parameter schema
const InteractionStatusParamsSchema = z.object({
  assetId: z.string().min(1, "Asset ID is required"),
});

export class GetInteractionStatusEndpoint extends OpenAPIRoute {
  schema = {
    summary: "获取用户交互状态",
    description: "获取当前用户对指定资产的交互状态（点赞、收藏等）",
    tags: ["Explore"],
    security: [
      {
        BearerAuth: [],
      },
    ],
    request: {
      params: InteractionStatusParamsSchema,
    },
    responses: {
      "200": {
        description: "成功获取交互状态",
        content: {
          "application/json": {
            schema: InteractionStatusResponseSchema,
          },
        },
      },
      "401": {
        description: "未认证或认证失效",
        content: {
          "application/json": {
            schema: ErrorResponseSchema,
          },
        },
      },
      "404": {
        description: "资产不存在",
        content: {
          "application/json": {
            schema: ErrorResponseSchema,
          },
        },
      },
      "500": {
        description: "服务器内部错误",
        content: {
          "application/json": {
            schema: ErrorResponseSchema,
          },
        },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    try {
      const container = c.get("container");
      const exploreService = container.resolve<ExploreService>(IExploreService);

      const firebaseToken = getFirebaseToken(c);
      if (!firebaseToken) {
        return c.json({ error: "Authentication required" }, 401);
      }
      const userId = firebaseToken.uid;

      const { params } = await this.getValidatedData<typeof this.schema>();
      const { assetId } = params;

      console.log(
        `[GetInteractionStatus] Getting interaction status for asset ${assetId} and user ${userId}`
      );

      // 获取用户对该资产的交互状态
      const interactionMap = await exploreService.getUserInteractionStatus(
        userId,
        [assetId]
      );

      const interactionState = interactionMap.get(assetId);

      if (!interactionState) {
        return c.json(
          {
            error: "Asset not found or not accessible",
            code: "NOT_FOUND",
            details:
              "The requested asset does not exist or is not publicly accessible",
          },
          404
        );
      }

      const response = {
        assetId: interactionState.assetId,
        isLikedByCurrentUser: interactionState.isLikedByCurrentUser,
        isFavoritedByCurrentUser: interactionState.isFavoritedByCurrentUser,
      };

      console.log(
        `[GetInteractionStatus] Successfully retrieved interaction status for asset ${assetId}`
      );

      return c.json(response, 200);
    } catch (error) {
      console.error("[GetInteractionStatus] Error:", error);

      return c.json(
        {
          error: "Failed to get interaction status",
          code: "INTERNAL_ERROR",
          details: error instanceof Error ? error.message : "Unknown error",
        },
        500
      );
    }
  }
}
