import { type Context } from "hono";
import { OpenAPIRoute } from "chanfana";

import { ExploreQuerySchema, ExploreResponseSchema } from "../explore.schema";
import { ExploreService, IExploreService } from "../explore.interface";
import { ErrorResponseSchema } from "../../../shared/error-response.schema";
import type { HonoEnv } from "../../../types";

export class GetExploreContentEndpoint extends OpenAPIRoute {
  schema = {
    summary: "获取 Explore 内容",
    description:
      "获取公开的用户生成内容列表，支持分页和过滤。无需认证即可访问。",
    tags: ["Explore"],
    request: {
      query: ExploreQuerySchema,
    },
    responses: {
      "200": {
        description: "成功获取 Explore 内容",
        content: {
          "application/json": {
            schema: ExploreResponseSchema,
          },
        },
      },
      "400": {
        description: "请求参数无效",
        content: {
          "application/json": {
            schema: ErrorResponseSchema,
          },
        },
      },
      "500": {
        description: "服务器内部错误",
        content: {
          "application/json": {
            schema: ErrorResponseSchema,
          },
        },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    try {
      const container = c.get("container");
      const exploreService = container.resolve<ExploreService>(IExploreService);

      const { query } = await this.getValidatedData<typeof this.schema>();

      console.log("[GetExploreContent] Query parameters:", query);

      const result = await exploreService.getExploreContent(query);

      console.log(
        `[GetExploreContent] Successfully retrieved ${result.items.length} items`
      );

      return c.json(result, 200);
    } catch (error) {
      console.error("[GetExploreContent] Error:", error);

      // 检查是否是验证错误
      if (error instanceof Error && error.message.includes("validation")) {
        return c.json(
          {
            error: "Invalid request parameters",
            code: "INVALID_REQUEST",
            details: error.message,
          },
          400
        );
      }

      return c.json(
        {
          error: "Failed to fetch explore content",
          code: "INTERNAL_ERROR",
          details: error instanceof Error ? error.message : "Unknown error",
        },
        500
      );
    }
  }
}
