// Import endpoints
import { GetExploreContentEndpoint } from "./endpoints/get-explore-content";
import { GetInteractionStatusEndpoint } from "./endpoints/get-interaction-status";

import { authMiddleware } from "../../middleware/auth.middleware";

/**
 * Register all explore-related routes
 * @param openapi - The OpenAPI Hono instance
 * @param basePath - The base path for explore routes (e.g., "/api/v1/explore")
 */
export function registerExploreRoutes(openapi: any, basePath: string) {
  // GET /explore - 获取 Explore 内容（无需认证）
  openapi.get(basePath, GetExploreContentEndpoint);

  // GET /explore/:assetId/interaction-status - 获取用户交互状态（需要认证）
  openapi.get(
    `${basePath}/:assetId/interaction-status`,
    authMiddleware,
    GetInteractionStatusEndpoint
  );
}
