import type { InjectionToken } from "tsyringe";
import {
  ExploreQuery,
  ExploreResponse,
  UserInteractionState,
} from "./explore.schema";

// Define the injection token for ExploreService
export const IExploreService: InjectionToken<ExploreService> =
  Symbol("IExploreService");

/**
 * Abstract class representing the contract for an explore service.
 * It defines methods for explore-related operations.
 */
export abstract class ExploreService {
  /**
   * Get explore content with pagination and filtering
   * @param query - Query parameters for filtering and pagination
   * @returns Promise<ExploreResponse> - Paginated list of explore items
   */
  abstract getExploreContent(query: ExploreQuery): Promise<ExploreResponse>;

  /**
   * Get user interaction status for multiple assets
   * @param userId - The user ID
   * @param assetIds - Array of asset IDs to check
   * @returns Promise<Map<string, UserInteractionState>> - Map of asset ID to interaction state
   */
  abstract getUserInteractionStatus(
    userId: string,
    assetIds: string[]
  ): Promise<Map<string, UserInteractionState>>;
}
