# Explore 功能模块

这个模块提供了 Explore 功能，允许用户浏览和发现公开的AI生成内容和用户上传内容。

## 🎯 功能特性

### 核心功能
- **内容浏览** - 浏览公开的图片和视频内容
- **智能过滤** - 按内容类型、来源类型过滤
- **多种排序** - 支持按时间和热门度排序
- **分页查询** - 高效的分页机制
- **用户信息** - 显示内容创建者信息
- **交互状态** - 查询用户的点赞/收藏状态（需认证）

### 查询功能
- **类型过滤** - 按 MIME 类型过滤（如 "image/*", "video/*"）
- **来源过滤** - 按来源类型过滤（AI生成 vs 用户上传）
- **排序选项** - 最新发布、热门程度
- **分页支持** - 支持页码和每页数量控制

## 📊 数据模型

### ExploreItem 接口
```typescript
interface ExploreItem {
  // 基础信息
  id: string;                    // 资产ID
  type: string;                  // MIME类型
  sourceType: "ai_generated" | "user_upload"; // 来源类型
  userId: string;                // 创建者ID
  userDisplayName?: string;      // 创建者显示名称
  userPhotoURL?: string;         // 创建者头像
  
  // 内容信息
  url: string;                   // 内容URL
  thumbnailUrl?: string;         // 缩略图URL
  name?: string;                 // 文件名
  size?: number;                 // 文件大小
  generationPrompt?: string;     // 生成提示词（仅AI生成）
  description?: string;          // 内容描述
  tags?: string[];               // 标签
  
  // 社交统计
  likeCount: number;             // 点赞数
  favoriteCount: number;         // 收藏数
  
  // 时间信息
  createdAt: string;             // 创建时间（ISO字符串）
}
```

### ExploreQuery 参数
```typescript
interface ExploreQuery {
  page?: number;                 // 页码（默认1）
  limit?: number;                // 每页数量（默认20，最大50）
  type?: string;                 // MIME类型过滤
  sourceType?: "ai_generated" | "user_upload" | "all"; // 来源过滤
  sortBy?: "latest" | "popular"; // 排序方式
}
```

## 🔧 API 端点

### 1. 获取 Explore 内容
```
GET /api/v1/explore
```

**查询参数**:
- `page` (可选): 页码，默认 1
- `limit` (可选): 每页数量，默认 20，最大 50
- `type` (可选): MIME 类型过滤，如 "image/*", "video/*"
- `sourceType` (可选): 来源类型，"ai_generated" | "user_upload" | "all"
- `sortBy` (可选): 排序方式，"latest" | "popular"

**响应**:
```json
{
  "items": [ExploreItem],
  "pagination": {
    "currentPage": 1,
    "totalPages": 5,
    "totalItems": 100,
    "itemsPerPage": 20,
    "hasNextPage": true,
    "hasPreviousPage": false
  },
  "filters": {
    "totalCounts": {
      "image": 80,
      "video": 20,
      "total": 100
    }
  }
}
```

### 2. 获取用户交互状态
```
GET /api/v1/explore/:assetId/interaction-status
```

**认证**: 必需（Bearer Token）

**响应**:
```json
{
  "assetId": "asset-123",
  "isLikedByCurrentUser": false,
  "isFavoritedByCurrentUser": true
}
```

## 🏗️ 架构设计

### 服务层
- **ExploreService** - 核心业务逻辑
- **FirestoreExploreService** - Firestore 数据库实现

### 数据层
- 基于现有的 **AssetService** 构建
- 复用 **Asset** 数据模型
- 集成用户信息查询

### API 层
- **GetExploreContentEndpoint** - 获取内容列表
- **GetInteractionStatusEndpoint** - 获取交互状态

## 🔄 与现有系统集成

### Asset 系统集成
- 复用现有的 `Asset` 数据模型
- 使用 `AssetService.getPublicAssets()` 方法
- 支持现有的过滤和分页机制

### 用户系统集成
- 批量查询用户信息以提高性能
- 显示用户显示名称和头像
- 支持用户认证状态查询

### 任务系统集成
- AI 生成任务完成后自动创建 Asset 记录
- Asset 记录包含生成提示词和任务关联信息
- 支持追溯到原始生成任务

## 🚀 使用示例

### 基础查询
```javascript
// 获取最新的 Explore 内容
fetch('/api/v1/explore')

// 获取热门图片内容
fetch('/api/v1/explore?type=image/*&sortBy=popular&limit=30')

// 获取 AI 生成的视频内容
fetch('/api/v1/explore?sourceType=ai_generated&type=video/*')
```

### 认证查询
```javascript
// 获取用户交互状态
fetch('/api/v1/explore/asset-123/interaction-status', {
  headers: {
    'Authorization': 'Bearer YOUR_JWT_TOKEN'
  }
})
```

## 🧪 测试

使用提供的 `api.http` 文件进行 API 测试：

1. 替换 `{{authToken}}` 为有效的 Firebase JWT
2. 替换 `{{assetId}}` 为实际的资产ID
3. 运行各个测试用例

## 🔮 未来扩展

### Phase 3: 社交功能（计划中）
- 点赞/取消点赞功能
- 收藏/取消收藏功能
- 用户交互历史查询
- 社交统计实时更新

### 性能优化
- 热门内容缓存
- 用户信息缓存
- 图片预加载
- CDN 加速

### 高级功能
- 内容推荐算法
- 个性化内容流
- 内容分类和标签
- 搜索功能

## 📝 注意事项

1. **无需认证**: Explore 内容浏览无需用户认证
2. **公开内容**: 只显示标记为公开的内容
3. **性能考虑**: 使用批量查询优化用户信息获取
4. **错误处理**: 完善的错误处理和用户友好的错误信息
5. **扩展性**: 为未来的社交功能预留接口
