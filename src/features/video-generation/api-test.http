### Content Generation API Tests

# Set base URL and auth token
@baseUrl = http://localhost:8787
@authToken = your-auth-token-here

### 1. Create Image Generation Task (New Endpoint)
POST {{baseUrl}}/content-generation/image
Content-Type: application/json
Authorization: Bearer {{authToken}}

{
  "prompt": "A beautiful sunset over the mountains",
  "imageUrls": ["https://example.com/reference.jpg"]
}

### 2. Create Video Generation Task (New Endpoint)
POST {{baseUrl}}/content-generation/video
Content-Type: application/json
Authorization: Bearer {{authToken}}

{
  "prompt": "两个人拥抱的温馨视频",
  "template": "hugging",
  "images": ["https://example.com/image1.jpg"],
  "seed": 42,
  "duration": 5
}

### 3. Get Task Status (Unified Endpoint)
GET {{baseUrl}}/content-generation/task/your-task-id-here?includeProgress=true

### 4. Legacy Image Generation (Backward Compatibility)
POST {{baseUrl}}/image-generation
Content-Type: application/json
Authorization: Bearer {{authToken}}

{
  "prompt": "A beautiful landscape",
  "imageUrls": ["https://example.com/ref.jpg"]
}

### 5. Legacy Video Generation (Under Legacy Prefix)
POST {{baseUrl}}/image-generation/video
Content-Type: application/json
Authorization: Bearer {{authToken}}

{
  "prompt": "一个人在跳舞的视频"
}

### 6. Legacy Task Status (Backward Compatibility)
GET {{baseUrl}}/image-generation/task/your-task-id-here

### 7. Test Invalid Video Request
POST {{baseUrl}}/content-generation/video
Content-Type: application/json
Authorization: Bearer {{authToken}}

{
  "template": "hugging"
  // Missing required prompt
}

### 8. Test Unauthorized Request
POST {{baseUrl}}/content-generation/video
Content-Type: application/json

{
  "prompt": "Test video without auth"
}
