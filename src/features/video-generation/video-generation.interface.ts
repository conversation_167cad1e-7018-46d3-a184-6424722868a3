import type { InjectionToken } from "tsyringe";
import type {
  VideoTask,
  ViduVideoGenerationRequest,
  VideoGenerationResponse,
  RunwayVideoGenerationRequest,
  GetUserVideoTasksResponse,
  GetUserTasksQuery,
} from "./video-generation.schema";

// Define the injection token for VideoGenerationService
export const IVideoGenerationService: InjectionToken<VideoGenerationService> =
  Symbol("IVideoGenerationService");

/**
 * Abstract class representing the contract for a video generation service.
 * Handles video generation through multiple providers.
 */
export abstract class VideoGenerationService {
  // Video generation methods - provider-specific
  abstract createViduVideoGenerationTask(
    userId: string,
    requestData: ViduVideoGenerationRequest,
    waitUntil: (promise: Promise<any>) => void
  ): Promise<VideoGenerationResponse>;

  // Future video generation providers can be added here:
  abstract createRunwayVideoGenerationTask(
    userId: string,
    requestData: RunwayVideoGenerationRequest,
    waitUntil: (promise: Promise<any>) => void
  ): Promise<VideoGenerationResponse>;

  // Video task management methods
  abstract getUserVideoTasks(
    userId: string,
    query?: GetUserTasksQuery
  ): Promise<GetUserVideoTasksResponse>;

  abstract getVideoTaskById(taskId: string): Promise<VideoTask | null>;

  abstract getVideoTasksByBatchId(batchId: string): Promise<VideoTask[]>;
}
