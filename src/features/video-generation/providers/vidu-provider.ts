import type { TaskVideoOutput, TaskProvider } from "./provider.interface";

/**
 * 302.ai Vidu API request interface
 */
interface ViduRequest {
  template: string;
  images: string[];
  prompt: string;
  seed: string;
}

/**
 * 302.ai Vidu API response interfaces
 */
interface ViduSubmitResponse {
  task_id: string;
  state: string;
  template: string;
  prompt: string;
  images: string[];
  seed: number;
  created_at: string;
}

interface ViduStatusResponse {
  state: "created" | "processing" | "success" | "failed";
  err_code: string;
  creations: Array<{
    id: string;
    url: string;
    cover_url: string;
    video: {
      duration: number;
      fps: number;
      resolution: {
        width: number;
        height: number;
      };
    };
  }>;
  id: string;
}

/**
 * 302.ai Vidu provider implementation
 */
export class ViduProvider
  implements TaskProvider<ViduRequest, TaskVideoOutput>
{
  readonly providerId = "302ai_vidu";

  private readonly apiKey: string;
  private readonly baseUrl = "https://api.302.ai/vidu/ent/v2";

  constructor(apiKey: string) {
    this.apiKey = apiKey;
    console.log(`[ViduProvider] Initialized with API key`);
  }

  async submitTask(input: ViduRequest): Promise<{
    providerTaskId: string;
    estimatedDuration?: number;
  }> {
    try {
      console.log(`[ViduProvider] Submitting task with input:`, input);

      const response = await fetch(`${this.baseUrl}/template2video`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(input),
      });

      if (!response.ok) {
        throw new Error(
          `Vidu API error: ${response.status} ${response.statusText}`
        );
      }

      const data: ViduSubmitResponse = await response.json();
      console.log(`[ViduProvider] Task submitted successfully:`, data);

      return {
        providerTaskId: data.task_id,
        estimatedDuration: 300, // 5 minutes estimated
      };
    } catch (error) {
      console.error(`[ViduProvider] Failed to submit task:`, error);
      throw error;
    }
  }

  async checkTaskStatus(providerTaskId: string): Promise<{
    status: "pending" | "processing" | "completed" | "failed";
    progress?: number;
    result?: TaskVideoOutput;
    error?: string;
  }> {
    try {
      console.log(`[ViduProvider] Checking status for task: ${providerTaskId}`);

      const response = await fetch(
        `${this.baseUrl}/tasks/${providerTaskId}/creations`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${this.apiKey}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error(
          `Vidu API error: ${response.status} ${response.statusText}`
        );
      }

      const data: ViduStatusResponse = await response.json();
      console.log(`[ViduProvider] Status check result:`, data);

      const status = this.mapViduStatusToStandard(data.state);

      if (
        status === "completed" &&
        data.creations &&
        data.creations.length > 0
      ) {
        return {
          status,
          progress: 100,
          result: this.normalizeOutput(data),
        };
      }

      if (status === "failed") {
        return {
          status,
          error: data.err_code || "Unknown error from Vidu API",
        };
      }

      return {
        status,
        progress: status === "processing" ? 50 : 10, // Rough progress estimation
      };
    } catch (error) {
      console.error(`[ViduProvider] Failed to check task status:`, error);
      return {
        status: "failed",
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  normalizeInput(standardInput: Record<string, any>): ViduRequest {
    return {
      template: standardInput.template || "hugging",
      images: standardInput.images || [],
      prompt: standardInput.prompt,
      seed: (standardInput.seed || 0).toString(),
    };
  }

  normalizeOutput(providerResponse: ViduStatusResponse): TaskVideoOutput {
    if (providerResponse.creations && providerResponse.creations.length > 0) {
      return {
        data: providerResponse,
        coverImageUrl: providerResponse.creations[0].cover_url,
        videoUrl: providerResponse.creations[0].url,
      };
    }
    return {
      data: providerResponse,
    };
  }

  private mapViduStatusToStandard(
    viduStatus: string
  ): "pending" | "processing" | "completed" | "failed" {
    switch (viduStatus) {
      case "created":
        return "pending";
      case "processing":
        return "processing";
      case "success":
        return "completed";
      case "failed":
        return "failed";
      default:
        console.warn(
          `[ViduProvider] Unknown status: ${viduStatus}, defaulting to pending`
        );
        return "pending";
    }
  }
}
