/**
 * Abstract interface for task providers (e.g., OpenAI, 302.ai, etc.)
 * Each provider implements this interface to handle specific API integrations
 */

/**
 * Base video output interface that all providers must include
 */
export interface TaskVideoOutput {
  coverImageUrl?: string;
  videoUrl?: string;
  data?: Record<string, any>;
}

export interface TaskProvider<TInput = any, TOutput = TaskVideoOutput> {
  readonly providerId: string;

  /**
   * Submit a task to the external provider
   * @param input - Provider-specific input parameters
   * @returns Provider task ID and request data
   */
  submitTask(input: TInput): Promise<{
    providerTaskId: string;
    estimatedDuration?: number; // Estimated completion time in seconds
  }>;

  /**
   * Check the status of a task with the external provider
   * @param providerTaskId - The external provider's task ID
   * @returns Current status and result if completed
   */
  checkTaskStatus(providerTaskId: string): Promise<{
    status: "pending" | "processing" | "completed" | "failed";
    progress?: number;
    result?: TaskVideoOutput;
    error?: string;
  }>;

  /**
   * Convert standardized input to provider-specific format
   * @param standardInput - Standardized input parameters
   * @returns Provider-specific input format
   */
  normalizeInput(standardInput: Record<string, any>): TInput;

  /**
   * Convert provider response to standardized output format
   * @param providerResponse - Raw provider response
   * @returns Standardized output format
   */
  normalizeOutput(providerResponse: any): TOutput;
}
