import { inject, InjectionToken, singleton } from "tsyringe";
import type { TaskProvider } from "./provider.interface";
import { ViduProvider } from "./vidu-provider";
import {
  IEnvService,
  type EnvService,
} from "../../../infrastructure/env/env-service.interface";

/**
 * Provider selection strategy interface
 */
interface ProviderSelectionStrategy {
  selectProvider(
    availableProviders: TaskProvider[],
    input?: Record<string, any>
  ): TaskProvider;
}

/**
 * Default provider selection strategy - simple round-robin or first available
 */
class DefaultProviderSelectionStrategy implements ProviderSelectionStrategy {
  selectProvider(
    availableProviders: TaskProvider[],
    input?: Record<string, any>
  ): TaskProvider {
    // For now, just return the first available provider
    // In the future, this can be enhanced with:
    // - Load balancing
    // - Cost optimization
    // - Speed optimization
    // - User preferences
    // - A/B testing

    if (availableProviders.length === 0) {
      throw new Error("No providers available");
    }

    return availableProviders[0];
  }
}

export const IProviderRouter: InjectionToken<ProviderRouter> =
  Symbol("IProviderRouter");

/**
 * Provider router that manages all available providers and handles provider selection
 */
@singleton()
export class ProviderRouter {
  private providers = new Map<string, TaskProvider>();
  private selectionStrategy: ProviderSelectionStrategy;

  constructor(@inject(IEnvService) private envService: EnvService) {
    this.selectionStrategy = new DefaultProviderSelectionStrategy();
    this.initializeProviders();
  }

  private initializeProviders(): void {
    const viduProvider = new ViduProvider(
      this.envService.getBindings().VIDU_API_KEY
    );
    this.registerProvider(viduProvider);

    // TODO: Future providers can be added here
    // this.registerProvider(new RunwayMLProvider());
    // this.registerProvider(new PikaLabsProvider());
  }

  /**
   * Register a new provider
   */
  registerProvider(provider: TaskProvider): void {
    console.log(
      `[ProviderRouter] Registering provider: ${provider.providerId}`
    );

    this.providers.set(provider.providerId, provider);
  }

  /**
   * Intelligently select the best provider
   * Frontend doesn't need to know about providers - this is handled automatically
   */
  selectProvider(input?: Record<string, any>): TaskProvider {
    const availableProviders = Array.from(this.providers.values());

    if (availableProviders.length === 0) {
      throw new Error("No providers available");
    }

    const selectedProvider = this.selectionStrategy.selectProvider(
      availableProviders,
      input
    );

    console.log(
      `[ProviderRouter] Selected provider: ${selectedProvider.providerId}`
    );

    return selectedProvider;
  }

  /**
   * Get a specific provider by ID (used for polling existing tasks)
   */
  getProvider(providerId: string): TaskProvider {
    const provider = this.providers.get(providerId);
    if (!provider) {
      throw new Error(`Provider ${providerId} not found`);
    }
    return provider;
  }

  /**
   * Get all registered providers
   */
  getAllProviders(): TaskProvider[] {
    return Array.from(this.providers.values());
  }

  /**
   * Check if a provider is available
   */
  hasProvider(providerId: string): boolean {
    return this.providers.has(providerId);
  }

  /**
   * Set a custom provider selection strategy
   */
  setSelectionStrategy(strategy: ProviderSelectionStrategy): void {
    this.selectionStrategy = strategy;
  }
}

/**
 * Advanced provider selection strategies (for future use)
 */

/**
 * Cost-optimized provider selection
 */
export class CostOptimizedStrategy implements ProviderSelectionStrategy {
  private providerCosts = new Map<string, number>([
    ["302ai_vidu", 0.1], // Example costs per request
    ["runwayml", 0.2],
    ["pika_labs", 0.15],
  ]);

  selectProvider(availableProviders: TaskProvider[]): TaskProvider {
    return availableProviders.reduce((cheapest, current) => {
      const currentCost =
        this.providerCosts.get(current.providerId) || Infinity;
      const cheapestCost =
        this.providerCosts.get(cheapest.providerId) || Infinity;
      return currentCost < cheapestCost ? current : cheapest;
    });
  }
}

/**
 * Load-balanced provider selection
 */
export class LoadBalancedStrategy implements ProviderSelectionStrategy {
  private requestCounts = new Map<string, number>();

  selectProvider(availableProviders: TaskProvider[]): TaskProvider {
    // Select provider with least requests
    const provider = availableProviders.reduce((leastUsed, current) => {
      const currentCount = this.requestCounts.get(current.providerId) || 0;
      const leastUsedCount = this.requestCounts.get(leastUsed.providerId) || 0;
      return currentCount < leastUsedCount ? current : leastUsed;
    });

    // Increment request count
    this.requestCounts.set(
      provider.providerId,
      (this.requestCounts.get(provider.providerId) || 0) + 1
    );

    return provider;
  }
}
