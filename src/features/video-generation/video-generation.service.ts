import "reflect-metadata"; // Needed for tsyringe
import { singleton, inject } from "tsyringe";

import type {
  VideoTask,
  GetUserVideoTasksResponse,
  GetUserTasksQuery,
  PaginationMeta,
  ViduVideoGenerationRequest,
  RunwayVideoGenerationRequest,
  VideoGenerationResponse,
} from "./video-generation.schema";
import { v4 as uuidv4 } from "uuid";

import { VideoGenerationService as VideoGenerationServiceInterface } from "./video-generation.interface";

import {
  IDbService,
  type DbService,
} from "../../infrastructure/db/db-service.interface";
import type { FirestoreClient } from "firebase-rest-firestore";
import {
  EnvService,
  IEnvService,
} from "../../infrastructure/env/env-service.interface";
import { IProviderRouter, ProviderRouter } from "./providers/provider-router";
import type { TaskProvider } from "./providers/provider.interface";
import {
  IVideoGenerationWorkflowService,
  VideoGenerationWorkflowService,
} from "../../workflow/video-generation-workflow.interface";

const VIDEO_TASK_COLLECTION = "video-generation-tasks";

@singleton()
export class VideoGenerationService implements VideoGenerationServiceInterface {
  private firestore: FirestoreClient;

  constructor(
    @inject(IDbService) private dbService: DbService,
    @inject(IEnvService) private envService: EnvService,
    @inject(IProviderRouter) private providerRouter: ProviderRouter,
    @inject(IVideoGenerationWorkflowService)
    private workflowService: VideoGenerationWorkflowService
  ) {
    this.firestore = this.dbService.getFirestoreInstance();
  }

  /**
   * Convert Firestore document data to VideoTask object
   * @param data - The raw data from Firestore document
   * @returns VideoTask object
   */
  private mapFirestoreDataToVideoTask(data: any): VideoTask {
    return {
      taskId: data?.taskId,
      userId: data?.userId,
      status: data?.status,
      errorMessage: data?.errorMessage,
      batchId: data?.batchId,
      progress: data?.progress,
      provider: data?.provider,
      providerTaskId: data?.providerTaskId,
      inputData: data?.inputData,
      resultData: data?.resultData,
      createdAt: data?.createdAt,
      updatedAt: data?.updatedAt,
      inputImageUrl: data?.inputImageUrl,
      coverImageUrl: data?.coverImageUrl,
      videoUrl: data?.videoUrl,
    } as VideoTask;
  }

  /**
   * Core method for creating video generation tasks
   * Handles common logic: task creation, storage, workflow triggering
   * @param userId - The ID of the user requesting the generation
   * @param provider - The provider instance to use for generation
   * @param inputData - Provider-specific input parameters (including prompt)
   * @param inputImageUrl - Optional input image URL for image-to-video generation
   * @param waitUntil - CF Worker waitUntil function for background processing
   * @returns The ID of the created task
   */
  protected async createVideoGenerationTaskCore(
    userId: string,
    provider: TaskProvider,
    inputData: Record<string, any>,
    inputImageUrl: string | undefined,
    _waitUntil: (promise: Promise<any>) => void
  ): Promise<VideoGenerationResponse> {
    const taskId = uuidv4();
    const now = new Date();

    console.log(
      `[VideoGeneration] Creating task ${taskId} for user ${userId} with provider ${provider.providerId}`
    );
    console.log(`[VideoGeneration] Input data:`, inputData);
    if (inputImageUrl) {
      console.log(`[VideoGeneration] Input image URL:`, inputImageUrl);
    }

    try {
      // 1. Create task record in Firestore
      const newTaskData: Omit<VideoTask, "createdAt" | "updatedAt"> & {
        createdAt: Date;
        updatedAt: Date;
      } = {
        taskId,
        userId,
        status: "pending",
        provider: provider.providerId,
        progress: 0,
        inputData,
        inputImageUrl,
        createdAt: now,
        updatedAt: now,
      };

      await this.firestore
        .collection(VIDEO_TASK_COLLECTION)
        .doc(taskId)
        .update(newTaskData);

      console.log(`[VideoGeneration] Task ${taskId} created in Firestore`);

      // 2. Start video generation workflow
      const bindings = this.envService.getBindings();
      const isLocalhost = bindings.ENVIRONMENT === "localhost";

      if (isLocalhost) {
        // For localhost/development environment, directly call the workflow service
        console.log(
          `[VideoGeneration] Running in localhost/development mode, executing workflow directly`
        );

        // Execute workflow in background without blocking the response
        _waitUntil(
          this.workflowService.executeWorkflow(taskId).catch((error) => {
            console.error(
              `[VideoGeneration] Workflow execution failed for task ${taskId}:`,
              error
            );
          })
        );
      } else {
        // For production environment, use Cloudflare Workflows
        const { VIDEO_GENERATION_WORKFLOW } = bindings;

        if (VIDEO_GENERATION_WORKFLOW) {
          const workflowInstance = await VIDEO_GENERATION_WORKFLOW.create({
            id: taskId + "-" + Math.random().toString(36).substring(2, 5),
            params: { taskId },
          });
          console.log(
            `[VideoGeneration] Started Cloudflare workflow: ${workflowInstance.id}`
          );
        } else {
          console.warn(
            `[VideoGeneration] VIDEO_GENERATION_WORKFLOW not available, task will remain pending`
          );
        }
      }

      return { taskId };
    } catch (error) {
      console.error(
        `[VideoGeneration] Failed to create task ${taskId}:`,
        error
      );
      throw new Error("Failed to create video generation task.");
    }
  }

  /**
   * Creates a new Vidu video generation task
   * @param userId - The ID of the user requesting the generation
   * @param requestData - The validated Vidu-specific request data
   * @param waitUntil - CF Worker waitUntil function for background processing
   * @returns The ID of the created task
   */
  async createViduVideoGenerationTask(
    userId: string,
    requestData: ViduVideoGenerationRequest,
    waitUntil: (promise: Promise<any>) => void
  ): Promise<VideoGenerationResponse> {
    const provider = this.providerRouter.getProvider("302ai_vidu");
    // Extract the first image URL as inputImageUrl if imageUrls array is provided
    const inputImageUrl = requestData.imageUrls?.[0];
    return this.createVideoGenerationTaskCore(
      userId,
      provider,
      {
        prompt: requestData.prompt,
        template: requestData.template,
        images: requestData.imageUrls,
        seed: requestData.seed || 0,
      },
      inputImageUrl,
      waitUntil
    );
  }

  /**
   * Creates a new Runway video generation task (example for future implementation)
   */
  async createRunwayVideoGenerationTask(
    userId: string,
    requestData: RunwayVideoGenerationRequest,
    waitUntil: (promise: Promise<any>) => void
  ): Promise<VideoGenerationResponse> {
    const provider = this.providerRouter.getProvider("runwayml");
    // Use reference_image as inputImageUrl for Runway
    const inputImageUrl = requestData.reference_image;
    return this.createVideoGenerationTaskCore(
      userId,
      provider,
      {
        prompt: requestData.prompt,
        motion_level: requestData.motion_level,
        aspect_ratio: requestData.aspect_ratio,
        style: requestData.style,
        reference_image: requestData.reference_image,
      },
      inputImageUrl,
      waitUntil
    );
  }

  /**
   * Retrieves a specific video task by its ID.
   * @param taskId - The ID of the video task to retrieve.
   * @returns A promise that resolves to a VideoTask object or null if not found.
   */
  public async getVideoTaskById(taskId: string): Promise<VideoTask | null> {
    try {
      const doc = await this.firestore
        .collection(VIDEO_TASK_COLLECTION)
        .doc(taskId)
        .get();

      if (!doc.exists) {
        return null;
      }

      const data = doc.data();
      return this.mapFirestoreDataToVideoTask(data);
    } catch (error) {
      console.error(`Failed to retrieve video task ${taskId}:`, error);
      throw new Error("Failed to retrieve video task.");
    }
  }

  /**
   * Retrieves video tasks for a given user with pagination support and optional batch filtering.
   */
  public async getUserVideoTasks(
    userId: string,
    query?: GetUserTasksQuery
  ): Promise<GetUserVideoTasksResponse> {
    try {
      const limit = query?.limit || 20;
      const page = query?.page || 1;
      const batchId = query?.batchId;
      const offset = (page - 1) * limit;

      // Build base query for counting
      let countQuery = this.firestore
        .collection(VIDEO_TASK_COLLECTION)
        .where("userId", "==", userId);

      if (batchId) {
        countQuery = countQuery.where("batchId", "==", batchId);
      }

      const totalSnapshot = await countQuery.get();
      const totalItems = totalSnapshot.size;
      const totalPages = Math.ceil(totalItems / limit);

      // Build query for paginated results
      let query_builder = this.firestore
        .collection(VIDEO_TASK_COLLECTION)
        .where("userId", "==", userId);

      if (batchId) {
        query_builder = query_builder.where("batchId", "==", batchId);
      }

      query_builder = query_builder.orderBy("createdAt", "desc");

      if (offset > 0) {
        query_builder = query_builder.offset(offset);
      }
      query_builder = query_builder.limit(limit);

      const snapshot = await query_builder.get();

      const tasks = snapshot.docs.map((doc) => {
        const data = doc.data();
        return this.mapFirestoreDataToVideoTask(data);
      });

      const pagination: PaginationMeta = {
        currentPage: page,
        totalPages,
        totalItems,
        itemsPerPage: limit,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      };

      return { tasks, pagination };
    } catch (error) {
      console.error(
        `Failed to retrieve video tasks for user ${userId}:`,
        error
      );
      throw new Error("Failed to retrieve user video tasks.");
    }
  }

  /**
   * Retrieves all video tasks that belong to a specific batch.
   */
  public async getVideoTasksByBatchId(batchId: string): Promise<VideoTask[]> {
    try {
      const snapshot = await this.firestore
        .collection(VIDEO_TASK_COLLECTION)
        .where("batchId", "==", batchId)
        .orderBy("createdAt", "desc")
        .get();

      const tasks = snapshot.docs.map((doc) => {
        const data = doc.data();
        return this.mapFirestoreDataToVideoTask(data);
      });

      return tasks;
    } catch (error) {
      console.error(
        `Failed to retrieve video tasks for batch ${batchId}:`,
        error
      );
      throw new Error("Failed to retrieve batch video tasks.");
    }
  }
}
