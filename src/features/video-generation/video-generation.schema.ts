import { z } from "zod";

/**
 * @Warning deniffer: only for type inference
 */

export const GenericErrorSchema = z.object({ error: z.string() });

export const TaskStatusSchema = z.enum([
  "pending",
  "processing",
  "succeeded",
  "failed",
  "deleted",
]);

export const VideoTaskSchema = z.object({
  taskId: z.string().uuid(),
  userId: z.string(),
  status: TaskStatusSchema,
  errorMessage: z.string().optional(),
  batchId: z.string().optional(),
  progress: z.number().min(0).max(100).optional(),

  provider: z.string().optional(), // Provider used (e.g., "302ai_vidu", "runwayml")
  providerTaskId: z.string().optional(), // External provider's task ID
  inputData: z.record(z.any()).optional(), // Additional input parameters (JSON)
  resultData: z.record(z.any()).optional(), // Additional result data (JSON)

  inputImageUrl: z.string().url().optional(), // input: Input image URL for image-to-video generation
  coverImageUrl: z.string().optional(), // result: Cover image URL
  videoUrl: z.string().optional(), // result: Video URL

  // Representing Firestore Timestamp. Use z.any() for schema definition ease,
  // but actual validation/transformation might be needed in the service layer.
  createdAt: z.date(),
  updatedAt: z.date(),
});

// Video generation schemas - Provider-specific

// Vidu video generation schema
export const ViduVideoGenerationRequestSchema = z.object({
  prompt: z.string().min(1, { message: "Prompt cannot be empty." }).openapi({
    example:
      "Video content\\n画面中的两个主体转向彼此，并开始拥抱# 要求\\n将Motion Level设置为‘Large’",
    description: "Video generation prompt",
  }),
  template: z.string().optional().openapi({
    example: "hugging",
    description: "Video template (e.g., hugging, dancing)",
  }),
  imageUrls: z
    .array(z.string().url())
    .optional()
    .openapi({
      example: [
        "https://prod-ss-images.s3.cn-northwest-1.amazonaws.com.cn/vidu-maas/scene-template/hug.jpeg",
      ],
      description: "Input images for video generation",
    }),
  seed: z.number().optional().openapi({
    example: 0,
    description: "Random seed for reproducible results",
  }),
});

// Runway video generation schema (example for future implementation)
export const RunwayVideoGenerationRequestSchema = z.object({
  prompt: z.string().min(1, { message: "Prompt cannot be empty." }).openapi({
    example: "A person walking through a futuristic city",
    description: "Video generation prompt",
  }),
  motion_level: z.enum(["low", "medium", "high"]).optional().openapi({
    example: "medium",
    description: "Motion intensity level",
  }),
  aspect_ratio: z.enum(["16:9", "9:16", "1:1"]).optional().openapi({
    example: "16:9",
    description: "Video aspect ratio",
  }),
  style: z.string().optional().openapi({
    example: "cinematic",
    description: "Video style preset",
  }),
  reference_image: z.string().url().optional().openapi({
    example: "https://example.com/reference.jpg",
    description: "Reference image for video generation",
  }),
  duration: z.number().min(1).max(10).optional().openapi({
    example: 4,
    description: "Video duration in seconds (1-10)",
  }),
});

export const VideoGenerationResponseSchema = z.object({
  taskId: z.string().uuid(),
});

export const ThirdPartyApiResponseSchema = z.object({
  success: z.boolean(),
  data: z.object({
    videoUrls: z.array(z.string().url()),
    thumbnailUrls: z.array(z.string().url()).optional(),
  }),
  message: z.string().optional(),
});

export const GetUserTasksRequestSchema = z.object({
  userId: z
    .string()
    .min(1, { message: "User ID cannot be empty." })
    .openapi({ example: "bGl3YXcxe3YsZeSSDvlG86kA4wj1" }),
});

export const GetUserTasksQuerySchema = z.object({
  batchId: z.string().optional().openapi({
    example: "batch-2024-01-15-001",
    description: "Batch identifier to retrieve all tasks in the batch",
  }),
  limit: z
    .string()
    .optional()
    .transform((val) => (val ? parseInt(val, 10) : 20))
    .refine((val) => val > 0 && val <= 100, {
      message: "Limit must be between 1 and 100",
    })
    .openapi({
      example: "20",
      description: "Number of tasks to return (1-100, default: 20)",
    }),
  page: z
    .string()
    .optional()
    .transform((val) => (val ? parseInt(val, 10) : 1))
    .refine((val) => val > 0, {
      message: "Page must be greater than 0",
    })
    .openapi({
      example: "1",
      description: "Page number (starting from 1, default: 1)",
    }),
});

export const PaginationMetaSchema = z.object({
  currentPage: z.number(),
  totalPages: z.number(),
  totalItems: z.number(),
  itemsPerPage: z.number(),
  hasNextPage: z.boolean(),
  hasPreviousPage: z.boolean(),
});

export const GetUserVideoTasksResponseSchema = z.object({
  tasks: z.array(VideoTaskSchema),
  pagination: PaginationMetaSchema,
});

export const GetVideoTaskByIdRequestSchema = z.object({
  taskId: z
    .string()
    .uuid({ message: "Task ID must be a valid UUID." })
    .openapi({
      example: "52df2e8e-e72d-455a-82fb-464fc7504040",
    }),
});

export const GetVideoTaskByIdResponseSchema = VideoTaskSchema;

export const GetVideoTasksByBatchIdRequestSchema = z.object({
  batchId: z.string().min(1, { message: "Batch ID cannot be empty." }).openapi({
    example: "batch-2024-01-15-001",
    description: "Batch identifier to retrieve all tasks in the batch",
  }),
});

export const GetVideoTasksByBatchIdResponseSchema = z.array(VideoTaskSchema);

// Type exports
export type ViduVideoGenerationRequest = z.infer<
  typeof ViduVideoGenerationRequestSchema
>;
export type RunwayVideoGenerationRequest = z.infer<
  typeof RunwayVideoGenerationRequestSchema
>;

export type VideoGenerationResponse = z.infer<
  typeof VideoGenerationResponseSchema
>;
export type TaskStatus = z.infer<typeof TaskStatusSchema>;

export type GetUserTasksRequest = z.infer<typeof GetUserTasksRequestSchema>;
export type GetUserTasksQuery = z.infer<typeof GetUserTasksQuerySchema>;
export type PaginationMeta = z.infer<typeof PaginationMetaSchema>;
export type GetUserVideoTasksResponse = z.infer<
  typeof GetUserVideoTasksResponseSchema
>;
export type GetVideoTaskByIdRequest = z.infer<
  typeof GetVideoTaskByIdRequestSchema
>;
export type GetVideoTaskByIdResponse = z.infer<
  typeof GetVideoTaskByIdResponseSchema
>;
export type GetVideoTasksByBatchIdRequest = z.infer<
  typeof GetVideoTasksByBatchIdRequestSchema
>;
export type GetVideoTasksByBatchIdResponse = z.infer<
  typeof GetVideoTasksByBatchIdResponseSchema
>;

export type VideoTask = z.infer<typeof VideoTaskSchema>;
