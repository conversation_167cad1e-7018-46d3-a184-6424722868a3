import { CreateViduVideoGenerationTask } from "./endpoints/create-vidu-video-generation-task";
import { GetTaskByIdEndpoint } from "./endpoints/get-task-by-id";
import { GetUserTasksHistoryEndpoint } from "./endpoints/get-user-tasks-history";
import { GetTasksByBatchIdEndpoint } from "./endpoints/get-tasks-by-batch-id";
import { CheckVideoQuotaEndpoint } from "./endpoints/check-quota";
import { authMiddleware } from "../../middleware/auth.middleware";

/**
 * Register all video generation routes
 * Supports multiple video generation providers through unified endpoints
 */
export function registerVideoGenerationRoutes(
  openapi: any,
  routePrefix: string = "/video-generation"
) {
  const base = routePrefix.startsWith("/") ? routePrefix : `/${routePrefix}`;

  // Video generation routes
  openapi.post(
    `${base}/video/template`,
    authMiddleware,
    CreateViduVideoGenerationTask
  );

  // Task management routes for video generation
  openapi.get(`${base}/task/:taskId`, GetTaskByIdEndpoint);
  openapi.get(`${base}/history`, authMiddleware, GetUserTasksHistoryEndpoint);
  openapi.get(`${base}/batch/:batchId`, GetTasksByBatchIdEndpoint);

  // Quota management routes
  openapi.get(`${base}/quota`, authMiddleware, CheckVideoQuotaEndpoint);
}
