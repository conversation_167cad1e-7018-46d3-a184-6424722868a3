import { type Context } from "hono";

import { OpenAPIRoute } from "chanfana";
import { z } from "zod";
import {
  GetVideoTasksByBatchIdRequestSchema,
  GetVideoTasksByBatchIdResponseSchema,
} from "../video-generation.schema";
import type { HonoEnv } from "../../../types";
import {
  IVideoGenerationService,
  VideoGenerationService,
} from "../video-generation.interface";

const GenericErrorSchema = z.object({
  error: z.string(),
});

export class GetTasksByBatchIdEndpoint extends OpenAPIRoute {
  schema = {
    summary: "Get video generation tasks by batch ID",
    description:
      "Retrieves all video generation tasks that belong to a specific batch.",
    tags: ["Video Generation"],
    request: {
      params: GetVideoTasksByBatchIdRequestSchema,
    },
    responses: {
      "200": {
        description: "Successfully retrieved batch tasks.",
        content: {
          "application/json": {
            schema: GetVideoTasksByBatchIdResponseSchema,
          },
        },
      },
      "400": {
        description: "Invalid batch ID.",
        content: {
          "application/json": {
            schema: GenericErrorSchema,
          },
        },
      },
      "404": {
        description: "No tasks found for the specified batch ID.",
        content: {
          "application/json": {
            schema: GenericErrorSchema,
          },
        },
      },
      "500": {
        description: "Failed to retrieve batch tasks.",
        content: {
          "application/json": {
            schema: GenericErrorSchema,
          },
        },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    const { params } = await this.getValidatedData<typeof this.schema>();

    const container = c.get("container");
    const videoGenerationService = container.resolve<VideoGenerationService>(
      IVideoGenerationService
    );

    try {
      const tasks = await videoGenerationService.getVideoTasksByBatchId(
        params.batchId
      );

      if (tasks.length === 0) {
        return c.json(
          { error: "No tasks found for the specified batch ID." },
          404
        );
      }

      return c.json(tasks, 200);
    } catch (error: any) {
      console.error(
        `Error retrieving video tasks for batch ${params.batchId}:`,
        error
      );
      return c.json({ error: "Failed to retrieve batch tasks." }, 500);
    }
  }
}
