import { <PERSON>APIRoute } from "chanfana";
import { z } from "zod";
import type { Context } from "hono";

import {
  VideoGenerationResponseSchema,
  ViduVideoGenerationRequestSchema,
} from "../video-generation.schema";
import {
  IVideoGenerationService,
  type VideoGenerationService,
} from "../video-generation.interface";
import {
  IQuotaService,
  type QuotaService,
} from "../../../infrastructure/quota/quota.interface";
import {
  IAnalyticsService,
  type AnalyticsService,
} from "../../../infrastructure/analytics/analytics.interface";
import { getFirebaseToken } from "@hono/firebase-auth";
import { HonoEnv } from "../../../types";

/**
 * API endpoint for creating video generation tasks
 * Frontend calls this endpoint without needing to know about providers
 */
export class CreateViduVideoGenerationTask extends OpenAPIRoute {
  schema = {
    tags: ["Video Generation"],
    summary: "Create a video generation task",
    description:
      "Creates a new video generation task and returns the task ID for tracking progress",
    security: [
      {
        BearerAuth: [],
      },
    ],
    request: {
      body: {
        content: {
          "application/json": {
            schema: ViduVideoGenerationRequestSchema,
          },
        },
      },
    },
    responses: {
      200: {
        description: "Video generation task created successfully",
        content: {
          "application/json": {
            schema: VideoGenerationResponseSchema,
          },
        },
      },
      400: {
        description: "Invalid request data",
        content: {
          "application/json": {
            schema: z.object({
              error: z.string(),
              details: z.any().optional(),
            }),
          },
        },
      },
      401: {
        description: "Unauthorized - missing or invalid authentication",
        content: {
          "application/json": {
            schema: z.object({
              error: z.string(),
            }),
          },
        },
      },
      500: {
        description: "Internal server error",
        content: {
          "application/json": {
            schema: z.object({
              error: z.string(),
            }),
          },
        },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    try {
      // Get Firebase token for user authentication
      const firebaseToken = getFirebaseToken(c);
      if (!firebaseToken) {
        return c.json({ error: "Authentication required" }, 401);
      }

      const userId = firebaseToken.uid;

      // Parse and validate request body
      const requestData = await this.getValidatedData<typeof this.schema>();

      console.log(
        `[CreateVideoGenerationTask] Request from user ${userId}:`,
        requestData.body
      );

      const container = c.get("container");

      // Get services
      const videoGenerationService = container.resolve<VideoGenerationService>(
        IVideoGenerationService
      );
      const quotaService = container.resolve<QuotaService>(IQuotaService);
      const analyticsService =
        container.resolve<AnalyticsService>(IAnalyticsService);

      // Record the API call attempt
      const endpointName = "create-vidu-video-generation-task";
      const featureName = "video-generation";
      await analyticsService.recordCall(endpointName, featureName, userId);

      // Check quota before processing
      const quota = await quotaService.checkQuota(userId, "video-generation");
      if (quota.remaining <= 0) {
        // Record failure
        await analyticsService.recordFailure(endpointName, featureName, userId);

        return c.json(
          {
            error: `Daily quota exceeded. Please try again tomorrow.`,
            quota,
          },  
          429
        ); // 429 Too Many Requests
      }

      // Consume quota before processing
      await quotaService.consumeQuota(
        userId,
        "video-generation",
        1
      );

      // Create Vidu video generation task
      const result = await videoGenerationService.createViduVideoGenerationTask(
        userId,
        requestData.body,
        c.executionCtx.waitUntil.bind(c.executionCtx)
      );

      console.log(`[CreateVideoGenerationTask] Task created:`, result);

      // Record success
      await analyticsService.recordSuccess(endpointName, featureName, userId);

      return c.json(result, 200);
    } catch (error: any) {
      console.error("[CreateVideoGenerationTask] Error:", error);

      // Try to record failure in analytics
      try {
        const container = c.get("container");
        const analyticsService =
          container.resolve<AnalyticsService>(IAnalyticsService);
        const endpointName = "create-vidu-video-generation-task";
        const featureName = "video-generation";
        const firebaseToken = getFirebaseToken(c);
        const userId = firebaseToken?.uid;
        await analyticsService.recordFailure(endpointName, featureName, userId);
      } catch (analyticsError) {
        console.error(
          "[CreateVideoGenerationTask] Failed to record analytics:",
          analyticsError
        );
      }

      // Handle validation errors
      if (error.name === "ZodError") {
        return c.json(
          {
            error: "Invalid request data",
            details: error.errors,
          },
          400
        );
      }

      // If it's a quota error, return specific error
      if (error.message?.includes("quota exceeded")) {
        return c.json({ error: error.message }, 429);
      }

      // Handle service errors
      return c.json(
        {
          error: error.message || "Failed to create video generation task",
        },
        500
      );
    }
  }
}
