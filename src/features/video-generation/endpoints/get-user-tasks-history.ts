import { type Context } from "hono";
import { OpenAPIRoute } from "chanfana";
import { getFirebaseToken } from "@hono/firebase-auth";

import {
  GenericErrorSchema,
  GetUserTasksQuerySchema,
  GetUserVideoTasksResponseSchema,
} from "../video-generation.schema";

import type { HonoEnv } from "../../../types";
import {
  IVideoGenerationService,
  VideoGenerationService,
} from "../video-generation.interface";

export class GetUserTasksHistoryEndpoint extends OpenAPIRoute {
  schema = {
    summary: "Get user's video generation task history",
    description:
      "Retrieves a paginated list of video generation tasks for the authenticated user. Optionally filter by batch ID.",
    tags: ["Video Generation"],
    security: [
      {
        BearerAuth: [],
      },
    ],
    request: {
      query: GetUserTasksQuerySchema,
    },
    responses: {
      "200": {
        description: "A paginated list of user's tasks with metadata.",
        content: {
          "application/json": {
            schema: GetUserVideoTasksResponseSchema,
          },
        },
      },
      "401": {
        description: "Authentication required.",
        content: {
          "application/json": {
            schema: GenericErrorSchema,
          },
        },
      },
      "404": {
        description: "Tasks not found for the user.",
        content: {
          "application/json": {
            schema: GenericErrorSchema,
          },
        },
      },
      "500": {
        description: "Failed to retrieve user tasks.",
        content: {
          "application/json": {
            schema: GenericErrorSchema,
          },
        },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    try {
      // Get Firebase token for user authentication
      const firebaseToken = getFirebaseToken(c);
      if (!firebaseToken) {
        return c.json({ error: "Authentication required" }, 401);
      }

      const userId = firebaseToken.uid;

      const { query } = await this.getValidatedData<typeof this.schema>();

      const container = c.get("container");

      const videoGenerationService = container.resolve<VideoGenerationService>(
        IVideoGenerationService
      );

      const result = await videoGenerationService.getUserVideoTasks(
        userId,
        query
      );

      if (result.tasks.length === 0 && result.pagination.currentPage === 1) {
        return c.json({ error: "Tasks not found" }, 404);
      }

      return c.json(result, 200);
    } catch (error: any) {
      console.error(`Error in GET /history endpoint:`, error);
      return c.json({ error: "Failed to retrieve user tasks." }, 500);
    }
  }
}
