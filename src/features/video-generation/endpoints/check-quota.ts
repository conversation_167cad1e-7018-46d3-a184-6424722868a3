import { type Context } from "hono";
import { OpenAPIRoute } from "chanfana";
import { z } from "zod";

import type { HonoEnv } from "../../../types";
import {
  IQuotaService,
  type QuotaService,
} from "../../../infrastructure/quota/quota.interface";
import { QuotaCheckResponseSchema } from "../../../infrastructure/quota/quota.schema";
import { getFirebaseToken } from "@hono/firebase-auth";

export class CheckVideoQuotaEndpoint extends OpenAPIRoute {
  schema = {
    summary: "Check video generation quota",
    description:
      "Check if the authenticated user can use the video generation feature and get quota information.",
    tags: ["Video Generation"],
    security: [
      {
        BearerAuth: [],
      },
    ],
    request: {
      query: z.object({
        userType: z
          .enum(["free", "weekly_pro", "monthly_pro", "yearly_pro"])
          .optional(),
      }),
    },
    responses: {
      200: {
        description: "Quota information retrieved successfully",
        content: {
          "application/json": {
            schema: QuotaCheckResponseSchema,
          },
        },
      },
      400: {
        description: "Invalid request parameters",
        content: {
          "application/json": {
            schema: z.object({
              error: z.string(),
            }),
          },
        },
      },
      500: {
        description: "Internal server error",
        content: {
          "application/json": {
            schema: z.object({
              error: z.string(),
            }),
          },
        },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    // Get Firebase token for user authentication
    const firebaseToken = getFirebaseToken(c);
    if (!firebaseToken) {
      return c.json({ error: "Authentication required" }, 401);
    }

    const uid = firebaseToken.uid;
    const container = c.get("container");
    const quotaService = container.resolve<QuotaService>(IQuotaService);

    const { query } = await this.getValidatedData<typeof this.schema>();
    const { userType } = query;

    try {
      const quota = await quotaService.checkQuota(
        uid,
        "video-generation",
        userType
      );
      const canUse = quota.remaining > 0;

      return c.json(
        {
          canUse,
          quota,
        },
        200
      );
    } catch (error: any) {
      console.error(
        "Error in GET /video-generation/quota/:deviceId endpoint:",
        error
      );
      return c.json({ error: "Failed to check quota." }, 500);
    }
  }
}
