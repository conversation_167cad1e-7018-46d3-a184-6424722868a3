import { container } from "tsyringe";
import {
  INotionService,
  NotionService,
} from "../features/notion/notion.interface";
import { CLOUDFLARE_ENV } from "../infrastructure/env/env-service.interface";
import { setupRequestContainer } from "../container.setup";
import type {
  ExecutionContext,
  ScheduledEvent,
} from "@cloudflare/workers-types";

/**
 * Scheduled task handler to refresh all Notion CMS caches.
 * This function is triggered by <PERSON><PERSON><PERSON><PERSON> cron triggers every 10 minutes.
 * It calls the Notion service to fetch the latest data and update the cache.
 *
 * @param event - The scheduled event from Cloudflare
 * @param env - The Cloudflare environment bindings
 * @param ctx - The execution context
 */
export async function refreshNotionCmsCache(
  _event: ScheduledEvent,
  env: any,
  _ctx: ExecutionContext
): Promise<void> {
  console.log(
    "Scheduled task: refreshNotionCmsCache started at",
    new Date().toISOString()
  );

  try {
    // Setup the container with the Cloudflare environment
    const requestContainer = container.createChildContainer();
    requestContainer.register(CLOUDFLARE_ENV, {
      useValue: env,
    });
    setupRequestContainer(requestContainer);

    // Resolve the Notion service
    const notionService =
      requestContainer.resolve<NotionService>(INotionService);

    // Refresh both database caches directly
    console.log("Refreshing Ghibli CMS database cache");
    await notionService.queryDatabase(false);

    console.log("Refreshing Video Usecase CMS database cache");
    await notionService.queryVideoUsecaseDatabase(false);

    console.log("All Notion CMS cache refresh completed successfully");
  } catch (error) {
    console.error("Error refreshing Notion CMS cache:", error);
  }
}