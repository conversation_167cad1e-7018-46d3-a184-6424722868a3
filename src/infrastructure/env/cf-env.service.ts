import { inject, singleton } from "tsyringe";

import type { CloudflareBindings } from "../../types";
import { CLOUDFLARE_ENV, EnvService } from "./env-service.interface";

@singleton()
export class CloudflareEnvService extends EnvService {
  constructor(@inject(CLOUDFLARE_ENV) private bindings: CloudflareBindings) {
    super();
    console.log("CloudflareEnvService constructor called");
  }

  getBindings(): CloudflareBindings {
    if (!this.bindings) {
      throw new Error(
        "Cloudflare environment bindings not available. Ensure Hono context with env is injected and available."
      );
    }
    return this.bindings;
  }

  getVariable<K extends keyof CloudflareBindings>(
    key: K
  ): CloudflareBindings[K] {
    if (!Object.prototype.hasOwnProperty.call(this.bindings, key)) {
      console.warn(
        `Attempted to access undefined environment variable or binding: ${String(
          key
        )}`
      );
    }
    const value = this.bindings[key];

    // It's good practice to check for undefined, even if the key exists,
    // as some environment variables might be optional or not set.
    if (value === undefined) {
      throw new Error(
        `Environment variable or binding ${String(
          key
        )} not found or is undefined.`
      );
    }
    return value;
  }

  getAllVariables(): CloudflareBindings {
    return this.bindings;
  }
}
