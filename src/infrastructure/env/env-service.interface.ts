import type { InjectionToken } from "tsyringe";
import type { CloudflareBindings } from "../../types";

export const IEnvService: InjectionToken<EnvService> = Symbol("IEnvService");

export const CLOUDFLARE_ENV: InjectionToken<CloudflareBindings> =
  Symbol("CloudflareEnv");

export abstract class EnvService {
  abstract getBindings(): CloudflareBindings;
  abstract getVariable<K extends keyof CloudflareBindings>(
    key: K
  ): CloudflareBindings[K];
  //   abstract getR2Bucket(bucketBindingName: keyof CloudflareBindings): R2Bucket;
  abstract getAllVariables(): CloudflareBindings;
}
