import { nanoid } from "nanoid";
import { uploadStreamToR2 } from "../storage/r2-service";
import {
  arrayBufferToBase64,
  base64ToStream,
  buildErrorResponse,
  buildSuccessResponse,
} from "../../lib/utils";
import { ApiResponse, CloudflareBindings } from "../../types";

// Constants
const WATERMARK_DEFAULT_URL = "https://twitter-r2.a1d.ai/picadabra_logo.png";
const DEFAULT_OUTPUT_FORMAT = "image/jpeg";

// Types
interface WatermarkOptions {
  env: CloudflareBindings;
  watermarkUrl?: string;
}

interface WatermarkUrlOptions extends WatermarkOptions {
  imageUrl: string;
}

interface WatermarkBase64Options extends WatermarkOptions {
  base64Image: string;
}

interface WatermarkResult {
  success: boolean;
  error?: string;
  imageStream?: ReadableStream;
  contentType?: string;
}

interface WatermarkBase64Result {
  success: boolean;
  base64Result?: string;
  error?: string;
}

interface WatermarkR2Result {
  message: string;
  r2Key: string;
  publicUrl: string;
}

// Default configuration
const DEFAULT_WATERMARK_TRANSFORM = {
  width: 200,
};

const DEFAULT_DRAW_OPTIONS = {
  bottom: 10, // 10px padding from bottom
  left: 10, // 10px padding from left
  opacity: 0.5, // 50% opacity
  repeat: false, // Ensure watermark is not tiled
};

function generateWatermarkedFilename(originalUrl: string): string {
  try {
    const url = new URL(originalUrl);
    const pathname = url.pathname;
    // Handle cases where pathname might be just '/' or empty
    const lastSlashIndex = pathname.lastIndexOf("/");
    const filename =
      lastSlashIndex === -1 ? pathname : pathname.substring(lastSlashIndex + 1);

    if (!filename) {
      // Fallback if filename extraction failed
      return `watermarked/image-${Date.now()}-with-watermark.jpg`;
    }

    const nameParts = filename.split(".");
    // Handle filenames without extensions or starting with '.'
    if (nameParts.length <= 1 && filename.startsWith(".")) {
      return `watermarked/${filename}-${nanoid(4)}-with-watermark`; // Keep original hidden file structure
    } else if (nameParts.length <= 1) {
      return `watermarked/${filename}-${nanoid(4)}-with-watermark.jpg`; // Add default extension
    }

    const extension = nameParts.pop() || "jpg";
    const baseName = nameParts.join(".");
    return `watermarked/${baseName}-${nanoid(4)}-with-watermark.${extension}`;
  } catch (e) {
    console.error("Error parsing original URL for filename:", e);
    // Fallback filename
    return `watermarked/image-${Date.now()}-with-watermark.jpg`;
  }
}

async function applyWatermarkToStream(
  imageStream: ReadableStream,
  watermarkResponse: Response,
  env: CloudflareBindings
): Promise<WatermarkResult> {
  try {
    if (!watermarkResponse.ok || !watermarkResponse.body) {
      const errorMsg = `Failed to fetch watermark: ${watermarkResponse.statusText}`;
      console.error(`[ERROR] ${errorMsg}`);
      return { success: false, error: errorMsg };
    }

    // create watermark transform
    const watermarkTransform = env.IMAGES.input(
      watermarkResponse.body
    ).transform(DEFAULT_WATERMARK_TRANSFORM);

    // apply watermark and output
    const finalImageResult = await env.IMAGES.input(imageStream)
      .draw(watermarkTransform, DEFAULT_DRAW_OPTIONS)
      .output({ format: DEFAULT_OUTPUT_FORMAT });

    const finalResponse = finalImageResult.response();
    if (!finalResponse.ok || !finalResponse.body) {
      const errorMsg = "Failed to generate watermarked image data.";
      console.error(`[ERROR] ${errorMsg}`);
      return { success: false, error: errorMsg };
    }

    const contentType =
      finalResponse.headers.get("content-type") || DEFAULT_OUTPUT_FORMAT;

    return {
      success: true,
      imageStream: finalResponse.body,
      contentType,
    };
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error processing image";
    console.error("[ERROR] Error applying watermark:", error);
    return { success: false, error: errorMessage };
  }
}

export async function addWatermark({
  imageUrl,
  watermarkUrl = WATERMARK_DEFAULT_URL,
  env,
}: WatermarkUrlOptions): Promise<ApiResponse<WatermarkR2Result | null>> {
  try {
    // 获取主图片
    const imageResponse = await fetch(imageUrl);
    if (!imageResponse.ok || !imageResponse.body) {
      const errorMsg = `Failed to fetch image: ${imageResponse.statusText}`;
      console.error(`[ERROR] ${errorMsg}`);
      return buildErrorResponse(errorMsg);
    }

    const r2PublicUrl = env.R2_PUBLIC_URL;
    if (!r2PublicUrl) {
      const errorMsg = "R2_PUBLIC_URL environment variable not configured.";
      console.error(`[ERROR] ${errorMsg}`);
      return buildErrorResponse(errorMsg);
    }

    const watermarkResponse = await fetch(watermarkUrl);
    const watermarkResult = await applyWatermarkToStream(
      imageResponse.body,
      watermarkResponse,
      env
    );

    if (!watermarkResult.success) {
      return buildErrorResponse(
        watermarkResult.error || "Apply watermark failed"
      );
    }

    const objectKey = generateWatermarkedFilename(imageUrl);
    const publicUrl = await uploadStreamToR2(
      watermarkResult.imageStream!,
      env.R2Bucket,
      objectKey,
      watermarkResult.contentType!,
      r2PublicUrl
    );

    const result: WatermarkR2Result = {
      message: "Watermark added and image uploaded to R2 successfully.",
      r2Key: objectKey,
      publicUrl: publicUrl,
    };

    return buildSuccessResponse<WatermarkR2Result>(result);
  } catch (error) {
    console.error("[ERROR] Error processing image:", error);
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error processing image";
    return buildErrorResponse(errorMessage);
  }
}

export async function addWatermarkToBase64({
  base64Image,
  watermarkUrl = WATERMARK_DEFAULT_URL,
  env,
}: WatermarkBase64Options): Promise<ApiResponse<WatermarkBase64Result | null>> {
  try {
    // convert base64 to stream
    const imageStream = base64ToStream(base64Image);

    // get watermark and apply
    const watermarkResponse = await fetch(watermarkUrl);
    const watermarkResult = await applyWatermarkToStream(
      imageStream,
      watermarkResponse,
      env
    );

    if (!watermarkResult.success) {
      return buildErrorResponse(watermarkResult.error || "Unknown error");
    }

    const response = new Response(watermarkResult.imageStream!);
    const arrayBuffer = await response.arrayBuffer();
    const base64Result = arrayBufferToBase64(arrayBuffer);
    return buildSuccessResponse<WatermarkBase64Result>({
      success: true,
      base64Result,
    });
  } catch (error) {
    console.error("[ERROR] Error processing base64 image:", error);
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error processing image";
    return buildErrorResponse(errorMessage);
  }
}
