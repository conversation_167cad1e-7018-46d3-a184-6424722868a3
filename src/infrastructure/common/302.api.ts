import type {
  ChatCompletionMessage,
  ChatCompletionResponse,
} from "../../types";

/**
 * Process GPT request with streaming that returns the full response
 */
export async function streamGptRequest(
  messages: ChatCompletionMessage[],
  apiKey: string,
  baseUrl: string,
  model: string,
  temperature: number = 0.7,
  maxTokens: number | null = null,
  onProgress?: (progress: number) => Promise<void>
): Promise<ChatCompletionResponse | null> {
  const startTime = Date.now();
  console.log(
    `[GPT 302.ai] Streaming request started at ${new Date(
      startTime
    ).toISOString()}`
  );

  const requestBody: any = {
    model,
    messages,
    temperature,
    stream: true,
  };

  // Add optional parameters
  if (maxTokens !== null) {
    requestBody.max_tokens = maxTokens;
  }

  console.log(
    `🚀 [GPT 302.ai] Request body: ${JSON.stringify({
      ...requestBody,
    })}`
  );

  // Call 302.ai API
  // const response = await fetch("https://api.302.ai/v1/chat/completions", {
  //   method: "POST",
  //   headers: {
  //     "Content-Type": "application/json",
  //     Authorization: `Bearer ${apiKey}`,
  //   },
  //   body: JSON.stringify(requestBody),
  // });

  // @deniffer debug
  // console.log(
  //   `[GPT 302.ai] Request url: ${baseUrl}/chat/completions, api key: ${apiKey}`
  // );
  const response = await fetch(`${baseUrl}/chat/completions`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${apiKey}`,
    },
    body: JSON.stringify(requestBody),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      `GPT API request failed: ${response.status} ${response.statusText} - ${
        typeof errorData === "object" ? JSON.stringify(errorData) : errorData
      }`
    );
  }

  if (!response.body) {
    throw new Error("Response body is null");
  }

  let fullContent = "";
  let fullResponse: ChatCompletionResponse | null = null;
  let chunkCount = 0;
  let dataBuffer = "";
  let progressReported = 0;

  const reader = response.body.getReader();
  const decoder = new TextDecoder();

  try {
    let done = false;

    while (!done) {
      const { value, done: doneReading } = await reader.read();
      chunkCount++;
      // console.log(
      //   `[GPT 302.ai] Chunk #${chunkCount} - value:`,
      //   value ? `[${value.length} bytes]` : "undefined",
      //   "doneReading:",
      //   doneReading
      // );
      done = doneReading;

      // Progress is now handled by parsing actual GPT response content

      if (done) {
        // console.log(`[GPT 302.ai] Stream completed after ${chunkCount} chunks`);
        break;
      }

      const chunk = decoder.decode(value, { stream: true });
      // console.log(
      //   `[GPT 302.ai] Decoded chunk #${chunkCount}: ${chunk.length} chars`
      // );

      dataBuffer += chunk;

      let newLineIndex;
      while ((newLineIndex = dataBuffer.indexOf("\n")) !== -1) {
        const line = dataBuffer.substring(0, newLineIndex).trim();
        dataBuffer = dataBuffer.substring(newLineIndex + 1);

        if (line === "" || line === "data: [DONE]") continue;

        if (line.startsWith("data: ")) {
          try {
            const jsonData = line.replace("data: ", "");
            const data = JSON.parse(jsonData);
            // console.log(`[GPT 302.ai] Parsed data:`, JSON.stringify(data));

            if (data.choices && data.choices[0]?.delta?.content) {
              const content = data.choices[0].delta.content;
              fullContent += content;

              console.log(`🐳 [GPT 302.ai] Content delta: "${content}"`);

              // Parse actual progress from GPT response
              if (onProgress && content) {
                const progressMatch = content.match(
                  /进度[：:]\s*(\d+(?:\.\d+)?)%/
                );
                if (progressMatch) {
                  const actualProgress = Math.round(
                    parseFloat(progressMatch[1])
                  );
                  if (
                    actualProgress > progressReported &&
                    actualProgress <= 100
                  ) {
                    progressReported = actualProgress;
                    try {
                      await onProgress(actualProgress);
                      console.log(
                        `📊 [GPT 302.ai] Actual progress from GPT: ${actualProgress}%`
                      );
                    } catch (error) {
                      console.error(
                        `[GPT 302.ai] Progress update failed:`,
                        error
                      );
                    }
                  }
                }
              }

              // console.log(
              //   `[GPT 302.ai] Full content length: ${fullContent.length} chars`
              // );
            }

            // if (data.choices && data.choices[0]?.finish_reason) {
            //   console.log(
            //     `[GPT 302.ai] Finish reason received: ${data.choices[0].finish_reason}`
            //   );
            // }

            if (data.id && data.model) {
              fullResponse = {
                ...data,
                choices: [
                  {
                    index: 0,
                    message: {
                      role: "assistant",
                      content: fullContent,
                    },
                    finish_reason: data.choices?.[0]?.finish_reason || "stop",
                  },
                ],
              };
              // console.log(
              //   `[GPT 302.ai] Response object ${
              //     prevResponseId ? "updated" : "created"
              //   } with id: ${data.id}`
              // );
            }
          } catch (error) {
            if (!(error instanceof SyntaxError)) {
              console.error(
                `[GPT 302.ai] Error parsing stream data: ${error}, line: ${line}`
              );
              throw error;
            }
            dataBuffer = line + "\n" + dataBuffer;
            break;
          }
        }
      }
    }

    if (dataBuffer.trim() !== "") {
      const lines = dataBuffer
        .split("\n")
        .filter((line) => line.trim() !== "" && line.trim() !== "data: [DONE]");

      for (const line of lines) {
        if (line.startsWith("data: ")) {
          try {
            const jsonData = line.replace("data: ", "");
            const data = JSON.parse(jsonData);

            if (data.choices && data.choices[0]?.delta?.content) {
              const content = data.choices[0].delta.content;
              fullContent += content;
            }

            if (data.id && data.model) {
              fullResponse = {
                ...data,
                choices: [
                  {
                    index: 0,
                    message: {
                      role: "assistant",
                      content: fullContent,
                    },
                    finish_reason: data.choices?.[0]?.finish_reason || "stop",
                  },
                ],
              };
            }
          } catch (error) {
            console.error(
              `[GPT 302.ai] Error parsing final buffer data: ${error}, line: ${line}`
            );
          }
        }
      }
    }
  } catch (error) {
    console.error(`[GPT 302.ai] Error reading stream: ${error}`);
    throw new Error(`Error processing stream: ${error}`);
  } finally {
    // Set progress to 100% when completed
    if (onProgress && progressReported < 100) {
      try {
        await onProgress(100);
        console.log(`✅ [GPT 302.ai] Progress completed: 100%`);
      } catch (error) {
        console.error(`[GPT 302.ai] Final progress update failed:`, error);
      }
    }

    const endTime = Date.now();
    const duration = endTime - startTime;
    console.log(
      `[GPT 302.ai] Streaming request completed in ${(duration / 1000).toFixed(
        2
      )}s at ${new Date(endTime).toISOString()}`
    );
  }

  console.log(
    `⭐ [GPT 302.ai] Final response object:`,
    fullResponse
      ? JSON.stringify({
          id: fullResponse.id,
          model: fullResponse.model,
          choices: fullResponse.choices?.map((c) => ({
            finish_reason: c.finish_reason,
            message: c.message
              ? {
                  role: c.message.role,
                  content: c.message.content,
                }
              : null,
          })),
        })
      : "null"
  );
  // console.log(`[GPT 302.ai] Final content length: ${fullContent.length} chars`);

  if (fullResponse && fullResponse.choices?.length > 0) {
    // console.log(
    //   `[GPT 302.ai] Updating final response with complete content (${fullContent.length} chars)`
    // );
    fullResponse.choices[0].message = {
      role: "assistant",
      content: fullContent,
    };
  }

  return fullResponse;
}
