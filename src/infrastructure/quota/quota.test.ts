import "reflect-metadata";
import { describe, it, expect, beforeEach } from "vitest";
import { UniversalQuotaService } from "./quota.service";
import { MemoryCacheService } from "../cache/memory-cache.service";

describe("UniversalQuotaService", () => {
  let quotaService: UniversalQuotaService;
  let cacheService: MemoryCacheService;

  beforeEach(() => {
    cacheService = new MemoryCacheService();
    quotaService = new UniversalQuotaService(cacheService);
  });

  describe("Image Generation Feature", () => {
    it("should return initial quota for new user", async () => {
      const uid = "test-user-1";
      const quota = await quotaService.checkQuota(uid, "image-generation");

      expect(quota.uid).toBe(uid);
      expect(quota.used).toBe(0);
      expect(quota.limit).toBe(3);
      expect(quota.remaining).toBe(3);
      expect(quota.resetTime).toBeDefined();
    });

    it("should consume quota correctly", async () => {
      const uid = "test-user-2";

      // Initial check
      let quota = await quotaService.checkQuota(uid, "image-generation");
      expect(quota.remaining).toBe(3);

      // Consume 1
      quota = await quotaService.consumeQuota(uid, "image-generation", 1);
      expect(quota.used).toBe(1);
      expect(quota.remaining).toBe(2);

      // Consume another 1
      quota = await quotaService.consumeQuota(uid, "image-generation", 1);
      expect(quota.used).toBe(2);
      expect(quota.remaining).toBe(1);
    });

    it("should throw error when quota exceeded", async () => {
      const uid = "test-user-3";

      // Consume all quota
      await quotaService.consumeQuota(uid, "image-generation", 3);

      // Try to consume more
      await expect(
        quotaService.consumeQuota(uid, "image-generation", 1)
      ).rejects.toThrow(
        "Daily quota exceeded. Used: 3, Limit: 3, Requested: 1"
      );
    });
  });

  describe("Video Generation Feature", () => {
    it("should return initial quota for video generation", async () => {
      const uid = "test-user-video-1";
      const quota = await quotaService.checkQuota(uid, "video-generation");

      expect(quota.uid).toBe(uid);
      expect(quota.used).toBe(0);
      expect(quota.limit).toBe(2); // Video generation has lower daily limit
      expect(quota.remaining).toBe(2);
      expect(quota.resetTime).toBeDefined();
    });

    it("should consume video generation quota correctly", async () => {
      const uid = "test-user-video-2";

      // Initial check
      let quota = await quotaService.checkQuota(uid, "video-generation");
      expect(quota.remaining).toBe(2);

      // Consume 1
      quota = await quotaService.consumeQuota(uid, "video-generation", 1);
      expect(quota.used).toBe(1);
      expect(quota.remaining).toBe(1);

      // Consume another 1
      quota = await quotaService.consumeQuota(uid, "video-generation", 1);
      expect(quota.used).toBe(2);
      expect(quota.remaining).toBe(0);
    });
  });

  describe("Pro User Features", () => {
    it("should return weekly quota for weekly_pro users", async () => {
      const uid = "test-user-weekly-1";
      const quota = await quotaService.checkQuota(
        uid,
        "image-generation",
        "weekly_pro"
      );

      expect(quota.uid).toBe(uid);
      expect(quota.used).toBe(0);
      expect(quota.limit).toBe(100);
      expect(quota.remaining).toBe(100);
      expect(quota.resetTime).toBeDefined();
    });

    it("should return weekly quota for video generation pro users", async () => {
      const uid = "test-user-video-weekly-1";
      const quota = await quotaService.checkQuota(
        uid,
        "video-generation",
        "weekly_pro"
      );

      expect(quota.uid).toBe(uid);
      expect(quota.used).toBe(0);
      expect(quota.limit).toBe(50); // Video generation has lower weekly limit
      expect(quota.remaining).toBe(50);
      expect(quota.resetTime).toBeDefined();
    });
  });

  describe("Cross-Feature Independence", () => {
    it("should maintain separate quotas for different features", async () => {
      const uid = "test-user-cross";

      // Consume image generation quota
      await quotaService.consumeQuota(uid, "image-generation", 2);
      let imageQuota = await quotaService.checkQuota(uid, "image-generation");
      expect(imageQuota.remaining).toBe(1);

      // Video generation quota should be unaffected
      let videoQuota = await quotaService.checkQuota(uid, "video-generation");
      expect(videoQuota.remaining).toBe(2);

      // Consume video generation quota
      await quotaService.consumeQuota(uid, "video-generation", 1);
      videoQuota = await quotaService.checkQuota(uid, "video-generation");
      expect(videoQuota.remaining).toBe(1);

      // Image generation quota should still be 1
      imageQuota = await quotaService.checkQuota(uid, "image-generation");
      expect(imageQuota.remaining).toBe(1);
    });
  });

  describe("Utility Functions", () => {
    it("should reset quota correctly", async () => {
      const uid = "test-user-4";

      // Consume some quota
      await quotaService.consumeQuota(uid, "image-generation", 2);
      let quota = await quotaService.checkQuota(uid, "image-generation");
      expect(quota.remaining).toBe(1);

      // Reset quota
      await quotaService.resetQuota(uid, "image-generation");

      // Check quota after reset
      quota = await quotaService.checkQuota(uid, "image-generation");
      expect(quota.remaining).toBe(3);
      expect(quota.used).toBe(0);
    });

    it("should refund quota correctly", async () => {
      const uid = "test-user-5";

      // Consume some quota
      await quotaService.consumeQuota(uid, "image-generation", 2);
      let quota = await quotaService.checkQuota(uid, "image-generation");
      expect(quota.used).toBe(2);
      expect(quota.remaining).toBe(1);

      // Refund 1 quota
      quota = await quotaService.refundQuota(uid, "image-generation", 1);
      expect(quota.used).toBe(1);
      expect(quota.remaining).toBe(2);
    });

    it("should not refund quota below zero", async () => {
      const uid = "test-user-6";

      // Initial state (no quota consumed)
      let quota = await quotaService.checkQuota(uid, "image-generation");
      expect(quota.used).toBe(0);

      // Try to refund when nothing was consumed
      quota = await quotaService.refundQuota(uid, "image-generation", 1);
      expect(quota.used).toBe(0); // Should remain 0, not go negative
      expect(quota.remaining).toBe(3);
    });
  });
});
