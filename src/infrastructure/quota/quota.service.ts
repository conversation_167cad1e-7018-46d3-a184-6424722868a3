import { inject, singleton } from "tsyringe";
import { QuotaService } from "./quota.interface";
import {
  ICacheService,
  type CacheService,
} from "../cache/cache-service.interface";
import type { QuotaInfo } from "./quota.schema";

interface QuotaData {
  used: number;
  limit: number;
  date: string; // YYYY-MM-DD format for daily, YYYY-WW format for weekly
  period: "daily" | "weekly";
}

/**
 * Feature-specific quota configuration
 */
interface FeatureQuotaConfig {
  dailyLimit: number;
  weeklyProLimit: number;
}

@singleton()
export class UniversalQuotaService implements QuotaService {
  private readonly DAILY_CACHE_TTL = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
  private readonly WEEKLY_CACHE_TTL = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds

  // Feature-specific quota configurations
  private readonly featureConfigs: Record<string, FeatureQuotaConfig> = {
    "image-generation": {
      dailyLimit: 300,
      weeklyProLimit: 1000,
    },
    "video-generation": {
      dailyLimit: 200,
      weeklyProLimit: 500,
    },
    // Add more features as needed
  };

  constructor(@inject(ICacheService) private cacheService: CacheService) {}

  /**
   * Get feature configuration or default values
   */
  private getFeatureConfig(feature: string): FeatureQuotaConfig {
    return (
      this.featureConfigs[feature] || {
        dailyLimit: 3,
        weeklyProLimit: 100,
      }
    );
  }

  /**
   * Determine if user should use weekly quota
   */
  private isProUser(userType?: string): boolean {
    return (
      userType === "weekly_pro" ||
      userType === "yearly_pro" ||
      userType === "monthly_pro"
    );
  }

  /**
   * Get the appropriate limit based on user type and feature
   */
  private getLimit(feature: string, userType?: string): number {
    const config = this.getFeatureConfig(feature);
    return this.isProUser(userType) ? config.weeklyProLimit : config.dailyLimit;
  }

  /**
   * Get the appropriate TTL based on user type
   */
  private getTTL(userType?: string): number {
    return this.isProUser(userType)
      ? this.WEEKLY_CACHE_TTL
      : this.DAILY_CACHE_TTL;
  }

  /**
   * Get the current week in YYYY-WW format
   */
  private getCurrentWeek(): string {
    const now = new Date();
    const startOfYear = new Date(now.getFullYear(), 0, 1);
    const pastDaysOfYear = (now.getTime() - startOfYear.getTime()) / 86400000;
    const weekNumber = Math.ceil(
      (pastDaysOfYear + startOfYear.getDay() + 1) / 7
    );
    return `${now.getFullYear()}-${weekNumber.toString().padStart(2, "0")}`;
  }

  /**
   * Get the cache key for a user and feature
   */
  private getCacheKey(uid: string, feature: string, userType?: string): string {
    if (this.isProUser(userType)) {
      const currentWeek = this.getCurrentWeek();
      return `quota:${feature}:${uid}:${currentWeek}:weekly`;
    } else {
      const today = new Date().toISOString().split("T")[0]; // YYYY-MM-DD
      return `quota:${feature}:${uid}:${today}:daily`;
    }
  }

  /**
   * Get the reset time for quota (midnight of next day for daily, next Monday for weekly)
   */
  private getResetTime(userType?: string): string {
    if (this.isProUser(userType)) {
      // Get next Monday
      const nextMonday = new Date();
      const daysUntilMonday = (8 - nextMonday.getDay()) % 7;
      nextMonday.setDate(
        nextMonday.getDate() + (daysUntilMonday === 0 ? 7 : daysUntilMonday)
      );
      nextMonday.setHours(0, 0, 0, 0);
      return nextMonday.toISOString();
    } else {
      // Get next day
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(0, 0, 0, 0);
      return tomorrow.toISOString();
    }
  }

  /**
   * Get current quota data from cache or create new
   */
  private async getQuotaData(
    uid: string,
    feature: string,
    userType?: string
  ): Promise<QuotaData> {
    const cacheKey = this.getCacheKey(uid, feature, userType);
    const cached = await this.cacheService.get<QuotaData>(cacheKey);

    if (cached) {
      return cached;
    }

    // Create new quota data
    const isWeekly = this.isProUser(userType);
    const date = isWeekly
      ? this.getCurrentWeek()
      : new Date().toISOString().split("T")[0];
    const newQuotaData: QuotaData = {
      used: 0,
      limit: this.getLimit(feature, userType),
      date,
      period: isWeekly ? "weekly" : "daily",
    };

    // Store in cache with appropriate TTL
    await this.cacheService.set(cacheKey, newQuotaData, this.getTTL(userType));

    return newQuotaData;
  }

  /**
   * Convert QuotaData to QuotaInfo
   */
  private toQuotaInfo(
    uid: string,
    quotaData: QuotaData,
    userType?: string
  ): QuotaInfo {
    return {
      uid,
      used: quotaData.used,
      limit: quotaData.limit,
      remaining: Math.max(0, quotaData.limit - quotaData.used),
      resetTime: this.getResetTime(userType),
    };
  }

  async checkQuota(
    uid: string,
    feature: string,
    userType?: string
  ): Promise<QuotaInfo> {
    const quotaData = await this.getQuotaData(uid, feature, userType);
    return this.toQuotaInfo(uid, quotaData, userType);
  }

  async consumeQuota(
    uid: string,
    feature: string,
    amount: number = 1,
    userType?: string
  ): Promise<QuotaInfo> {
    const quotaData = await this.getQuotaData(uid, feature, userType);

    // Check if quota would be exceeded
    if (quotaData.used + amount > quotaData.limit) {
      const periodText = this.isProUser(userType) ? "weekly" : "daily";
      throw new Error(
        `${
          periodText.charAt(0).toUpperCase() + periodText.slice(1)
        } quota exceeded. Used: ${quotaData.used}, Limit: ${
          quotaData.limit
        }, Requested: ${amount}`
      );
    }

    // Update quota
    quotaData.used += amount;

    // Save back to cache
    const cacheKey = this.getCacheKey(uid, feature, userType);
    await this.cacheService.set(cacheKey, quotaData, this.getTTL(userType));

    return this.toQuotaInfo(uid, quotaData, userType);
  }

  async getQuotaInfo(
    uid: string,
    feature: string,
    userType?: string
  ): Promise<QuotaInfo> {
    return this.checkQuota(uid, feature, userType);
  }

  async resetQuota(
    uid: string,
    feature: string,
    userType?: string
  ): Promise<void> {
    const cacheKey = this.getCacheKey(uid, feature, userType);
    await this.cacheService.del(cacheKey);
  }

  async refundQuota(
    uid: string,
    feature: string,
    amount: number = 1,
    userType?: string
  ): Promise<QuotaInfo> {
    const quotaData = await this.getQuotaData(uid, feature, userType);

    // Refund quota (ensure it doesn't go below 0)
    quotaData.used = Math.max(0, quotaData.used - amount);

    // Save back to cache
    const cacheKey = this.getCacheKey(uid, feature, userType);
    await this.cacheService.set(cacheKey, quotaData, this.getTTL(userType));

    return this.toQuotaInfo(uid, quotaData, userType);
  }
}
