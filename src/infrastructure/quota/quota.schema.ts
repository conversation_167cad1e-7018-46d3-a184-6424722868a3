import { z } from "zod";

// Quota related schemas
export const QuotaInfoSchema = z.object({
  uid: z.string(),
  used: z.number().min(0),
  limit: z.number().min(1),
  remaining: z.number().min(0),
  resetTime: z.string(), // ISO date string when quota resets
});

export const QuotaCheckResponseSchema = z.object({
  canUse: z.boolean(),
  quota: QuotaInfoSchema,
});

// Type exports
export type QuotaInfo = z.infer<typeof QuotaInfoSchema>;
export type QuotaCheckResponse = z.infer<typeof QuotaCheckResponseSchema>;
