import type { InjectionToken } from "tsyringe";
import type { QuotaInfo } from "./quota.schema";

export const IQuotaService: InjectionToken<QuotaService> =
  Symbol("IQuotaService");

/**
 * Abstract class representing the contract for a quota service.
 * Manages daily usage quotas for different features.
 */
export abstract class QuotaService {
  /**
   * Check if a user can use a specific feature
   * @param uid The user identifier
   * @param feature The feature name (e.g., 'image-generation', 'video-generation')
   * @param userType The user subscription type
   * @returns Quota information including whether the user can use the feature
   */
  abstract checkQuota(
    uid: string,
    feature: string,
    userType?: string
  ): Promise<QuotaInfo>;

  /**
   * Consume quota for a user and feature
   * @param uid The user identifier
   * @param feature The feature name
   * @param amount The amount to consume (default: 1)
   * @param userType The user subscription type
   * @returns Updated quota information
   */
  abstract consumeQuota(
    uid: string,
    feature: string,
    amount?: number,
    userType?: string
  ): Promise<QuotaInfo>;

  /**
   * Get current quota information without consuming
   * @param uid The user identifier
   * @param feature The feature name
   * @param userType The user subscription type
   * @returns Current quota information
   */
  abstract getQuotaInfo(
    uid: string,
    feature: string,
    userType?: string
  ): Promise<QuotaInfo>;

  /**
   * Reset quota for a user and feature (admin function)
   * @param uid The user identifier
   * @param feature The feature name
   * @param userType The user subscription type
   */
  abstract resetQuota(
    uid: string,
    feature: string,
    userType?: string
  ): Promise<void>;

  /**
   * Refund quota for a user and feature when operation fails
   * @param uid The user identifier
   * @param feature The feature name
   * @param amount The amount to refund (default: 1)
   * @param userType The user subscription type
   * @returns Updated quota information
   */
  abstract refundQuota(
    uid: string,
    feature: string,
    amount?: number,
    userType?: string
  ): Promise<QuotaInfo>;
}
