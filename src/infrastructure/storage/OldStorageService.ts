// import { singleton } from "tsyringe";
// import { storage } from "../../firebase";
// import { v4 as uuidv4 } from "uuid";
// import path from "path";
// import type { UploadResult, FileInfo, FileType } from "./storage.schema";
// import sharp from "sharp";

// // 用于确定文件类型的映射
// const contentTypeMap: Record<string, FileType> = {
//   "image/": "image",
//   "audio/": "audio",
//   "video/": "video",
//   "application/pdf": "document",
//   "application/msword": "document",
//   "application/vnd.openxmlformats-officedocument": "document",
//   "text/": "document",
// };

// const bucketName = process.env.FIRESTORE_BUCKET_NAME;
// console.log("bucketName:", bucketName);

// @singleton()
// export class StorageService {
//   private storage = storage;
//   private bucketName = bucketName;

//   /**
//    * 上传文件到 Firebase Storage
//    * @param file 文件数据 (Buffer 或 Uint8Array)
//    * @param fileName 文件名
//    * @param contentType 文件类型 (MIME type)
//    * @param options 其他选项 (路径, 元数据等)
//    */
//   async uploadFile(
//     file: Buffer | Uint8Array,
//     fileName: string,
//     contentType: string,
//     options?: {
//       path?: string;
//       metadata?: Record<string, string>;
//       generateThumbnail?: boolean;
//       userId?: string;
//     }
//   ): Promise<UploadResult> {
//     // 生成唯一的文件名，避免冲突
//     const fileExt = path.extname(fileName);
//     const fileNameWithoutExt = path.basename(fileName, fileExt);
//     const uniqueFileName = `${fileNameWithoutExt}_${uuidv4()}${fileExt}`;

//     // 设置存储路径
//     const storagePath = options?.path
//       ? `${options.path}/${uniqueFileName}`
//       : `uploads/${uniqueFileName}`;

//     // 创建文件引用
//     const fileRef = this.storage.bucket(this.bucketName).file(storagePath);

//     // 设置元数据
//     const metadata = {
//       contentType,
//       metadata: {
//         ...options?.metadata,
//         originalFileName: fileName,
//         uploadedAt: new Date().toISOString(),
//         userId: options?.userId || "anonymous",
//       },
//     };

//     // 上传文件
//     await fileRef.save(file, {
//       metadata,
//       contentType,
//       public: true,
//     });

//     // 获取文件访问URL
//     const [url] = await fileRef.getSignedUrl({
//       action: "read",
//       expires: "01-01-2100", // 设置长期有效的URL
//     });

//     console.log("上传文件成功:", url);

//     // 生成缩略图（如果是图片且需要生成缩略图）
//     let thumbnailUrl: string | undefined;
//     if (
//       contentType.startsWith("image/") &&
//       options?.generateThumbnail !== false &&
//       ["image/jpeg", "image/png", "image/webp"].includes(contentType)
//     ) {
//       try {
//         // 创建缩略图
//         const thumbnail = await sharp(file)
//           .resize(300, 300, { fit: "inside" })
//           .toBuffer();

//         // 上传缩略图
//         const thumbnailPath = `thumbnails/${uniqueFileName}`;
//         const thumbnailRef = this.storage
//           .bucket(this.bucketName)
//           .file(thumbnailPath);

//         await thumbnailRef.save(thumbnail, {
//           contentType,
//           metadata: {
//             contentType,
//             metadata: {
//               originalFileName: fileName,
//               isThumbnail: "true",
//               originalFilePath: storagePath,
//             },
//           },
//           public: true,
//         });

//         // 获取缩略图URL
//         const [thumbUrl] = await thumbnailRef.getSignedUrl({
//           action: "read",
//           expires: "01-01-2100",
//         });

//         thumbnailUrl = thumbUrl;
//       } catch (error) {
//         console.error("生成缩略图失败:", error);
//       }
//     }

//     // 获取文件信息
//     const [metadata2] = await fileRef.getMetadata();
//     const size = metadata2.size ? parseInt(String(metadata2.size), 10) : 0;

//     return {
//       id: uniqueFileName,
//       fileName: uniqueFileName,
//       contentType,
//       size,
//       url,
//       thumbnailUrl,
//       path: storagePath,
//       bucket: fileRef.bucket.name,
//       createdAt: new Date(),
//       metadata: options?.metadata,
//     };
//   }

//   /**
//    * 从 URL 或 Base64 数据创建并上传文件
//    * @param source URL或Base64字符串
//    * @param fileName 文件名
//    * @param options 选项
//    */
//   async uploadFromSource(
//     source: string,
//     fileName: string,
//     options?: {
//       path?: string;
//       metadata?: Record<string, string>;
//       generateThumbnail?: boolean;
//       userId?: string;
//     }
//   ): Promise<UploadResult> {
//     let file: Buffer;
//     let contentType: string;

//     // 判断是URL还是Base64数据
//     if (source.startsWith("data:")) {
//       // 解析Base64数据
//       const matches = source.match(/^data:([^;]+);base64,(.+)$/);
//       if (!matches || matches.length !== 3) {
//         throw new Error("无效的Base64数据格式");
//       }

//       contentType = matches[1];
//       file = Buffer.from(matches[2], "base64");
//     } else {
//       // 下载URL中的文件
//       const response = await fetch(source);
//       if (!response.ok) {
//         throw new Error(`下载文件失败: ${response.statusText}`);
//       }

//       file = Buffer.from(await response.arrayBuffer());
//       contentType =
//         response.headers.get("content-type") || "application/octet-stream";
//     }

//     return this.uploadFile(file, fileName, contentType, options);
//   }

//   /**
//    * 通过路径获取文件
//    * @param filePath 文件路径
//    */
//   async getFile(filePath: string): Promise<FileInfo | null> {
//     try {
//       const fileRef = this.storage.bucket(this.bucketName).file(filePath);
//       const [exists] = await fileRef.exists();

//       if (!exists) {
//         return null;
//       }

//       const [metadata] = await fileRef.getMetadata();
//       const [url] = await fileRef.getSignedUrl({
//         action: "read",
//         expires: "01-01-2100",
//       });

//       // 尝试获取缩略图
//       let thumbnailUrl: string | undefined;
//       if (metadata.contentType?.startsWith("image/")) {
//         const thumbnailPath = `thumbnails/${path.basename(filePath)}`;
//         const thumbnailRef = this.storage
//           .bucket(this.bucketName)
//           .file(thumbnailPath);
//         const [thumbExists] = await thumbnailRef.exists();

//         if (thumbExists) {
//           const [thumbUrl] = await thumbnailRef.getSignedUrl({
//             action: "read",
//             expires: "01-01-2100",
//           });
//           thumbnailUrl = thumbUrl;
//         }
//       }

//       // 确定文件类型
//       let fileType: FileType = "other";
//       for (const [typePrefix, type] of Object.entries(contentTypeMap)) {
//         if (metadata.contentType?.startsWith(typePrefix)) {
//           fileType = type;
//           break;
//         }
//       }

//       // 安全地获取字符串值
//       const getName = (value: any): string =>
//         typeof value === "string" ? value : String(value || "");

//       // 安全地解析大小
//       const size = metadata.size ? parseInt(String(metadata.size), 10) : 0;

//       return {
//         id: path.basename(filePath),
//         name:
//           typeof metadata.metadata?.originalFileName === "string"
//             ? metadata.metadata.originalFileName
//             : path.basename(filePath),
//         type: fileType,
//         size,
//         contentType: metadata.contentType || "application/octet-stream",
//         url,
//         thumbnailUrl,
//         path: filePath,
//         createdAt: new Date(metadata.timeCreated || Date.now()),
//         updatedAt: metadata.updated ? new Date(metadata.updated) : undefined,
//         ownerId: metadata.metadata?.userId
//           ? String(metadata.metadata.userId)
//           : undefined,
//         metadata: metadata.metadata
//           ? Object.fromEntries(
//               Object.entries(metadata.metadata)
//                 .filter(([_, v]) => v !== null && v !== undefined)
//                 .map(([k, v]) => [k, String(v)])
//             )
//           : undefined,
//       };
//     } catch (error) {
//       console.error("获取文件失败:", error);
//       return null;
//     }
//   }

//   /**
//    * 删除文件
//    * @param filePath 文件路径
//    */
//   async deleteFile(filePath: string): Promise<boolean> {
//     try {
//       const fileRef = this.storage.bucket(this.bucketName).file(filePath);
//       const [exists] = await fileRef.exists();

//       if (!exists) {
//         return false;
//       }

//       // 同时删除对应的缩略图
//       try {
//         const thumbnailPath = `thumbnails/${path.basename(filePath)}`;
//         const thumbnailRef = this.storage
//           .bucket(this.bucketName)
//           .file(thumbnailPath);
//         const [thumbExists] = await thumbnailRef.exists();

//         if (thumbExists) {
//           await thumbnailRef.delete();
//         }
//       } catch (error) {
//         console.warn("删除缩略图时出错:", error);
//       }

//       // 删除主文件
//       await fileRef.delete();
//       return true;
//     } catch (error) {
//       console.error("删除文件失败:", error);
//       return false;
//     }
//   }

//   /**
//    * 从 Attachment 附件中保存文件到 Storage
//    * 用于从临时消息附件创建永久文件
//    */
//   async saveAttachmentToStorage(
//     attachment: {
//       id: string;
//       type: string;
//       url: string;
//       mimeType?: string;
//       filename?: string;
//     },
//     options?: {
//       userId?: string;
//       path?: string;
//     }
//   ): Promise<UploadResult> {
//     const fileName =
//       attachment.filename || `${attachment.id}.${attachment.type}`;
//     const contentType =
//       attachment.mimeType || this.getMimeTypeFromUrl(attachment.url);

//     return this.uploadFromSource(attachment.url, fileName, {
//       path: options?.path || "attachments",
//       userId: options?.userId,
//       metadata: {
//         attachmentId: attachment.id,
//         attachmentType: attachment.type,
//       },
//     });
//   }

//   /**
//    * 尝试从URL中获取MIME类型
//    */
//   getMimeTypeFromUrl(url: string): string {
//     if (url.startsWith("data:")) {
//       const match = url.match(/^data:([^;]+);/);
//       return match ? match[1] : "application/octet-stream";
//     }

//     // 根据扩展名猜测MIME类型
//     const ext = path.extname(url).toLowerCase();
//     const mimeTypes: Record<string, string> = {
//       ".jpg": "image/jpeg",
//       ".jpeg": "image/jpeg",
//       ".png": "image/png",
//       ".gif": "image/gif",
//       ".webp": "image/webp",
//       ".svg": "image/svg+xml",
//       ".mp3": "audio/mpeg",
//       ".mp4": "video/mp4",
//       ".pdf": "application/pdf",
//       ".doc": "application/msword",
//       ".docx":
//         "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
//     };

//     return mimeTypes[ext] || "application/octet-stream";
//   }
// }
