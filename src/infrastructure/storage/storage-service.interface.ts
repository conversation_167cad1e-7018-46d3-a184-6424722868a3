import type { InjectionToken } from "tsyringe";
import type { UploadResult } from "./storage.schema";

// Define the injection token for StorageService
export const IStorageService: InjectionToken<StorageService> =
  Symbol("IStorageService");

/**
 * Abstract class representing the contract for a storage service.
 * It should define methods for file/data storage operations.
 */
export abstract class StorageService {
  abstract uploadImage(
    mimeType: string,
    base64Data: string,
    fileName?: string,
    prefix?: string
  ): Promise<string | undefined>;

  /**
   * Upload image with detailed metadata
   * @param mimeType - MIME type of the image
   * @param base64Data - Base64 encoded image data
   * @param fileName - Optional filename
   * @param prefix - Optional prefix for the file path
   * @returns Promise<UploadResult | undefined> - Detailed upload result
   */
  abstract uploadImageWithMetadata(
    mimeType: string,
    base64Data: string,
    fileName?: string,
    prefix?: string
  ): Promise<UploadResult | undefined>;
}
