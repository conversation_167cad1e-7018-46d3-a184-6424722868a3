import { inject, singleton } from "tsyringe";
import { v4 as uuidv4 } from "uuid";

import { uploadImage } from "./r2-service";
import type { CloudflareBindings } from "../../types";
import { IEnvService, type EnvService } from "../env/env-service.interface";
import { StorageService } from "./storage-service.interface";
import type { UploadResult } from "./storage.schema";

@singleton()
export class R2StorageService implements StorageService {
  private env: CloudflareBindings;
  constructor(@inject(IEnvService) envService: EnvService) {
    this.env = envService.getBindings();
    if (!this.env || !this.env.R2Bucket) {
      console.error(
        "CRITICAL ERROR in StorageService constructor: Injected env is missing R2Bucket!"
      );
    }
  }

  /**
   * upload to R2 storage
   */
  async uploadImage(
    mimeType: string,
    base64Data: string,
    fileName?: string,
    prefix: string = "general-uploads"
  ): Promise<string | undefined> {
    try {
      const fileExtension = mimeType.split("/")[1] || "png";
      if (!fileName) {
        fileName = Date.now().toString();
      }
      const finalFileName = `${prefix}/${fileName}.${fileExtension}`;
      const r2ImageUrl = await uploadImage(
        `data:${mimeType};base64,${base64Data}`,
        this.env.R2Bucket,
        finalFileName,
        this.env.R2_PUBLIC_URL
      );

      return r2ImageUrl;
    } catch (error) {
      console.error("Failed to upload media to R2:", error);
      return undefined;
    }
  }

  /**
   * Upload image with detailed metadata
   */
  async uploadImageWithMetadata(
    mimeType: string,
    base64Data: string,
    fileName?: string,
    prefix: string = "general-uploads"
  ): Promise<UploadResult | undefined> {
    try {
      const fileExtension = mimeType.split("/")[1] || "png";
      const finalFileName = fileName || Date.now().toString();
      const fullPath = `${prefix}/${finalFileName}.${fileExtension}`;

      // Calculate file size from base64 data
      const base64Length = base64Data.length;
      const fileSize = Math.round((base64Length * 3) / 4);

      const r2ImageUrl = await uploadImage(
        `data:${mimeType};base64,${base64Data}`,
        this.env.R2Bucket,
        fullPath,
        this.env.R2_PUBLIC_URL
      );

      if (!r2ImageUrl) {
        return undefined;
      }

      const result: UploadResult = {
        id: uuidv4(),
        fileName: `${finalFileName}.${fileExtension}`,
        contentType: mimeType,
        size: fileSize,
        url: r2ImageUrl,
        path: fullPath,
        bucket: "r2-bucket", // You might want to get this from env
        createdAt: new Date(),
        metadata: {
          uploadedBy: "r2-storage-service",
          originalFileName: fileName || "unknown",
        },
      };

      return result;
    } catch (error) {
      console.error("Failed to upload media to R2 with metadata:", error);
      return undefined;
    }
  }
}
