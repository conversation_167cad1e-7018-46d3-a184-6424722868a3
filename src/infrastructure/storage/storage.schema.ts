export interface UploadResult {
  id: string;
  fileName: string;
  contentType: string;
  size: number;
  url: string;
  thumbnailUrl?: string;
  path: string;
  bucket: string;
  createdAt: Date;
  metadata?: Record<string, string>;
}

export type FileType = "image" | "audio" | "video" | "document" | "other";

export interface FileInfo {
  id: string;
  name: string;
  type: FileType;
  size: number;
  contentType: string;
  url: string;
  thumbnailUrl?: string;
  path: string;
  createdAt: Date;
  updatedAt?: Date;
  ownerId?: string;
  metadata?: Record<string, string>;
}
