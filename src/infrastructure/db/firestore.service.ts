import { inject, singleton } from "tsyringe";
import type { EnvService } from "../env/env-service.interface";
import { IEnvService } from "../env/env-service.interface";
import { initializeFirestoreClient } from "../../firebase"; // We will modify firebase to export this
import type { DbService, FirestoreClient } from "./db-service.interface";

@singleton()
export class FirestoreDbService implements DbService {
  private firestoreInstance: FirestoreClient;

  constructor(@inject(IEnvService) envService: EnvService) {
    const env = envService.getBindings();
    const { FIREBASE_PROJECT_ID, FIREBASE_PRIVATE_KEY, FIREBASE_CLIENT_EMAIL } =
      env;

    // console.log("FIREBASE_PROJECT_ID", FIREBASE_PROJECT_ID);
    // console.log("FIREBASE_PRIVATE_KEY", FIREBASE_PRIVATE_KEY);
    // console.log("FIREBASE_CLIENT_EMAIL", FIREBASE_CLIENT_EMAIL);

    if (
      !FIREBASE_PROJECT_ID ||
      !FIREBASE_PRIVATE_KEY ||
      !FIREBASE_CLIENT_EMAIL
    ) {
      throw new Error(
        "Firebase configuration environment variables are not fully set."
      );
    }

    this.firestoreInstance = initializeFirestoreClient({
      projectId: FIREBASE_PROJECT_ID,
      privateKey: FIREBASE_PRIVATE_KEY,
      clientEmail: FIREBASE_CLIENT_EMAIL,
    });
  }

  getFirestoreInstance(): FirestoreClient {
    return this.firestoreInstance;
  }
}
