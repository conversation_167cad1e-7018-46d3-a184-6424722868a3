import type { InjectionToken } from "tsyringe";

export const ICacheService: InjectionToken<CacheService> = Symbol("ICacheService");

/**
 * Abstract class representing the contract for a cache service.
 * It defines methods for interacting with a cache system.
 */
export abstract class CacheService {
  /**
   * Retrieves a value from the cache by key
   * @param key The key to look up in the cache
   * @returns The cached value or undefined if not found or expired
   */
  abstract get<T>(key: string): Promise<T | undefined>;

  /**
   * Stores a value in the cache with an optional TTL
   * @param key The key to store the value under
   * @param value The value to store
   * @param ttl Optional time-to-live in milliseconds
   */
  abstract set<T>(key: string, value: T, ttl?: number): Promise<void>;

  /**
   * Deletes a value from the cache by key
   * @param key The key to delete
   */
  abstract del(key: string): Promise<void>;

  /**
   * Checks if a key exists in the cache and is not expired
   * @param key The key to check
   * @returns True if the key exists and is not expired, false otherwise
   */
  abstract has(key: string): Promise<boolean>;

  /**
   * Clears all entries from the cache
   */
  abstract clear(): Promise<void>;
}
