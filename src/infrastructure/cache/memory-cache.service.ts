import { singleton } from "tsyringe";
import { CacheService } from "./cache-service.interface";

interface CacheEntry<T> {
  value: T;
  expiry?: number;
}

/**
 * In-memory implementation of the cache service.
 * Uses a Map to store cache entries in memory.
 */
@singleton()
export class MemoryCacheService implements CacheService {
  private cache = new Map<string, CacheEntry<unknown>>();

  /**
   * Retrieves a value from the in-memory cache by key
   * @param key The key to look up in the cache
   * @returns The cached value or undefined if not found or expired
   */
  public async get<T>(key: string): Promise<T | undefined> {
    const entry = this.cache.get(key);
    if (entry) {
      if (entry.expiry && entry.expiry < Date.now()) {
        this.cache.delete(key);
        return undefined;
      }
      return entry.value as T;
    }
    return undefined;
  }

  /**
   * Stores a value in the in-memory cache with an optional TTL
   * @param key The key to store the value under
   * @param value The value to store
   * @param ttl Optional time-to-live in milliseconds
   */
  public async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    const expiry = ttl ? Date.now() + ttl : undefined;
    this.cache.set(key, { value, expiry });
  }

  /**
   * Deletes a value from the in-memory cache by key
   * @param key The key to delete
   */
  public async del(key: string): Promise<void> {
    this.cache.delete(key);
  }

  /**
   * Checks if a key exists in the in-memory cache and is not expired
   * @param key The key to check
   * @returns True if the key exists and is not expired, false otherwise
   */
  public async has(key: string): Promise<boolean> {
    const entry = this.cache.get(key);
    if (entry) {
      if (entry.expiry && entry.expiry < Date.now()) {
        this.cache.delete(key);
        return false;
      }
      return true;
    }
    return false;
  }

  /**
   * Clears all entries from the in-memory cache
   */
  public async clear(): Promise<void> {
    this.cache.clear();
  }
}
