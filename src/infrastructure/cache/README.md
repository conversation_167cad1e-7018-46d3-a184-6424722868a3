# Cache Services

This directory contains the cache service implementations for the application. The cache services provide a unified interface for caching data, with different backend implementations.

## Architecture

The cache services follow a clean architecture pattern with interface segregation:

- `cache-service.interface.ts` - Defines the common interface for all cache implementations
- `memory-cache.service.ts` - In-memory implementation using a Map
- `kv-cache.service.ts` - Cloudflare KV implementation for distributed caching

## Usage

### Dependency Injection

The cache services are registered in the dependency injection container in `src/container.setup.ts`. By default, the `MemoryCacheService` is registered as the implementation for the `ICacheService` token.

```typescript
// Register cache services
// Use MemoryCacheService as the default implementation
container.registerSingleton(ICacheService, MemoryCacheService);
// Register KVCacheService with a specific token if needed
container.registerSingleton("KVCacheService", KVCacheService);
```

### Using the Cache Service

To use the cache service in your code, inject it using the `ICacheService` token:

```typescript
import { inject } from "tsyringe";
import { ICacheService, CacheService } from "../infrastructure/cache/cache-service.interface";

@singleton()
export class YourService {
  constructor(@inject(ICacheService) private cacheService: CacheService) {}

  async someMethod() {
    // Get a value from the cache
    const cachedValue = await this.cacheService.get<YourType>("your-key");
    
    if (cachedValue) {
      return cachedValue;
    }
    
    // If not in cache, compute the value
    const value = await computeExpensiveValue();
    
    // Store in cache with a TTL of 5 minutes
    await this.cacheService.set("your-key", value, 5 * 60 * 1000);
    
    return value;
  }
}
```

### Using the KV Cache Service Directly

If you need to use the KV cache service specifically, you can inject it using the `"KVCacheService"` token:

```typescript
import { inject } from "tsyringe";
import { KVCacheService } from "../infrastructure/cache/kv-cache.service";

@singleton()
export class YourService {
  constructor(@inject("KVCacheService") private kvCache: KVCacheService) {}

  async someMethod() {
    // Use KV cache directly
    const value = await this.kvCache.get<YourType>("your-key");
    // ...
  }
}
```

## Configuration

### Memory Cache

The memory cache service requires no configuration. It stores data in memory using a Map.

### KV Cache

The KV cache service requires a Cloudflare KV namespace to be configured in `wrangler.jsonc`:

```json
"kv_namespaces": [
  {
    "binding": "KV",
    "id": "your-kv-namespace-id",
    "preview_id": "your-preview-kv-namespace-id"
  }
]
```

And the KV namespace needs to be added to the `CloudflareBindings` interface in `src/types/index.ts`:

```typescript
export interface CloudflareBindings {
  // ...
  KV?: KVNamespace;
  // ...
}
```

## Differences Between Implementations

### Memory Cache

- Fast access (in-memory)
- Data is not shared between workers/instances
- Data is lost when the worker restarts
- Good for development and testing

### KV Cache

- Slightly slower access (network call)
- Data is shared between all workers/instances
- Data persists across worker restarts
- Good for production and distributed environments
- Supports larger data sets
