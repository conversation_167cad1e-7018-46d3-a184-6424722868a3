import { inject, singleton } from "tsyringe";
import { CacheService } from "./cache-service.interface";
import { IEnvService, type EnvService } from "../env/env-service.interface";
import type { CloudflareBindings } from "../../types";

/**
 * Cloudflare KV implementation of the cache service.
 * Uses Cloudflare KV namespace for distributed caching.
 */
@singleton()
export class KVCacheService implements CacheService {
  private env: CloudflareBindings;

  constructor(@inject(IEnvService) envService: EnvService) {
    this.env = envService.getBindings();
    // KV namespace is required, not optional
    if (!this.env.KV) {
      throw new Error(
        "CRITICAL ERROR in KVCacheService constructor: Injected env is missing KV namespace!"
      );
    }
  }

  /**
   * Retrieves a value from the KV cache by key
   * @param key The key to look up in the cache
   * @returns The cached value or undefined if not found or expired
   */
  public async get<T>(key: string): Promise<T | undefined> {
    try {
      const value = await this.env.KV.get<T>(key, { type: "json" });
      return value === null ? undefined : value;
    } catch (error) {
      console.error(`Error getting value from KV cache: ${error}`);
      return undefined;
    }
  }

  /**
   * Stores a value in the KV cache with an optional TTL
   * @param key The key to store the value under
   * @param value The value to store
   * @param ttl Optional time-to-live in milliseconds
   */
  public async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    try {
      const options: KVNamespacePutOptions = {};
      if (ttl) {
        options.expirationTtl = Math.ceil(ttl / 1000); // Convert ms to seconds
      }

      await this.env.KV.put(key, JSON.stringify(value), options);
    } catch (error) {
      console.error(`Error setting value in KV cache: ${error}`);
    }
  }

  /**
   * Deletes a value from the KV cache by key
   * @param key The key to delete
   */
  public async del(key: string): Promise<void> {
    try {
      await this.env.KV.delete(key);
    } catch (error) {
      console.error(`Error deleting value from KV cache: ${error}`);
    }
  }

  /**
   * Checks if a key exists in the KV cache
   * @param key The key to check
   * @returns True if the key exists, false otherwise
   */
  public async has(key: string): Promise<boolean> {
    try {
      const value = await this.env.KV.get(key, { type: "text" });
      return value !== null;
    } catch (error) {
      console.error(`Error checking key in KV cache: ${error}`);
      return false;
    }
  }

  /**
   * Clears all entries from the KV cache with a specific prefix
   * Note: Cloudflare KV doesn't support clearing the entire namespace,
   * so this method deletes keys with a specific prefix (default: all keys)
   * @param prefix Optional prefix to limit deletion to keys with this prefix
   */
  public async clear(prefix: string = ""): Promise<void> {
    try {
      // List all keys with the given prefix
      const keys = await this.env.KV.list({ prefix });

      // Delete each key
      const deletePromises = keys.keys.map((key: { name: string }) =>
        this.env.KV.delete(key.name)
      );
      await Promise.all(deletePromises);
    } catch (error) {
      console.error(`Error clearing KV cache: ${error}`);
    }
  }
}
