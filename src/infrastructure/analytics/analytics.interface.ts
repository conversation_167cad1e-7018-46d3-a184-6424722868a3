import type { InjectionToken } from "tsyringe";

export const IAnalyticsService: InjectionToken<AnalyticsService> = Symbol("IAnalyticsService");

/**
 * Daily statistics for API endpoints
 */
export interface DailyStats {
  date: string; // YYYY-MM-DD format
  totalCalls: number;
  successCalls: number;
  failureCalls: number;
  lastUpdated: string; // ISO timestamp
  feature?: string; // Optional feature identifier
  endpoint?: string; // Optional endpoint identifier
}

/**
 * Analytics service interface for tracking API usage statistics
 * Enhanced to support multiple features and more detailed tracking
 */
export abstract class AnalyticsService {
  /**
   * Record an API call attempt
   * @param endpoint The endpoint name (e.g., 'generate-image-from-usecase')
   * @param feature Optional feature name (e.g., 'image-generation', 'video-generation')
   * @param userId Optional user identifier for user-specific analytics
   * @param date Optional date string (YYYY-MM-DD), defaults to today
   */
  abstract recordCall(
    endpoint: string, 
    feature?: string, 
    userId?: string, 
    date?: string
  ): Promise<void>;

  /**
   * Record a successful API call
   * @param endpoint The endpoint name
   * @param feature Optional feature name
   * @param userId Optional user identifier
   * @param date Optional date string (YYYY-MM-DD), defaults to today
   */
  abstract recordSuccess(
    endpoint: string, 
    feature?: string, 
    userId?: string, 
    date?: string
  ): Promise<void>;

  /**
   * Record a failed API call
   * @param endpoint The endpoint name
   * @param feature Optional feature name
   * @param userId Optional user identifier
   * @param date Optional date string (YYYY-MM-DD), defaults to today
   */
  abstract recordFailure(
    endpoint: string, 
    feature?: string, 
    userId?: string, 
    date?: string
  ): Promise<void>;

  /**
   * Get daily statistics for an endpoint
   * @param endpoint The endpoint name
   * @param feature Optional feature name for filtering
   * @param date Optional date string (YYYY-MM-DD), defaults to today
   * @returns Daily statistics or null if no data exists
   */
  abstract getDailyStats(
    endpoint: string, 
    feature?: string, 
    date?: string
  ): Promise<DailyStats | null>;

  /**
   * Get statistics for multiple days
   * @param endpoint The endpoint name
   * @param startDate Start date (YYYY-MM-DD)
   * @param endDate End date (YYYY-MM-DD)
   * @param feature Optional feature name for filtering
   * @returns Array of daily statistics
   */
  abstract getStatsRange(
    endpoint: string, 
    startDate: string, 
    endDate: string, 
    feature?: string
  ): Promise<DailyStats[]>;

  /**
   * Get aggregated statistics for a feature across all endpoints
   * @param feature The feature name
   * @param date Optional date string (YYYY-MM-DD), defaults to today
   * @returns Aggregated daily statistics or null if no data exists
   */
  abstract getFeatureStats(
    feature: string, 
    date?: string
  ): Promise<DailyStats | null>;

  /**
   * Get aggregated statistics for a feature across a date range
   * @param feature The feature name
   * @param startDate Start date (YYYY-MM-DD)
   * @param endDate End date (YYYY-MM-DD)
   * @returns Array of aggregated daily statistics
   */
  abstract getFeatureStatsRange(
    feature: string, 
    startDate: string, 
    endDate: string
  ): Promise<DailyStats[]>;

  /**
   * Clear statistics for an endpoint and date
   * @param endpoint The endpoint name
   * @param feature Optional feature name
   * @param date Optional date string (YYYY-MM-DD), defaults to today
   */
  abstract clearStats(
    endpoint: string, 
    feature?: string, 
    date?: string
  ): Promise<void>;
}
