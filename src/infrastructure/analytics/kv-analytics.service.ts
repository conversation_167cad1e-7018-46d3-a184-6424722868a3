import { inject, singleton } from "tsyringe";
import { AnalyticsService, type DailyStats } from "./analytics.interface";
import {
  ICacheService,
  type CacheService,
} from "../cache/cache-service.interface";

/**
 * KV-based implementation of the analytics service
 * Uses Cloudflare KV for persistent storage of API usage statistics
 * Enhanced to support multiple features and more detailed tracking
 */
@singleton()
export class KVAnalyticsService implements AnalyticsService {
  private readonly CACHE_PREFIX = "analytics";
  private readonly FEATURE_PREFIX = "feature-analytics";
  private readonly CACHE_TTL = 90 * 24 * 60 * 60 * 1000; // 90 days in milliseconds

  constructor(@inject(ICacheService) private cacheService: CacheService) {}

  /**
   * Generate cache key for endpoint statistics
   */
  private getCacheKey(endpoint: string, date: string, feature?: string): string {
    if (feature) {
      return `${this.CACHE_PREFIX}:${feature}:${endpoint}:${date}`;
    }
    return `${this.CACHE_PREFIX}:${endpoint}:${date}`;
  }

  /**
   * Generate cache key for feature-level statistics
   */
  private getFeatureCacheKey(feature: string, date: string): string {
    return `${this.FEATURE_PREFIX}:${feature}:${date}`;
  }

  /**
   * Get current date in YYYY-MM-DD format
   */
  private getCurrentDate(): string {
    return new Date().toISOString().split("T")[0];
  }

  /**
   * Get or create daily stats for an endpoint
   */
  private async getOrCreateDailyStats(
    endpoint: string,
    date: string,
    feature?: string
  ): Promise<DailyStats> {
    const cacheKey = this.getCacheKey(endpoint, date, feature);
    const existingStats = await this.cacheService.get<DailyStats>(cacheKey);

    if (existingStats) {
      return existingStats;
    }

    // Create new stats if none exist
    const newStats: DailyStats = {
      date,
      totalCalls: 0,
      successCalls: 0,
      failureCalls: 0,
      lastUpdated: new Date().toISOString(),
      feature,
      endpoint,
    };

    await this.cacheService.set(cacheKey, newStats, this.CACHE_TTL);
    return newStats;
  }

  /**
   * Update daily stats in cache
   */
  private async updateDailyStats(
    endpoint: string,
    date: string,
    updater: (stats: DailyStats) => void,
    feature?: string
  ): Promise<void> {
    const stats = await this.getOrCreateDailyStats(endpoint, date, feature);
    updater(stats);
    stats.lastUpdated = new Date().toISOString();

    const cacheKey = this.getCacheKey(endpoint, date, feature);
    await this.cacheService.set(cacheKey, stats, this.CACHE_TTL);

    // Also update feature-level aggregated stats if feature is provided
    if (feature) {
      await this.updateFeatureStats(feature, date, updater);
    }
  }

  /**
   * Update feature-level aggregated statistics
   */
  private async updateFeatureStats(
    feature: string,
    date: string,
    updater: (stats: DailyStats) => void
  ): Promise<void> {
    const cacheKey = this.getFeatureCacheKey(feature, date);
    let featureStats = await this.cacheService.get<DailyStats>(cacheKey);

    if (!featureStats) {
      featureStats = {
        date,
        totalCalls: 0,
        successCalls: 0,
        failureCalls: 0,
        lastUpdated: new Date().toISOString(),
        feature,
      };
    }

    updater(featureStats);
    featureStats.lastUpdated = new Date().toISOString();

    await this.cacheService.set(cacheKey, featureStats, this.CACHE_TTL);
  }

  /**
   * Record an API call attempt
   */
  async recordCall(
    endpoint: string,
    feature?: string,
    userId?: string,
    date?: string
  ): Promise<void> {
    const targetDate = date || this.getCurrentDate();
    await this.updateDailyStats(
      endpoint,
      targetDate,
      (stats) => {
        stats.totalCalls += 1;
      },
      feature
    );
  }

  /**
   * Record a successful API call
   */
  async recordSuccess(
    endpoint: string,
    feature?: string,
    userId?: string,
    date?: string
  ): Promise<void> {
    const targetDate = date || this.getCurrentDate();
    await this.updateDailyStats(
      endpoint,
      targetDate,
      (stats) => {
        stats.successCalls += 1;
      },
      feature
    );
  }

  /**
   * Record a failed API call
   */
  async recordFailure(
    endpoint: string,
    feature?: string,
    userId?: string,
    date?: string
  ): Promise<void> {
    const targetDate = date || this.getCurrentDate();
    await this.updateDailyStats(
      endpoint,
      targetDate,
      (stats) => {
        stats.failureCalls += 1;
      },
      feature
    );
  }

  /**
   * Get daily statistics for an endpoint
   */
  async getDailyStats(
    endpoint: string,
    feature?: string,
    date?: string
  ): Promise<DailyStats | null> {
    const targetDate = date || this.getCurrentDate();
    const cacheKey = this.getCacheKey(endpoint, targetDate, feature);
    return (await this.cacheService.get<DailyStats>(cacheKey)) || null;
  }

  /**
   * Get statistics for multiple days
   */
  async getStatsRange(
    endpoint: string,
    startDate: string,
    endDate: string,
    feature?: string
  ): Promise<DailyStats[]> {
    const stats: DailyStats[] = [];
    const start = new Date(startDate);
    const end = new Date(endDate);

    for (
      let date = new Date(start);
      date <= end;
      date.setDate(date.getDate() + 1)
    ) {
      const dateStr = date.toISOString().split("T")[0];
      const dailyStats = await this.getDailyStats(endpoint, feature, dateStr);
      if (dailyStats) {
        stats.push(dailyStats);
      }
    }

    return stats;
  }

  /**
   * Get aggregated statistics for a feature across all endpoints
   */
  async getFeatureStats(
    feature: string,
    date?: string
  ): Promise<DailyStats | null> {
    const targetDate = date || this.getCurrentDate();
    const cacheKey = this.getFeatureCacheKey(feature, targetDate);
    return (await this.cacheService.get<DailyStats>(cacheKey)) || null;
  }

  /**
   * Get aggregated statistics for a feature across a date range
   */
  async getFeatureStatsRange(
    feature: string,
    startDate: string,
    endDate: string
  ): Promise<DailyStats[]> {
    const stats: DailyStats[] = [];
    const start = new Date(startDate);
    const end = new Date(endDate);

    for (
      let date = new Date(start);
      date <= end;
      date.setDate(date.getDate() + 1)
    ) {
      const dateStr = date.toISOString().split("T")[0];
      const featureStats = await this.getFeatureStats(feature, dateStr);
      if (featureStats) {
        stats.push(featureStats);
      }
    }

    return stats;
  }

  /**
   * Clear statistics for an endpoint and date
   */
  async clearStats(
    endpoint: string,
    feature?: string,
    date?: string
  ): Promise<void> {
    const targetDate = date || this.getCurrentDate();
    const cacheKey = this.getCacheKey(endpoint, targetDate, feature);
    await this.cacheService.del(cacheKey);

    // Also clear feature-level stats if feature is provided
    if (feature) {
      const featureCacheKey = this.getFeatureCacheKey(feature, targetDate);
      await this.cacheService.del(featureCacheKey);
    }
  }
}
