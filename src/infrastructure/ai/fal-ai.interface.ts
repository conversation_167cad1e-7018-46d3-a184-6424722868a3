import type { InjectionToken } from "tsyringe";

export const IFalAiService: InjectionToken<FalAiService> = Symbol("IFalAiService");

export interface FalAiEditImageRequest {
  prompt: string;
  image_url: string;
}

export interface FalAiEditImageResponse {
  images: Array<{
    url: string;
    width: number;
    height: number;
    content_type: string;
  }>;
  timings: {
    inference: number;
  };
  seed: number;
  has_nsfw_concepts: boolean[];
  prompt: string;
}

export abstract class FalAiService {
  abstract editImage(request: FalAiEditImageRequest): Promise<string | null>;
}
