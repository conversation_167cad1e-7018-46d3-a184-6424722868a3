// import { singleton } from "tsyringe";
// import {
//   GoogleGenerativeAI,
//   type GenerateContentRequest,
//   type GenerationConfig,
//   type Part,
// } from "@google/generative-ai";

// import dotenv from "dotenv";
// dotenv.config();

// const apiKey = process.env.GEMINI_API_KEY;

// // 扩展GenerationConfig类型以包含responseModalities
// interface ExtendedGenerationConfig extends GenerationConfig {
//   responseModalities?: string[];
// }

// @singleton()
// export class GeminiService {
//   private genAI: GoogleGenerativeAI | null = null;

//   constructor() {
//     if (!apiKey) {
//       console.warn(
//         "Warning: GEMINI_API_KEY environment variable is missing. Gemini AI service will be unavailable."
//       );
//     } else {
//       this.genAI = new GoogleGenerativeAI(apiKey);
//     }
//   }

//   async processRequest(request: GenerateContentRequest): Promise<Part[]> {
//     if (!this.genAI) {
//       throw new Error("Gemini AI service unavailable: Missing API key");
//     }

//     const model = this.genAI.getGenerativeModel({
//       model: "gemini-2.0-flash-exp-image-generation",
//       generationConfig: {
//         responseModalities: ["Text", "Image"],
//       } as ExtendedGenerationConfig,
//     });

//     try {
//       const result = await model.generateContent(request);
//       const response = await result.response;
//       const parts = response.candidates?.[0].content.parts;

//       if (!parts || parts.length === 0) {
//         throw new Error(
//           "Image processing failed: No content parts received from the model."
//         );
//       }

//       return parts;
//     } catch (error: any) {
//       console.error("Error during Gemini API call:", error);
//       throw new Error(`Gemini API request failed: ${error.message}`);
//     }
//   }
// }
