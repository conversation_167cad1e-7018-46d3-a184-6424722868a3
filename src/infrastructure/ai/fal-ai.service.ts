import { fal } from "@fal-ai/client";
import { inject, injectable } from "tsyringe";
import { FalAiService as FalAiServiceInterface, type FalAiEditImageRequest, type FalAiEditImageResponse } from "./fal-ai.interface";
import type { EnvService } from "../env/env-service.interface";
import { IEnvService } from "../env/env-service.interface";

@injectable()
export class FalAiService implements FalAiServiceInterface {
  constructor(@inject(IEnvService) private envService: EnvService) {
    const env = this.envService.getBindings();
    fal.config({
      credentials: env.FAL_KEY,
    });
  }

  /**
   * Edits an image using fal-ai's flux-pro/kontext model
   * @param request - The edit request containing prompt and image URL
   * @returns The URL of the edited image, or null if editing failed
   */
  async editImage(request: FalAiEditImageRequest): Promise<string | null> {
    try {
      console.log(`[FalAI] Starting image edit with prompt: "${request.prompt}"`);
      console.log(`[FalAI] Image URL: ${request.image_url}`);

      const result = await fal.subscribe("fal-ai/flux-pro/kontext", {
        input: {
          prompt: request.prompt,
          image_url: request.image_url,
        },
        logs: true,
        onQueueUpdate: (update) => {
          if (update.status === "IN_PROGRESS") {
            update.logs.map((log) => log.message).forEach(console.log);
          }
        },
      });

      console.log(`[FalAI] Request ID: ${result.requestId}`);

      const response = result.data as FalAiEditImageResponse;
      
      if (!response || !response.images || response.images.length === 0) {
        console.error("[FalAI] No images returned from fal-ai");
        return null;
      }

      const imageUrl = response.images[0].url;
      console.log(`[FalAI] Successfully edited image: ${imageUrl}`);
      
      return imageUrl;
    } catch (error) {
      console.error("[FalAI] Error editing image:", error);
      throw new Error(
        `Failed to edit image: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }
}
