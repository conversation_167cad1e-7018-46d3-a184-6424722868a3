import { streamGptRequest } from "../common/302.api";

import { extractImageUrlFromResponse } from "../../lib/utils";
import type {
  ChatCompletionMessage,
  ChatCompletionMessageContent,
} from "../../types";
import { GptService as GptServiceInterface } from "./gpt.interface";
import type { EnvService } from "../env/env-service.interface";
import { IEnvService } from "../env/env-service.interface";
import { inject, injectable } from "tsyringe";

@injectable()
export class GptService implements GptServiceInterface {
  private apiKey: string;
  private baseUrl: string;
  private model: string;

  constructor(@inject(IEnvService) envService: EnvService) {
    const env = envService.getBindings();
    this.apiKey = env.GPT_API_KEY;
    this.baseUrl = env.GPT_API_BASE_URL;
    this.model = env.GPT_API_MODEL;
  }

  /**
   * Base function to generate content using GPT model.
   * This method calls the streamGptRequest and processes the response.
   */
  private async generateBaseContent(
    messages: ChatCompletionMessage[],
    onProgress?: (progress: number) => Promise<void>
  ): Promise<string> {
    const gptResponse = await streamGptRequest(
      messages,
      this.apiKey,
      this.baseUrl,
      this.model,
      0.7, // Default temperature
      null, // maxTokens
      onProgress
    );

    if (
      !gptResponse ||
      !gptResponse.choices ||
      gptResponse.choices.length === 0
    ) {
      throw new Error(
        "GPT response did not contain usable content (no choices)."
      );
    }

    const responseContent = gptResponse.choices[0].message.content || "";

    if (responseContent === "") {
      throw new Error(
        "GPT response did not contain usable content (empty content)."
      );
    }

    return responseContent;
  }

  /**
   * Generates an image based on a text prompt and optional existing image URLs.
   * Returns the URL of the generated image, or null if no URL is found.
   * The caller is responsible for any further processing of the image URL (e.g., downloading, watermarking, uploading to storage).
   */
  async generateImage(
    prompt: string,
    imageUrls?: string[],
    onProgress?: (progress: number) => Promise<void>
  ): Promise<string | null> {
    try {
      const messageContent: ChatCompletionMessageContent[] = [];

      if (imageUrls && imageUrls.length > 0) {
        imageUrls.forEach((url) => {
          messageContent.push({
            type: "image_url",
            image_url: { url },
          });
        });
      }

      messageContent.push({
        type: "text",
        text: prompt,
      });

      const messages: ChatCompletionMessage[] = [
        {
          role: "user",
          content: messageContent,
        },
      ];

      const responseContent = await this.generateBaseContent(
        messages,
        onProgress
      );
      const imageUrl = extractImageUrlFromResponse(responseContent);

      if (!imageUrl) {
        return null;
      }
      return imageUrl;
    } catch (error) {
      console.error("GptService.generateImage failed:", error);
      throw new Error(
        `Failed to generate image: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }
}
