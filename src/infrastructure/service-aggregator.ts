import { singleton, inject } from "tsyringe";

// Infrastructure Services
import { IDbService, type DbService } from "./db/db-service.interface";
import { IEnvService, type EnvService } from "./env/env-service.interface";
import {
  ICacheService,
  type CacheService,
} from "./cache/cache-service.interface";
import {
  IStorageService,
  type StorageService,
} from "./storage/storage-service.interface";
import { IQuotaService, type QuotaService } from "./quota/quota.interface";
import {
  IAnalyticsService,
  type AnalyticsService,
} from "./analytics/analytics.interface";

// Business Services
import { NotionService } from "../features/notion/notion.service";
import { IGptService, type GptService } from "./ai/gpt.interface";

/**
 * TSyringe 最佳实践 - 服务聚合模式
 *
 * 这种模式将相关的服务组合在一起，减少单个服务的构造函数参数数量
 * 同时保持类型安全和依赖注入的优势
 */

/**
 * 基础设施服务聚合器
 * 包含所有基础设施相关的服务
 */
@singleton()
export class InfrastructureServices {
  constructor(
    @inject(IDbService) public readonly db: DbService,
    @inject(IEnvService) public readonly env: EnvService,
    @inject(ICacheService) public readonly cache: CacheService,
    @inject(IStorageService) public readonly storage: StorageService,
    @inject(IQuotaService) public readonly quota: QuotaService,
    @inject(IAnalyticsService) public readonly analytics: AnalyticsService
  ) {}

  /**
   * 获取 Firestore 实例的便捷方法
   */
  get firestore() {
    return this.db.getFirestoreInstance();
  }

  /**
   * 获取环境变量的便捷方法
   */
  get bindings() {
    return this.env.getBindings();
  }
}

/**
 * 业务服务聚合器
 * 包含所有业务逻辑相关的服务
 */
@singleton()
export class BusinessServices {
  constructor(
    @inject(NotionService) public readonly notion: NotionService,
    @inject(IGptService) public readonly gpt: GptService
  ) {}
}

/**
 * 应用服务聚合器
 * 组合基础设施和业务服务，提供完整的应用上下文
 */
@singleton()
export class ApplicationServices {
  constructor(
    @inject(InfrastructureServices)
    public readonly infrastructure: InfrastructureServices,
    @inject(BusinessServices) public readonly business: BusinessServices
  ) {}

  /**
   * 便捷访问方法
   */
  get db() {
    return this.infrastructure.db;
  }
  get env() {
    return this.infrastructure.env;
  }
  get cache() {
    return this.infrastructure.cache;
  }
  get storage() {
    return this.infrastructure.storage;
  }
  get quota() {
    return this.infrastructure.quota;
  }
  get analytics() {
    return this.infrastructure.analytics;
  }
  get firestore() {
    return this.infrastructure.firestore;
  }
  get bindings() {
    return this.infrastructure.bindings;
  }

  get notion() {
    return this.business.notion;
  }
  get gpt() {
    return this.business.gpt;
  }
}

/**
 * 使用示例：
 *
 * @singleton()
 * export class SomeComplexService {
 *   constructor(
 *     @inject(ApplicationServices) private app: ApplicationServices
 *   ) {}
 *
 *   async doSomething() {
 *     const data = await this.app.notion.getPromptList();
 *     await this.app.cache.set('key', data);
 *     return this.app.firestore.collection('test').add(data);
 *   }
 * }
 */
