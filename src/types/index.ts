import type { DependencyContainer } from "tsyringe";

/**
 * Global environment variables type definition
 */
export interface CloudflareBindings {
  // QUEUE: Queue;
  // GEMINI_API_KEY: string;
  GPT_API_KEY: string;
  GPT_API_BASE_URL: string;
  GPT_API_MODEL: string;
  FAL_KEY: string;
  VIDU_API_KEY: string;
  R2_PUBLIC_URL: string;
  KV: KVNamespace;
  R2Bucket: R2Bucket;
  // DB: D1Database;
  IMAGE_GENERATION_WORKFLOW: Workflow;
  VIDEO_GENERATION_WORKFLOW: Workflow;
  IMAGES: ImagesBinding;
  NOTION_AUTH_TOKEN: string;
  NOTION_DATABASE_ID: string;
  NOTION_VIDEO_USECASE_DATABASE_ID: string;
  FIREBASE_PROJECT_ID: string;
  FIREBASE_PRIVATE_KEY: string;
  FIREBASE_CLIENT_EMAIL: string;
  ENVIRONMENT: "test" | "prod" | "localhost";
  CF_VERSION_METADATA?: {
    id: string;
    tag?: string;
    timestamp?: number;
  };
  SENTRY_DSN: string;
}

export type HonoEnv = {
  Bindings: CloudflareBindings;
  Variables: {
    container: DependencyContainer;
  };
};

/**
 * ChatGPT message content definition
 */
export interface ChatCompletionMessageContent {
  type: "text" | "image_url";
  text?: string;
  image_url?: {
    url: string;
  };
}

/**
 * ChatGPT message definition
 */
export interface ChatCompletionMessage {
  role: "system" | "user" | "assistant";
  content: ChatCompletionMessageContent[];
}

/**
 * ChatGPT API response
 */
export interface ChatCompletionResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: {
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }[];
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
    prompt_tokens_details?: {
      text_tokens: number;
      image_tokens: number;
    };
    completion_tokens_details?: {
      image_tokens: number;
      content_tokens: number;
    };
  };
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    message: string;
    status: number;
  };
}
