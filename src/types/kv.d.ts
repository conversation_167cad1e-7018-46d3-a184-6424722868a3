/**
 * Type definitions for Cloudflare KV
 */

interface KVNamespace {
  get(key: string, options?: KVNamespaceGetOptions<undefined>): Promise<string | null>;
  get(key: string, options: KVNamespaceGetOptions<"text">): Promise<string | null>;
  get<T>(key: string, options: KVNamespaceGetOptions<"json">): Promise<T | null>;
  get(key: string, options: KVNamespaceGetOptions<"arrayBuffer">): Promise<ArrayBuffer | null>;
  get(key: string, options: KVNamespaceGetOptions<"stream">): Promise<ReadableStream | null>;
  
  put(
    key: string,
    value: string | ReadableStream | ArrayBuffer | FormData,
    options?: KVNamespacePutOptions
  ): Promise<void>;
  
  delete(key: string): Promise<void>;
  
  list(options?: KVNamespaceListOptions): Promise<KVNamespaceListResult>;
}

interface KVNamespaceGetOptions<Type> {
  type?: Type;
  cacheTtl?: number;
}

interface KVNamespacePutOptions {
  expiration?: number;
  expirationTtl?: number;
  metadata?: Record<string, unknown>;
}

interface KVNamespaceListOptions {
  prefix?: string;
  limit?: number;
  cursor?: string;
}

interface KVNamespaceListKey {
  name: string;
  expiration?: number;
  metadata?: Record<string, unknown>;
}

interface KVNamespaceListResult {
  keys: KVNamespaceListKey[];
  list_complete: boolean;
  cursor?: string;
}
