import { z } from "zod";

/**
 * Standard error response schema used across all API endpoints
 */
export const ErrorResponseSchema = z.object({
  error: z.string().describe("Error message"),
  code: z.string().optional().describe("Error code for programmatic handling"),
  details: z.string().optional().describe("Additional error details"),
});

export type ErrorResponse = z.infer<typeof ErrorResponseSchema>;

/**
 * Validation error schema for 400 responses
 */
export const ValidationErrorSchema = z.object({
  error: z.string().describe("Error message"),
  code: z.literal("VALIDATION_ERROR").describe("Error code"),
  details: z.string().optional().describe("Validation error details"),
  issues: z
    .array(
      z.object({
        code: z.string(),
        path: z.array(z.union([z.string(), z.number()])),
        message: z.string(),
      })
    )
    .optional()
    .describe("Detailed validation issues"),
});

export type ValidationError = z.infer<typeof ValidationErrorSchema>;
